package com.coocaa.meht.module.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 请求日志详情
 *
 * <AUTHOR>
 * @Date 2023-12-06 20:03
 */
@Data
@Accessors(chain = true)
@TableName("sys_request_log_desc")
public class SysRequestLogDescEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    private String requestParam;
    private String errorLog;
}
