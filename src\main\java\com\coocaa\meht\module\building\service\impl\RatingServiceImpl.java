package com.coocaa.meht.module.building.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.PermissionDTO;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.common.constants.DictCodeConstants;
import com.coocaa.meht.common.constants.RatingConstants;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.common.handler.PermissionHandler;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.converter.CodeNameHelper;
import com.coocaa.meht.converter.ConverterFactory;
import com.coocaa.meht.module.approve.dto.ScreenApproveRecordDTO;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.module.approve.facade.ApprovalCenterFacade;
import com.coocaa.meht.module.approve.service.ApprovalQueryService;
import com.coocaa.meht.module.building.convert.BuildingDetailsConvert;
import com.coocaa.meht.module.building.convert.BuildingGeneConvert;
import com.coocaa.meht.module.building.convert.BuildingScreenConvert;
import com.coocaa.meht.module.building.dto.RatingDraftDTO;
import com.coocaa.meht.module.building.dto.RatingPageDTO;
import com.coocaa.meht.module.building.entity.BuildingScreenEntity;
import com.coocaa.meht.module.building.entity.CompleteRatingEntity;
import com.coocaa.meht.module.building.entity.RatingAttachmentEntity;
import com.coocaa.meht.module.building.handler.AiRatingHandler;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.building.service.RatingAttachmentService;
import com.coocaa.meht.module.building.service.RatingService;
import com.coocaa.meht.module.building.vo.BuildingDetailsVO;
import com.coocaa.meht.module.building.vo.BuildingScreenVO;
import com.coocaa.meht.module.building.vo.RatingPageVO;
import com.coocaa.meht.module.building.vo.RatingVO;
import com.coocaa.meht.module.building.vo.SysFileVO;
import com.coocaa.meht.module.sys.dto.SysUserDto;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dao.BuildingRatingDao;
import com.coocaa.meht.module.web.dto.BigScreenCalculateDTO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.dto.convert.BuildingRatingConvert;
import com.coocaa.meht.module.web.dto.req.BuildingLevelReq;
import com.coocaa.meht.module.web.dto.tctask.CalculateResultDTO;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BuildingSnapshotEntity;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.CityCoefficientEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.enums.BusinessTypeEnum;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingGeneService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BuildingSnapshotService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.CityCoefficientService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.service.IBuildingStatusChangeLogService;
import com.coocaa.meht.module.web.service.OperateLogService;
import com.coocaa.meht.module.web.vo.BuildingGeneVO;
import com.coocaa.meht.module.web.vo.common.UserVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.dto.CityVO;
import com.coocaa.meht.rpc.dto.UserBatchMessageParam;
import com.coocaa.meht.rpc.dto.UserMessageContentParam;
import com.coocaa.meht.rpc.vo.InnerInstanceTaskVO;
import com.coocaa.meht.utils.AddressUtil;
import com.coocaa.meht.utils.CodeGenerator;
import com.coocaa.meht.utils.H5MapUtil;
import com.coocaa.meht.utils.LargeScreenCalculator;
import com.coocaa.meht.utils.MessageSendUtil;
import com.coocaa.meht.utils.RsaExample;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 楼宇评级业务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class RatingServiceImpl implements RatingService {

    private final LargeScreenProperties largeScreenProperties;

    private final LargeScreenCalculator largeScreenCalculator;

    private final PermissionHandler permissionHandler;

    private final RsaExample rsaExample;

    private final ConverterFactory converterFactory;

    private final SysUserService sysUserService;

    private final BuildingRatingService buildingRatingService;

    private final BuildingSnapshotService buildingSnapshotService;

    private final BuildingDetailsService buildingDetailsService;

    private final BuildingScreenService buildingScreenService;

    private final BuildingGeneService buildingGeneService;

    private final IBuildingMetaService buildingMetaService;

    private final OperateLogService operateLogService;

    private final RatingAttachmentService ratingAttachmentService;

    private final BuildingRatingDao buildingRatingDao;

    private final CompleteRatingService completeRatingService;

    private final FeignCmsRpc feignCmsRpc;

    private final FeignAuthorityRpc feignAuthorityRpc;

    private final BuildingRatingConvert ratingConvert;

    private final SysFileService sysFileService;

    private final CityCoefficientService cityCoefficientService;

    private final H5MapUtil h5MapUtil;

    private final ApprovalCenterFacade approvalCenterFacade;

    private final CodeGenerator codeGenerator;

    private final BusinessOpportunityService businessOpportunityService;

    private final CodeNameHelper codeNameHelper;

    private final ApprovalQueryService approvalQueryService;

    private final MessageSendUtil messageSendUtil;

    private final IBuildingStatusChangeLogService changeLogService;

    private final AiRatingHandler aiRatingHandler;


    private static final String EDIT_MSG = "「%s」的「%s」评级信息更新，操作人：「%s」";

    @Override
    public PageResult<RatingPageVO> page(RatingPageDTO param) {
        // 数据权限
        PermissionDTO cmsPermission = permissionHandler.getCmsPermission(param.getWnos(), null);
        if (Objects.isNull(cmsPermission.getCities()) || Objects.isNull(cmsPermission.getUserCodes())) {
            // 没有符合权限的条件，直接返回空数据
            return new PageResult<>(Collections.emptyList(), 0);
        }

        // 时间处理
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        String startTime = null;
        String endTime = null;
        if (Objects.nonNull(param.getStart())) {
            startTime = param.getStart().atStartOfDay().format(dateTimeFormatter);
        }
        if (Objects.nonNull(param.getEnd())) {
            endTime = param.getEnd().atTime(LocalTime.MAX).format(dateTimeFormatter);
        }

        Page<RatingPageVO> page = buildingRatingDao.ratingPage(new Page<>(param.getPage(), param.getLimit()),
                param.getBuildingName(), param.getStatuses(), startTime, endTime,
                cmsPermission.getCities(), cmsPermission.getUserCodes(), UserThreadLocal.getUser().getWno());

        List<String> userCodes = new ArrayList<>(page.getRecords().size());
        page.getRecords().forEach(item -> {
            // 地址
            item.setMapAddress(rsaExample.decryptByPrivate(item.getMapAddress()));
            // 设置状态名称
            item.setStatusName(BuildingRatingEntity.Status.getNameByValue(item.getStatus()));
            // 设置编辑权限
            item.setIsSelfApply(StrUtil.isNotBlank(item.getSubmitUser())
                    && item.getSubmitUser().equals(UserThreadLocal.getUser().getWno()));
            if (BuildingRatingEntity.Status.DRAFT.getValue() == item.getStatus()) {
                // 草稿置空提交时间
                item.setSubmitTime(null);
            }
            userCodes.add(item.getCreateBy());
        });

        // 设置创建人名称
        List<UserVO> userVOS = RpcUtils.unBox(feignAuthorityRpc.listUserByWnos(userCodes));
        Map<String, String> codeMapping = userVOS.stream()
                .collect(Collectors.toMap(UserVO::getWno, UserVO::getName, (existing, replacement) -> existing));
        feignAuthorityRpc.listUserByWnos(userCodes);
        page.getRecords().forEach(item -> {
            String name = codeMapping.get(item.getCreateBy());
            item.setCreateByName(StrUtil.isNotBlank(name) ? name : item.getCreateBy());
        });

        converterFactory.convert(page.getRecords());

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuildingRatingEntity edit(RatingApplyDto param) {
        if (StrUtil.isBlank(param.getBuildingNo())) {
            throw new ServerException("楼宇编号不能为空");
        }

        // 查询楼宇评级数据
        BuildingRatingEntity existRatingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, param.getBuildingNo())
                .eq(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.AUDITED.getValue())
                .last("limit 1")
                .one();
        if (Objects.isNull(existRatingEntity)) {
            throw new ServerException("楼宇评级数据不存在");
        }

        // 完善评级中检查
        checkComplete(param.getBuildingNo());

        // 大屏标识
        boolean isLargeScreen = isLargeScreen(param);

        // 非写字楼大屏检查
        if (BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue() != param.getBuildingType()
                && isLargeScreen) {
            throw new ServerException("非写字楼，不能选择大屏");
        }

        // 写字楼小屏，已大屏评级，不能编辑评级
        if (BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue() == param.getBuildingType()
                && !isLargeScreen && BooleFlagEnum.YES.getCode().equals(existRatingEntity.getLargeScreenRatingFlag())) {
            throw new ServerException("已大屏评级，不能改为小屏");
        }

        // 写字楼大屏，未大屏评级，不能编辑评级
        if (BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue() == param.getBuildingType()
                && isLargeScreen && BooleFlagEnum.NO.getCode().equals(existRatingEntity.getLargeScreenRatingFlag())) {
            throw new ServerException("未完善大屏评级，不能编辑大屏评级数据");
        }

        // 保存数据快照
        buildingSnapshotService.save(existRatingEntity.getBuildingNo(), BuildingSnapshotEntity.Type.RATING_EDIT);

        // 创建楼宇详情
        BuildingDetailsEntity detailEntity = buildingDetailsService.createDetailsEntity(param);

        // 人工评级，填充评级数据到detailsEntity
        CalculateResultDTO manualRatingResult = buildingRatingService.manualRating(param, detailEntity);

        // 创建楼宇评级
        BuildingRatingEntity ratingEntity = buildingRatingService.createRatingEntity(param, true);
        // 填充评级数据
        fillRatingData(ratingEntity, manualRatingResult);
        // 填充不可更改字段
        fillImmutableField(ratingEntity, existRatingEntity);
        // 小屏打标
        ratingEntity.setSmallScreenRatingFlag(BooleFlagEnum.YES.getCode());
        // 大屏打标
        ratingEntity.setLargeScreenRatingFlag(isLargeScreen ? BooleFlagEnum.YES.getCode() : BooleFlagEnum.NO.getCode());
        // 设置复核等级
        setReviewLevelIfNeed(ratingEntity);
        // 更新楼宇评级
        buildingRatingService.updateById(ratingEntity);

        // 更新评级详情
        detailEntity.setBuildingNo(ratingEntity.getBuildingNo());
        saveOrUpdateDetail(detailEntity);

        // 大屏提交系数
        String submitCoefficient = calculateSubmitCoefficient(param, isLargeScreen);

        // 更新BuildingScreen对象
        buildingScreenService.saveOrUpdate(ratingEntity.getBuildingNo(), submitCoefficient, param);

        // 更新楼宇基因数据
        buildingGeneService.saveOrUpdate(ratingEntity.getBuildingNo(), param);

        // 更新楼宇元数据
        buildingMetaService.saveOrUpdate(ratingEntity, isLargeScreen, false);

        // 通知城市负责人
        sendMessageToCityBusinessHead(List.of(ratingEntity));

        // 提交AI评级处理
        aiRatingHandler.aiRatingAfterCommit(AiRatingHandler.Type.EDIT_RATING, ratingEntity.getBuildingNo(),
                UserThreadLocal.getUser().getWno());

        return ratingEntity;
    }

    @Override
    public Boolean checkComplete(String buildingNo) {
        // 完善评级中检查
        boolean isCompleting = completeRatingService.lambdaQuery()
                .eq(CompleteRatingEntity::getBuildingRatingNo, buildingNo)
                .eq(CompleteRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.getValue())
                .exists();
        if (isCompleting) {
            throw new ServerException("该楼宇存在审核中的完善评级申请，无法编辑");
        }

        return true;
    }

    @Override
    public void revokeUnsubmitted(AiRatingHandler.Type type, String bizCode) {
        if (Objects.isNull(type) || StrUtil.isBlank(bizCode)) {
            return;
        }

        switch (type) {
            case BUILDING_RATING:
                doRevokeUnsubmitted(bizCode);
                break;
            case COMPLETE_RATING:
                completeRatingService.revokeUnsubmitted(bizCode);
                break;
        }
    }

    private void doRevokeUnsubmitted(String bizCode) {
        BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, bizCode)
                .one();
        if (Objects.isNull(ratingEntity)) {
            return;
        }

        if (approvalCenterFacade.isTaskExist(bizCode, ratingEntity.getRatingVersion(), ApprovalTypeEnum.BUILDING_APPROVAL)) {
            // 已经发起了审批流，不能撤回
            return;
        }

        handleRevoke(bizCode);
    }

    private void sendMessageToCityBusinessHead(List<BuildingRatingEntity> buildingRatings) {
        try {
            // 获取城市负责人
            List<CityVO> cityVos = RpcUtils.unBox(feignCmsRpc.getBusinessHead(buildingRatings.stream()
                    .map(BuildingRatingEntity::getMapCity)
                    .collect(Collectors.toSet())));
            if (CollUtil.isEmpty(cityVos)) {
                log.warn("城市负责人为空，无法发送消息");
                return;
            }

            Map<String, Integer> cityMapping = cityVos.stream().collect(Collectors.toMap(CityVO::getName, CityVO::getBusinessHead));

            // 城市负责人id集合
            Collection<Integer> businessHeads = cityMapping.values();
            List<CodeNameVO> users = RpcUtils.unBox(feignAuthorityRpc.listUserByIds(businessHeads));
            Map<Integer, CodeNameVO> userMapping = users.stream().collect(Collectors.toMap(CodeNameVO::getId, Function.identity()));

            // 组装消息
            UserBatchMessageParam message = new UserBatchMessageParam();
            List<UserMessageContentParam> contents = new ArrayList<>();
            message.setContentParams(contents);
            message.setAppCode(DictCodeConstants.MSG_APP_MEHT);
            message.setModuleCode(DictCodeConstants.MSG_MODULE_HIGH_SEA);
            message.setSendUserId(UserThreadLocal.getUserId());
            buildingRatings.forEach(buildingRating -> {
                // 城市负责人id
                Integer businessHead = cityMapping.get(buildingRating.getMapCity());
                if (Objects.isNull(businessHead)) {
                    log.warn("客户{} {}的城市{}负责人为空，无法发送消息",
                            buildingRating.getBuildingNo(), buildingRating.getBuildingName(), buildingRating.getMapCity());
                    return;
                }

                CodeNameVO user = userMapping.get(businessHead);
                if (Objects.isNull(user)) {
                    log.warn("客户{} {}城市负责人{}信息为空，无法发送消息",
                            buildingRating.getBuildingNo(), buildingRating.getBuildingName(), businessHead);
                    return;
                }

                UserMessageContentParam content = new UserMessageContentParam();
                content.setContent(String.format(EDIT_MSG, buildingRating.getMapCity(), buildingRating.getBuildingName(), UserThreadLocal.getUser().getName()));
                content.setReceiveUserId(user.getId());
                if (user.isInner()) {
                    // 内部用户需要发送飞书机器人消息
                    content.setFeiShuFlag(true);
                }
                contents.add(content);
            });

            messageSendUtil.sendMessage(message);
        } catch (Exception e) {
            log.error("消息发送失败", e);
        }
    }

    private void setReviewLevelIfNeed(BuildingRatingEntity ratingEntity) {
        if (RatingConstants.RATING_LEVEL_BELOW_A.equals(ratingEntity.getProjectLevel())) {
            // 认证等级未达到A级，设置复核等级为A级
            ratingEntity.setProjectReviewLevel(RatingConstants.RATING_LEVEL_A);
            // 保存复核等级设置操作记录
            operateLogService.saveOperateLog(
                    BusinessTypeEnum.BUILDING_RATING.getValue(),
                    ratingEntity.getBuildingNo(),
                    "编辑评级未达到A级，自动调整为A级",
                    "编辑评级",
                    ""
            );
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuildingRatingEntity draft(RatingApplyDto param) {
        BuildingRatingEntity existRatingEntity = getExistDraft(param.getBuildingNo());

        // 创建楼宇评级草稿
        BuildingRatingEntity ratingEntity = buildingRatingService.createRatingEntity(param, false);

        // 创建楼宇详情
        BuildingDetailsEntity detailEntity = buildingDetailsService.createDetailsEntity(param);
        // 保存AI数据用于页面草稿回显
        fillAiData(param, detailEntity);

        // 创建BuildingScreen对象
        BuildingScreenEntity screenEntity = buildingScreenService.creteScreenEntity(ratingEntity.getBuildingNo(), null, param);

        // 保存或更新楼宇评级
        saveOrUpdateDraft(existRatingEntity, ratingEntity);

        detailEntity.setBuildingNo(ratingEntity.getBuildingNo());
        screenEntity.setBuildingRatingNo(ratingEntity.getBuildingNo());

        RatingDraftDTO draftDTO = new RatingDraftDTO();
        draftDTO.setRatingEntity(ratingEntity);
        draftDTO.setDetailsEntity(detailEntity);
        draftDTO.setScreenEntity(screenEntity);

        // 更新草稿快照
        buildingRatingService.lambdaUpdate()
                .set(BuildingRatingEntity::getDraft, JSON.toJSONString(draftDTO))
                .eq(BuildingRatingEntity::getId, ratingEntity.getId())
                .update();

        return ratingEntity;
    }

    private void fillAiData(RatingApplyDto param, BuildingDetailsEntity detailEntity) {
        detailEntity.setThirdBuildingGrade(param.getThirdBuildingGrade());
        detailEntity.setThirdBuildingLocation(param.getThirdBuildingLocation());
        detailEntity.setThirdBuildingNumber(param.getThirdBuildingNumber());
        detailEntity.setThirdBuildingPrice(param.getThirdBuildingPrice());
        detailEntity.setThirdBuildingAge(param.getThirdBuildingAge());
        detailEntity.setThirdBuildingExterior(param.getThirdBuildingExterior());
        detailEntity.setThirdBuildingLobby(param.getThirdBuildingLobby());
        detailEntity.setThirdBuildingGarage(param.getThirdBuildingGarage());
        detailEntity.setThirdBuildingBrand(param.getThirdBuildingBrand());
        detailEntity.setThirdBuildingRating(param.getThirdBuildingRating());
        detailEntity.setThirdDailyPrice(param.getThirdDailyPrice());
        detailEntity.setThirdDeliveryDate(param.getThirdDeliveryDate());
    }

    private BuildingRatingEntity getExistDraft(String buildingNo) {
        if (StrUtil.isBlank(buildingNo)) {
            return null;
        }

        // 查询楼宇评级草稿数据
        return buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo)
                .eq(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.DRAFT.getValue())
                .last("limit 1")
                .one();
    }

    @Override
    public RatingVO info(String businessKey, Integer type, String ratingVersion) {
        RatingVO ratingVO;
        if (RatingConstants.TYPE_RATING.equals(type)) {
            // 查询楼宇评级详情
            ratingVO = getRatingInfo(businessKey, ratingVersion);
        } else if (RatingConstants.TYPE_COMPLETE.equals(type)) {
            if (businessKey.contains("WS")) {
                // 查询楼宇完善评级详情
                ratingVO = completeRatingService.getCompleteInfo(businessKey, ratingVersion);

                //补充完善评级经纬度
                if (StringUtils.isNotBlank(ratingVO.getBuildingNo())) {
                    BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                            .select(BuildingRatingEntity::getBuildingNo, BuildingRatingEntity::getMapLatitude, BuildingRatingEntity::getMapLongitude)
                            .eq(BuildingRatingEntity::getBuildingNo, ratingVO.getBuildingNo())
                            .one();
                    ratingVO.setMapLatitude(StringUtils.isBlank(ratingEntity.getMapLatitude()) ? "" : rsaExample.decryptByPrivate(ratingEntity.getMapLatitude()));
                    ratingVO.setMapLongitude(StringUtils.isBlank(ratingEntity.getMapLongitude()) ? "" : rsaExample.decryptByPrivate(ratingEntity.getMapLongitude()));
                }

            } else {
                // 1.7.8以前的完善评级数据，完善表里没有数据，需要查主表详情
                ratingVO = getRatingInfo(businessKey, ratingVersion);
                ratingVO.setCompleteRatingNo(businessKey);
            }
            ratingVO.setType(RatingConstants.TYPE_COMPLETE);
            // 设置主表评级标识
            setRatingFlag(ratingVO);
        } else {
            ratingVO = null;
        }

        if (Objects.isNull(ratingVO)) {
            log.error("不支持的评级类型，businessKey：{}，type：{}", businessKey, type);
            throw new ServerException("不支持的评级类型");
        }

        // 设置简略地址
        ratingVO.setSimplifyAddress(AddressUtil.simplify(ratingVO.getMapAddress(), ratingVO.getMapProvince(), ratingVO.getMapCity(), ratingVO.getMapRegion()));

        // 设置楼宇类型名称
        Optional.ofNullable(ratingVO.getBuildingType())
                .ifPresent(buildingType -> ratingVO.setBuildingTypeName(BuildingRatingEntity.BuildingType.getNameByValue(buildingType)));

        // 设置审核权限
        setApproveRight(businessKey, ratingVO);

        // h5地图地址
        ratingVO.setMapUrl(h5MapUtil.getH5MapUrl(ratingVO.getMapCity(), ratingVO.getBuildingName()));

        // 设置楼宇状态名
        if (Objects.nonNull(ratingVO.getStatus())) {
            ratingVO.setStatusName(BuildingRatingEntity.Status.getNameByValue(ratingVO.getStatus()));
        }

        // 设置编辑权限
        ratingVO.setIsSelfApply(StrUtil.isNotBlank(ratingVO.getSubmitUser())
                && ratingVO.getSubmitUser().equals(UserThreadLocal.getUser().getWno()));

        //空对象处理
        if (Objects.isNull(ratingVO.getBuildingDetailsVO())) {
            ratingVO.setBuildingDetailsVO(new BuildingDetailsVO());
        }

        if (Objects.isNull(ratingVO.getBuildingScreenVO())) {
            ratingVO.setBuildingScreenVO(new BuildingScreenVO());
        }

        if (Objects.isNull(ratingVO.getBuildingGeneVO())) {
            ratingVO.setBuildingGeneVO(new BuildingGeneVO());
        }

        return ratingVO;
    }

    private void setRatingFlag(RatingVO ratingVO) {
        BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getSmallScreenRatingFlag,
                        BuildingRatingEntity::getLargeScreenRatingFlag)
                .eq(BuildingRatingEntity::getBuildingNo, ratingVO.getBuildingNo())
                .one();
        if (Objects.nonNull(ratingEntity)) {
            ratingVO.setLargeScreenRatingFlag(ratingEntity.getLargeScreenRatingFlag());
            ratingVO.setSmallScreenRatingFlag(ratingEntity.getSmallScreenRatingFlag());
        }
        CityCoefficientEntity coefficient = cityCoefficientService.getCoefficient(ratingEntity.getMapAdCode());
        if (Objects.nonNull(coefficient)) {
            ratingVO.setCoefficientValue(coefficient.getCoefficient());
        } else {
            ratingVO.setCoefficientValue(BigDecimal.ONE);
        }
    }

    private void setApproveRight(String buildingNo, RatingVO ratingVO) {
        try {
            InnerInstanceTaskVO currentTask = approvalCenterFacade.getCurrentTask(buildingNo, ratingVO.getRatingVersion());
            if (Objects.nonNull(currentTask) && Objects.nonNull(currentTask.getUserId())) {
                ratingVO.setIsSelfApprove(Objects.equals(UserThreadLocal.getUserId(), currentTask.getUserId()));
            } else {
                ratingVO.setIsSelfApprove(false);
            }
        } catch (Exception e) {
            ratingVO.setIsSelfApprove(false);
            log.error("设置审核权限异常", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDraft(String buildingNo) {
        BuildingRatingEntity existRatingEntity = getExistDraft(buildingNo);

        if (Objects.isNull(existRatingEntity)) {
            return;
        }

        buildingRatingService.removeById(existRatingEntity);

        buildingScreenService.lambdaUpdate().eq(BuildingScreenEntity::getBuildingRatingNo, buildingNo).remove();

        buildingDetailsService.lambdaUpdate().eq(BuildingDetailsEntity::getBuildingNo, buildingNo).remove();

        buildingGeneService.lambdaUpdate().eq(BuildingGeneEntity::getBuildingRatingNo, buildingNo).remove();

        buildingMetaService.lambdaUpdate().eq(BuildingMetaEntity::getBuildingRatingNo, buildingNo).remove();

        // 关闭商机
        businessOpportunityService.lambdaUpdate()
                .set(BusinessOpportunityEntity::getStatus, BusinessChangeStatusEnum.CLOSE.getCode())
                .eq(BusinessOpportunityEntity::getBuildingNo, existRatingEntity.getBuildingNo())
                .update();

        // 无效跟进
        businessOpportunityService.updateFollowRecord(existRatingEntity.getBuildingNo());
    }

    @Override
    public void handleApproved(String businessKey) {
        BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, businessKey)
                .one();
        if (Objects.isNull(ratingEntity)) {
            return;
        }
        buildingRatingService.lambdaUpdate()
                .set(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.AUDITED.getValue())
                .set(BuildingRatingEntity::getBuildingStatus, BuildingRatingEntity.BuildingStatus.CONFIRMED.getValue())
                .set(BuildingRatingEntity::getApproveTime, LocalDateTime.now())
                .eq(BuildingRatingEntity::getId, ratingEntity.getId())
                .update();

        buildingMetaService.lambdaUpdate()
                .set(BuildingMetaEntity::getBuildingStatus, BuildingRatingEntity.BuildingStatus.CONFIRMED.getValue())
                .set(BuildingMetaEntity::getSuccessTime, LocalDateTime.now())
                .eq(BuildingMetaEntity::getBuildingRatingNo, ratingEntity.getBuildingNo())
                .update();

        // 增加楼宇状态变动记录
        buildingRatingService.addBuildingChangeLog(ratingEntity.getId(), ratingEntity.getBuildingNo(),
                BuildingRatingEntity.Status.AUDITED.getValue());

        // 处理商机
        buildingRatingService.processBusiness(ratingEntity);
    }

    @Override
    public void handleRejected(String businessKey) {
        BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, businessKey)
                .one();
        if (Objects.isNull(ratingEntity)) {
            return;
        }

        ratingEntity.setStatus(BuildingRatingEntity.Status.FAILED_AUDIT.getValue())
                .setBuildingStatus(BuildingRatingEntity.BuildingStatus.UN_CONFIRM.getValue())
                .setSmallScreenRatingFlag(BooleFlagEnum.NO.getCode())
                .setLargeScreenRatingFlag(BooleFlagEnum.NO.getCode());
        buildingRatingService.updateById(ratingEntity);

        rejectOrRevoke(ratingEntity);

        // 清空入公海时间
        if (BuildingRatingEntity.HighSeaFlagEnum.NO.getCode().equals(ratingEntity.getHighSeaFlag())) {
            LambdaUpdateWrapper<BuildingRatingEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BuildingRatingEntity::getId, ratingEntity.getId());
            updateWrapper.set(BuildingRatingEntity::getEnterSeaTime, null);
            buildingRatingService.update(updateWrapper);
        }

        // 置空复核系数
        LambdaUpdateWrapper<BuildingScreenEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(BuildingScreenEntity::getFinalCoefficient, null)
                .eq(BuildingScreenEntity::getBuildingRatingNo, ratingEntity.getBuildingNo());
        buildingScreenService.update(updateWrapper);

        // 增加楼宇状态变动记录
        buildingRatingService.addBuildingChangeLog(ratingEntity.getId(), ratingEntity.getBuildingNo(),
                BuildingRatingEntity.Status.FAILED_AUDIT.getValue());
    }

    @Override
    public void handleRevoke(String businessKey) {
        BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, businessKey)
                .one();
        if (Objects.isNull(ratingEntity)) {
            return;
        }

        // 状态回退到草稿
        ratingEntity.setStatus(BuildingRatingEntity.Status.DRAFT.getValue())
                .setBuildingStatus(BuildingRatingEntity.BuildingStatus.UN_CONFIRM.getValue())
                .setSmallScreenRatingFlag(BooleFlagEnum.NO.getCode())
                .setLargeScreenRatingFlag(BooleFlagEnum.NO.getCode())
                .setHighSeaFlag(BooleFlagEnum.NO.getCode());
        buildingRatingService.updateById(ratingEntity);

        BuildingScreenEntity screenEntity = buildingScreenService.lambdaQuery()
                .eq(BuildingScreenEntity::getBuildingRatingNo, businessKey)
                .one();

        BuildingDetailsEntity detailEntity = buildingDetailsService.lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, businessKey)
                .one();

        // 设置草稿快照
        RatingDraftDTO draftDTO = new RatingDraftDTO();
        draftDTO.setRatingEntity(ratingEntity);
        draftDTO.setDetailsEntity(detailEntity);
        draftDTO.setScreenEntity(screenEntity);
        buildingRatingService.lambdaUpdate()
                .set(BuildingRatingEntity::getDraft, JSON.toJSONString(draftDTO))
                .eq(BuildingRatingEntity::getBuildingNo, ratingEntity.getBuildingNo())
                .update();

        rejectOrRevoke(ratingEntity);

        // 删除上一次的提交状态变更记录
        changeLogService.deleteLatestLog(ratingEntity.getId(), BuildingStatusChangeLogEntity.BizType.RATING,
                BuildingStatusChangeLogEntity.RatingApplicationStatus.WAIT_APPROVED);
    }

    private void rejectOrRevoke(BuildingRatingEntity ratingEntity) {
        // 处理元数据
        buildingMetaService.lambdaUpdate()
                .set(BuildingMetaEntity::getBuildingStatus, BuildingRatingEntity.BuildingStatus.UN_CONFIRM.getValue())
                .set(BuildingMetaEntity::getUpdateBy, UserThreadLocal.getUser().getWno())
                .set(BuildingMetaEntity::getManager, null)
                .set(BuildingMetaEntity::getProjectLevel, "")
                .set(BuildingMetaEntity::getBuildingScore, null)
                .eq(BuildingMetaEntity::getBuildingRatingNo, ratingEntity.getBuildingNo())
                .update();

        // 关闭商机
        businessOpportunityService.lambdaUpdate()
                .set(BusinessOpportunityEntity::getStatus, BusinessChangeStatusEnum.CLOSE.getCode())
                .eq(BusinessOpportunityEntity::getBuildingNo, ratingEntity.getBuildingNo())
                .update();

        // 无效跟进
        businessOpportunityService.updateFollowRecord(ratingEntity.getBuildingNo());
    }

    private RatingVO getRatingInfo(String buildingNo, String ratingVersion) {
        BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo)
                .last("limit 1")
                .one();
        if (Objects.isNull(ratingEntity)) {
            throw new ServerException("楼宇评级不存在");
        }

        BuildingDetailsEntity detailsEntity;
        BuildingScreenEntity screenEntity;

        if (ratingEntity.getRatingVersion().equals(ratingVersion)) {
            if (BuildingRatingEntity.Status.DRAFT.getValue() == ratingEntity.getStatus()) {
                // 获取草稿
                String draft = ratingEntity.getDraft();
                if (StrUtil.isBlank(draft)) {
                    return new RatingVO();
                }
                RatingDraftDTO ratingDraftDTO = JSON.parseObject(draft, RatingDraftDTO.class);
                ratingEntity = ratingDraftDTO.getRatingEntity();
                detailsEntity = ratingDraftDTO.getDetailsEntity();
                screenEntity = ratingDraftDTO.getScreenEntity();
            } else {
                // 获取最新数据
                detailsEntity = buildingDetailsService.lambdaQuery()
                        .eq(BuildingDetailsEntity::getBuildingNo, ratingEntity.getBuildingNo())
                        .last("limit 1")
                        .one();

                screenEntity = buildingScreenService.lambdaQuery()
                        .eq(BuildingScreenEntity::getBuildingRatingNo, ratingEntity.getBuildingNo())
                        .last("limit 1")
                        .one();
            }
        } else {
            // 获取版本对应快照
            BuildingSnapshotEntity snapshotEntity = buildingSnapshotService.lambdaQuery()
                    .eq(BuildingSnapshotEntity::getBuildingRatingNo, buildingNo)
                    .eq(BuildingSnapshotEntity::getRatingVersion, ratingVersion)
                    .last("limit 1")
                    .one();

            if (Objects.isNull(snapshotEntity)) {
                throw new ServerException("快照不存在");
            }
            ratingEntity = JSON.parseObject(snapshotEntity.getRatingSnapshot(), BuildingRatingEntity.class);
            detailsEntity = JSON.parseObject(snapshotEntity.getDetailsSnapshot(), BuildingDetailsEntity.class);
            screenEntity = JSON.parseObject(snapshotEntity.getScreenSnapshot(), BuildingScreenEntity.class);
        }

        // 解密
        try {
            ratingEntity.setMapAddress(rsaExample.decryptByPrivate(ratingEntity.getMapAddress()));
            ratingEntity.setMapLatitude(rsaExample.decryptByPrivate(ratingEntity.getMapLatitude()));
            ratingEntity.setMapLongitude(rsaExample.decryptByPrivate(ratingEntity.getMapLongitude()));
        } catch (Exception ignored) {
        }

        RatingVO ratingVO = ratingConvert.toRatingVO(ratingEntity);
        // 设置图片
        fillPics(ratingEntity, ratingVO);
        // 设置用户名称
        Map<String, SysUserDto> nameMapping = sysUserService.getNameMaps(Arrays.asList(ratingEntity.getSubmitUser(), ratingEntity.getCreateBy()));
        Optional.ofNullable(nameMapping.get(ratingEntity.getSubmitUser())).ifPresent(user -> ratingVO.setSubmitUserName(user.getRealName()));
        Optional.ofNullable(nameMapping.get(ratingEntity.getCreateBy())).ifPresent(user -> ratingVO.setCreateUserName(user.getRealName()));
        ratingVO.setCreateUserCode(ratingEntity.getCreateBy());

        // 设置评级详情
        Optional.ofNullable(detailsEntity)
                .ifPresent(details -> ratingVO.setBuildingDetailsVO(BuildingDetailsConvert.INSTANCE.toBuildingDetailsVO(detailsEntity)));

        // 设置大屏详情
        if (Objects.nonNull(screenEntity)) {
            ratingVO.setLargeScreen(buildingScreenService.isLargeScreen(screenEntity.getSpec()));

            String specName = null;
            if (StrUtil.isNotBlank(screenEntity.getSpec())) {
                List<String> dicCodes = JSON.parseArray(screenEntity.getSpec(), String.class);
                Map<String, String> dictMapping = codeNameHelper.getDictMapping(dicCodes);
                List<CodeNameVO> codeNameVOS = dictMapping.entrySet().stream()
                        .map(entry -> new CodeNameVO(null, entry.getKey(), entry.getValue(), null, null))
                        .toList();
                screenEntity.setSpec(JSON.toJSONString(codeNameVOS));
                specName = String.join(",", dictMapping.values());
            }
            BuildingScreenVO buildingScreenVO = BuildingScreenConvert.INSTANCE.toBuildingScreenVO(screenEntity);
            buildingScreenVO.setSpecName(specName);
            ratingVO.setBuildingScreenVO(buildingScreenVO);
        } else {
            ratingVO.setLargeScreen(false);
        }

        // 设置地区系数
        CityCoefficientEntity coefficient = cityCoefficientService.getCoefficient(ratingEntity.getMapAdCode());
        if (Objects.nonNull(coefficient)) {
            ratingVO.setCoefficientValue(coefficient.getCoefficient());
        } else {
            ratingVO.setCoefficientValue(BigDecimal.ONE);
        }

        // 设置基因数据
        fillBuildingGene(ratingVO);

        // 设置审批信息
        Result<List<ScreenApproveRecordDTO>> screenApproveRecordDTOResult = approvalQueryService.queryLocalNodes(ratingVO.getBuildingNo());
        if (Objects.nonNull(screenApproveRecordDTOResult) && Objects.nonNull(screenApproveRecordDTOResult.getData())) {
            List<ScreenApproveRecordDTO> data = screenApproveRecordDTOResult.getData();
            ratingVO.setApprovalDetailVO(data);
        }

        ratingVO.setType(RatingConstants.TYPE_RATING);

        return ratingVO;
    }

    private void fillBuildingGene(RatingVO ratingVO) {
        BuildingGeneEntity buildingGeneEntity = buildingGeneService.lambdaQuery()
                .eq(BuildingGeneEntity::getBuildingRatingNo, ratingVO.getBuildingNo())
                .one();

        if (Objects.nonNull(buildingGeneEntity)) {
            if (StringUtils.isNotBlank(buildingGeneEntity.getCompetitiveMediaInfo())) {
                buildingGeneEntity.setCompetitiveMediaInfos(Arrays.asList(buildingGeneEntity.getCompetitiveMediaInfo().split(",")));
            }

            BuildingGeneVO buildingGeneVO = BuildingGeneConvert.INSTANCE.toBuildingGeneVO(buildingGeneEntity);
            buildingGeneVO.setTargetPointCount(ratingVO.getTargetPointCount());
            translate(buildingGeneVO);
            ratingVO.setBuildingGeneVO(buildingGeneVO);
        }

    }


    private void translate(BuildingGeneVO buildingGene) {
        if (buildingGene != null) {
            if (CollectionUtils.isNotEmpty(buildingGene.getCompetitiveMediaInfos())) {
                // 按逗号截取
                Map<String, String> mediaTypeDictMapping = codeNameHelper.getDictMapping(buildingGene.getCompetitiveMediaInfos());
                // 使用String.join方法进行字符串拼接
                String res = String.join("，", mediaTypeDictMapping.values());
                buildingGene.setCompetitiveMediaInfoName(res);
            }


            if (StringUtils.isNotBlank(buildingGene.getForbiddenIndustry())) {
                if (buildingGene.getForbiddenIndustry().trim().equals("无")) {
                    buildingGene.setForbiddenIndustryName("无");
                } else {
                    Map<String, String> industryMapping = codeNameHelper.getIndustryMapping(Arrays.asList(buildingGene.getForbiddenIndustry().split(",")));
                    // 使用String.join方法进行字符串拼接
                    String res = String.join("，", industryMapping.values());
                    buildingGene.setForbiddenIndustryName(res);
                }
            }
        }
    }

    private void fillPics(BuildingRatingEntity entity, RatingVO ratingVO) {
        // 外墙材料附件地址
        fillPic(entity.getBuildingExteriorPic(), ratingVO, "buildingExteriorPic");
        // 楼盘大堂附件地址
        fillPic(entity.getBuildingLobbyPic(), ratingVO, "buildingLobbyPic");
        // 侯梯厅附件地址
        fillPic(entity.getBuildingHallPic(), ratingVO, "buildingHallPic");
        // 大堂环境图附件地址
        fillPic(entity.getBuildingLobbyEnvPic(), ratingVO, "buildingLobbyEnvPic");
        // 梯厅环境图附件地址
        fillPic(entity.getBuildingElevatorPic(), ratingVO, "buildingElevatorPic");
        // 闸口图附件地址
        fillPic(entity.getBuildingGatePic(), ratingVO, "buildingGatePic");
        // 安装示意图附件地址
        fillPic(entity.getBuildingInstallationPic(), ratingVO, "buildingInstallationPic");
    }

    private void fillPic(String fileIds, RatingVO result, String key) {
        if (StringUtils.isBlank(fileIds)) {
            return;
        }

        String[] split = StringUtils.split(fileIds, ",");
        List<SysFileEntity> list = sysFileService.list(Wrappers.<SysFileEntity>lambdaQuery()
                .in(SysFileEntity::getId, Arrays.asList(split)));
        if (CollectionUtils.isNotEmpty(list)) {
            List<SysFileVO> fileVOS = new ArrayList<>();
            list.forEach(ele -> {
                SysFileVO sysFileVO = new SysFileVO();
                sysFileVO.setId(ele.getId());
                sysFileVO.setName(ele.getName());
                sysFileVO.setUrl(ele.getUrl());
                sysFileVO.setSize(ele.getSize());
                sysFileVO.setAttachmentType(ele.getAttachmentType());
                fileVOS.add(sysFileVO);
            });

            switch (key) {
                case "buildingExteriorPic" -> result.setBuildingExteriorPics(fileVOS);
                case "buildingLobbyPic" -> result.setBuildingLobbyPics(fileVOS);
                case "buildingHallPic" -> result.setBuildingHallPics(fileVOS);
                case "buildingLobbyEnvPic" -> result.setBuildingLobbyEnvPics(fileVOS);
                case "buildingElevatorPic" -> result.setBuildingElevatorPics(fileVOS);
                case "buildingGatePic" -> result.setBuildingGatePics(fileVOS);
                case "buildingInstallationPic" -> result.setBuildingInstallationPics(fileVOS);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLevel(BuildingLevelReq req) {
        BuildingRatingEntity existEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, req.getBuildingNo())
                .eq(BuildingRatingEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .last("limit 1")
                .one();
        if (Objects.isNull(existEntity)) {
            throw new ServerException("楼宇评级数据不存在");
        }
        String oldLevel = existEntity.getProjectReviewLevel();
        existEntity.setProjectReviewLevel(req.getLevel());
        BuildingMetaEntity metaEntity = buildingMetaService.lambdaQuery()
                .eq(BuildingMetaEntity::getBuildingRatingNo, existEntity.getBuildingNo())
                .last("limit 1")
                .one();
        if (Objects.isNull(metaEntity)) {
            throw new ServerException("楼宇基本信息数据不存在");
        }
        metaEntity.setProjectLevel(req.getLevel());
        // 更新楼宇评级数据
        buildingRatingService.updateById(existEntity);
        // 更新楼宇基本信息
        buildingMetaService.updateById(metaEntity);
        // 保存附件
        if (CollectionUtil.isNotEmpty(req.getAttachmentUrls())) {
            List<RatingAttachmentEntity> ratingAttachmentEntityList = new ArrayList<>();
            for (String url : req.getAttachmentUrls()) {
                // 保存附件信息
                RatingAttachmentEntity ratingAttachmentEntity = new RatingAttachmentEntity();
                ratingAttachmentEntity.setBizCode(req.getBuildingNo());
                ratingAttachmentEntity.setBizId(req.getId());
                ratingAttachmentEntity.setUrl(url);
                ratingAttachmentEntity.setType(BusinessTypeEnum.BUILDING_RATING.getValue());
                ratingAttachmentEntityList.add(ratingAttachmentEntity);
            }
            ratingAttachmentService.saveBatch(ratingAttachmentEntityList);
        }
        // 构建操作日志内容
        String content = buildContent(req, oldLevel);
        // 保存操作日志
        operateLogService.saveOperateLog(
                BusinessTypeEnum.BUILDING_RATING.getValue(),
                existEntity.getBuildingNo(),
                req.getDescription(),
                "修改",
                content
        );
    }

    /**
     * 构建变更内容
     */
    private String buildContent(BuildingLevelReq req, String oldLevel) {
        StringBuilder changeDesc = new StringBuilder()
                .append("楼宇评级修改等级从")
                .append(oldLevel)
                .append(", 变更为")
                .append(req.getLevel())
                .append(";");
        if (CollectionUtil.isNotEmpty(req.getAttachmentUrls())) {
            changeDesc.append("楼宇评级修改附件从")
                    .append("[空]")
                    .append(", 变更为")
                    .append(JSONUtil.toJsonStr(req.getAttachmentUrls()))
                    .append(";");
        }
        if (StringUtils.isNotBlank(req.getDescription())) {
            changeDesc.append("楼宇评级修改备注从")
                    .append("[空]")
                    .append(", 变更为")
                    .append(req.getDescription())
                    .append(";");
        }

//        // 查询最近的操作日志
//        OperateLogEntity operateLogEntity = operateLogService.lambdaQuery()
//                .eq(OperateLogEntity::getBizId, req.getBuildingNo())
//                .eq(OperateLogEntity::getType, BusinessTypeEnum.BUILDING_RATING.getValue())
//                .orderByDesc(OperateLogEntity::getCreateTime)
//                .last("limit 1")
//                .one();
//
//        // 获取旧描述和新描述
//        String oldDesc = operateLogEntity != null ? operateLogEntity.getDescription() : null;
//        String newDesc = req.getDescription();
//
//        // 只有当新旧描述都不为空时才添加备注变更信息
//        if (StringUtils.isNotBlank(oldDesc) || StringUtils.isNotBlank(newDesc)) {
//            changeDesc.append("楼宇评级修改备注从")
//                    .append(StringUtils.defaultIfBlank(oldDesc, "空"))
//                    .append(", 变更为")
//                    .append(StringUtils.defaultIfBlank(newDesc, "空"))
//                    .append(";");
//        }

        return changeDesc.toString();
    }

    private void saveOrUpdateDraft(BuildingRatingEntity existRatingEntity, BuildingRatingEntity ratingEntity) {
        if (Objects.nonNull(existRatingEntity)) {
            ratingEntity.setId(existRatingEntity.getId());
            ratingEntity.setBuildingNo(existRatingEntity.getBuildingNo());
            ratingEntity.setRatingVersion(existRatingEntity.getRatingVersion());
            buildingRatingService.updateById(ratingEntity);
        } else {
            ratingEntity.setBuildingNo(codeGenerator.generateRatingCode());
            buildingRatingService.save(ratingEntity);
        }
    }

    /**
     * 填充不可更改字段
     *
     * @param ratingEntity
     * @param existRatingEntity
     */
    private void fillImmutableField(BuildingRatingEntity ratingEntity, BuildingRatingEntity existRatingEntity) {
        ratingEntity.setId(existRatingEntity.getId());
        ratingEntity.setBuildingNo(existRatingEntity.getBuildingNo());
        ratingEntity.setStatus(existRatingEntity.getStatus());
        ratingEntity.setBuildingStatus(existRatingEntity.getBuildingStatus());
        ratingEntity.setProjectReviewLevel(existRatingEntity.getProjectReviewLevel());
        ratingEntity.setSubmitUser(existRatingEntity.getSubmitUser());
        ratingEntity.setSubmitTime(existRatingEntity.getSubmitTime());
        ratingEntity.setCreateBy(existRatingEntity.getCreateBy());
        ratingEntity.setCreateTime(existRatingEntity.getCreateTime());
    }

    /**
     * 保存或更新楼宇详情
     *
     * @param detailsEntity
     */
    private void saveOrUpdateDetail(BuildingDetailsEntity detailsEntity) {
        BuildingDetailsEntity existEntity = buildingDetailsService.lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, detailsEntity.getBuildingNo())
                .last("limit 1")
                .one();

        if (Objects.isNull(existEntity)) {
            buildingDetailsService.save(detailsEntity);
        } else {
            detailsEntity.setId(existEntity.getId());
            buildingDetailsService.updateById(detailsEntity);
        }
    }

    /**
     * 填充评级数据
     *
     * @param ratingEntity
     * @param manualRatingResult
     */
    private void fillRatingData(BuildingRatingEntity ratingEntity, CalculateResultDTO manualRatingResult) {
        ratingEntity.setBuildingScore(manualRatingResult.getBuildingScore())
                .setProjectLevel(manualRatingResult.getProjectLevel())
                .setFirstFloorExclusive(manualRatingResult.getFirstFloorExclusiveScore())
                .setFirstFloorShare(manualRatingResult.getFirstFloorShareScore())
                .setNegativeFirstFloor(manualRatingResult.getNegativeFirstFloorScore())
                .setNegativeTwoFloor(manualRatingResult.getNegativeTwoFloorScore())
                .setTwoFloorAbove(manualRatingResult.getTwoFloorAboveScore())
                .setThirdFloorBelow(manualRatingResult.getThirdFloorBelowScore());
    }

    /**
     * 是否大屏
     *
     * @param dto
     * @return
     */
    private boolean isLargeScreen(RatingApplyDto dto) {
        if (dto.getBuildingType().equals(BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue())
                && StringUtils.isNotBlank(dto.getSpec())) {
            List<String> codeList = JSON.parseArray(dto.getSpec(), String.class);
            List<String> largeDeviceKey = largeScreenProperties.getLargeDictKey();

            Collection<String> intersection = CollectionUtil.intersection(largeDeviceKey, codeList);
            return CollectionUtil.isNotEmpty(intersection);
        }
        return false;
    }

    /**
     * 大屏系数计算
     *
     * @param param
     * @param isLargeScreen
     * @return
     */
    private String calculateSubmitCoefficient(RatingApplyDto param, boolean isLargeScreen) {
        if (!isLargeScreen) {
            return null;
        }
        BigScreenCalculateDTO bigScreenCalculateDTO = new BigScreenCalculateDTO();
        bigScreenCalculateDTO.setBuildingCeilingHeight(param.getBuildingCeilingHeight());
        bigScreenCalculateDTO.setBuildingNumberInput(param.getBuildingNumberInput());
        bigScreenCalculateDTO.setBuildingSpacing(param.getBuildingSpacing());
        bigScreenCalculateDTO.setBuildingAgeInput(param.getBuildingAgeInput());
        bigScreenCalculateDTO.setBuildingLocationText(param.getLocationName());
        return largeScreenCalculator.calculate(bigScreenCalculateDTO);
    }

}
