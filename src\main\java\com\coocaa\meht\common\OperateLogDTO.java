package com.coocaa.meht.common;


import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/19
 * @description TODO
 */
@Data
public class OperateLogDTO {
    /**
     * 系统名称
     * 数据字典中配置为 0006-7（媒资工作台）
     */
    private String systemCode = "0006-7";
    /**
     * 功能名称
     */
    private String functionName;

    /**
     * 实体名称 (数据字典中配置)（媒资工作台-客户管理 0005-26，媒资工作台-商机管理 0005-27）
     */
    private String entityCode;

    /**
     * 实体ID
     */
    private Integer entityId;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 操作人
     */
    private Integer operator;
}
