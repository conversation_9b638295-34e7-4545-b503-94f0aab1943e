package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.idempotent.RepeatSubmit;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.module.crm.dto.req.TransferReq;
import com.coocaa.meht.module.crm.enums.TransferFlagEnum;
import com.coocaa.meht.module.crm.vo.BuildingVO;
import com.coocaa.meht.module.web.dto.BigScreenCalculateDTO;
import com.coocaa.meht.module.web.dto.BuildingRateDto;
import com.coocaa.meht.module.web.dto.BuildingRatingExportDTO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.dto.RatingCalculateDto;
import com.coocaa.meht.module.web.dto.RatingCalculateManualDto;
import com.coocaa.meht.module.web.dto.VerifyRatingDTO;
import com.coocaa.meht.module.web.dto.req.BuildingPicReq;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.vo.BuildingMarginVO;
import com.coocaa.meht.module.web.vo.BuildingRatingPicVO;
import com.coocaa.meht.module.web.vo.BuildingStatusVO;
import com.coocaa.meht.module.web.vo.BuildingTrialCalculateVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 楼宇评级
 */
@RestController
@RequestMapping("/buildRating")
@Tag(name = "楼宇评级相关接口", description = "楼宇评级相关接口")
public class BuildingRatingController {

    @Resource
    private BuildingRatingService buildingRatingService;

    @Resource
    private BusinessOpportunityService businessOpportunityService;

    @Resource
    private LargeScreenProperties largeScreenProperties;

    /**
     * 智能获取楼宇信息
     *
     * @param name
     * @return
     */
    @GetMapping("getAiByRating")
    public Result<?> getByRating(String name, String mapNo) {
        Map<String, Object> result = buildingRatingService.getByRating(name, mapNo);
        return Result.ok(result);
    }

    /**
     * 判断是否有合同
     *
     * @param mapNo
     * @return
     */
    @GetMapping("/has-contract")
    public Result<?> hasContract(@RequestParam("mapNo") String mapNo) {
        return Result.ok(buildingRatingService.hasContract(mapNo));
    }


    /**
     * 楼宇信息
     *
     * @return
     */
    @Deprecated
    @GetMapping("info")
    public Result<?> info(String buildingNo) {
        Map<String, Object> result = buildingRatingService.info(buildingNo);
        return Result.ok(result);
    }


    /**
     * 楼宇申请
     *
     * @return
     */
    @RepeatSubmit
    @Operation(summary = "楼宇申请")
    @PostMapping("apply")
    public Result<BuildingRatingEntity> apply(@RequestBody @Valid RatingApplyDto dto) {
        return Result.ok(buildingRatingService.apply(dto));
    }

    /**
     * 楼宇试算
     */
    @Deprecated
    @Operation(summary = "楼宇试算")
    @PostMapping("calculate")
    public Result<BuildingTrialCalculateVO> calculate(@RequestBody @Valid RatingCalculateDto dto) {
        BuildingTrialCalculateVO result = buildingRatingService.calculate(dto);
        return Result.ok(result);
    }

    @Deprecated
    @Operation(summary = "楼宇人工试算")
    @PostMapping("/calculate/manual")
    public Result<BuildingTrialCalculateVO> manualCalculate(@RequestBody @Valid RatingCalculateManualDto dto) {
        return Result.ok(buildingRatingService.manualCalculate(dto));
    }

    /**
     * 楼宇申请编辑
     *
     * @return
     */
    @PostMapping("apply/update")
    public Result<?> applyUpdate(@RequestBody RatingApplyDto dto) {
        Map<String, Object> result = buildingRatingService.applyUpdate(dto);
        return Result.ok(result);
    }


    /**
     * 楼宇申请列表
     *
     * @return
     */
    @GetMapping("apply/list")
    @Operation(summary = "楼宇申请列表")
    public Result<?> applyList(@RequestParam(name = "name", required = false) String name,
                               @RequestParam(name = "pageSize", required = false) Integer pageSize,
                               @RequestParam(name = "pageNum", required = false) Integer pageNum) {
        pageSize = Optional.ofNullable(pageSize).orElse(20);
        pageNum = Optional.ofNullable(pageNum).orElse(1);
        Map<String, Object> result = buildingRatingService.applyList(name, pageSize, pageNum);
        return Result.ok(result);
    }

    /**
     * 已经审批过的楼宇列表
     */
    @GetMapping("/apply/list/audited")
    public Result<?> listAudited(@RequestParam(name = "name", required = false) String name,
                                 @RequestParam(name = "all", required = false, defaultValue = "false") boolean allUser) {
        return Result.ok(buildingRatingService.listAudited(name, allUser));
    }


    /**
     * 楼宇审批
     *
     * @return
     */
//    @Operation(summary = "楼宇审批")
//    @PostMapping("approve")
//    public Result<?> approve(@RequestBody @Valid RatingApproveDto dto) {
//        Map<String, Object> result = buildingRatingService.approve(dto);
//        return Result.ok(result);
//    }


    /**
     * 楼宇审批列表
     *
     * @return
     */
    @Deprecated
    @Operation(summary = "楼宇审批列表")
    @GetMapping("approve/list")
    public Result<?> approveList(@RequestParam(name = "name", required = false) String name,
                                 @RequestParam(name = "status", required = false) Integer status,
                                 @RequestParam(name = "pageSize", required = false) Integer pageSize,
                                 @RequestParam(name = "pageNum", required = false) Integer pageNum) {
        pageSize = Optional.ofNullable(pageSize).orElse(20);
        pageNum = Optional.ofNullable(pageNum).orElse(1);
        status = Optional.ofNullable(status).orElse(0);
        Map<String, Object> result = buildingRatingService.approveList(name, status, pageSize, pageNum);
        return Result.ok(result);
    }

    /**
     * 评分详情
     *
     * @param buildingNo
     * @return
     */
    @GetMapping("/scoringDetails")
    public Result<Map<String, Object>> scoringDetails(@RequestParam(name = "buildingNo") String buildingNo,
                                    @RequestParam(name = "ratingVersion", required = false) String ratingVersion) {
        return Result.ok(buildingRatingService.getScoringDetails(buildingNo, ratingVersion));
    }

    /**
     * 针对飞书调用接口
     *
     * @param buildingNo
     * @return
     */
    @Anonymous
    @GetMapping("detail")
    @Reqlog(value = "针对飞书调用接口", type = Reqlog.LogType.SELECT)
    public Result<?> detail(String buildingNo) {
        BuildingRateDto result = buildingRatingService.infoFs(buildingNo);
        return Result.ok(result);
    }


    @Anonymous
    @GetMapping("scoringFsDetails")
    public Result<?> scoringFsDetails(@RequestParam(name = "buildingNo") String buildingNo,
                                      @RequestParam(name = "ratingVersion", required = false) String ratingVersion) {
        Map<String, Object> result = buildingRatingService.scoringFsDetails(buildingNo, ratingVersion);
        return Result.ok(result);
    }


    @Anonymous
    @GetMapping("detailNew")
    @Reqlog(value = "针对飞书调用接口New", type = Reqlog.LogType.SELECT)
    public Object detailNew(String buildingNo) {
        BuildingRateDto result = buildingRatingService.infoFs(buildingNo);
        return result;
    }


    @Anonymous
    @GetMapping("getStatusChart")
    public Result<List<Map<String, Object>>> getStatusChart() {
        List<Map<String, Object>> result = buildingRatingService.getStatusChart();
        return Result.ok(result);
    }

    @Anonymous
    @GetMapping("getUv")
    public Result<List<Map<String, Object>>> getUv() {
        List<Map<String, Object>> result = buildingRatingService.getUv();
        return Result.ok(result);
    }

    @Anonymous
    @GetMapping("getTotal")
    public Result<List<Map<String, Object>>> getTotal() {
        List<Map<String, Object>> result = buildingRatingService.getTotal();
        return Result.ok(result);
    }

    @Anonymous
    @GetMapping("getList")
    public Result<List<Map<String, Object>>> getList() {
        List<Map<String, Object>> result = buildingRatingService.getList();
        return Result.ok(result);
    }


    @Anonymous
    @GetMapping("getProvinceList")
    public Result<List<Map<String, Object>>> getProvinceList() {
        List<Map<String, Object>> result = buildingRatingService.getProvinceList();

        return Result.ok(result);
    }

    @Operation(summary = "楼宇图片列表")
    @Anonymous
    @PostMapping("/building-pic")
    public Result<BuildingRatingPicVO> buildingPic(@RequestBody BuildingPicReq buildingPicReq) {
        return Result.ok(buildingRatingService.buildingPic(buildingPicReq));
    }

    @Operation(summary = "客户转移")
    @PostMapping("/transfer/customer")
    public Result<Boolean> transferCustomer(@RequestBody TransferReq req) {
        Boolean flag = false;
        if (TransferFlagEnum.BATCH_CUSTOMERS.name().equals(req.getTransferFlag())) {
            flag = buildingRatingService.transferCustomerBatch(req);
        } else if (TransferFlagEnum.SINGLE_CUSTOMERS.name().equals(req.getTransferFlag())) {
            flag = buildingRatingService.transferCustomer(req);
        }
        return Result.ok(flag);
    }

    @Operation(summary = "楼宇助手查城市")
    @GetMapping("/building-city")
    public Result<List<String>> buildingCity() {
        return Result.ok(buildingRatingService.buildingCIty());
    }


    @Operation(summary = "楼宇助手状态")
    @GetMapping("/building-status/{mapNo}")
    public Result<BuildingStatusVO> buildingStatus(@PathVariable String mapNo) {
        return Result.ok(buildingRatingService.buildingStatus(mapNo));
    }

    @Operation(summary = "楼宇助手余量信息")
    @GetMapping("/building-margin/{mapNo}")
    public Result<BuildingMarginVO> buildingMargin(@PathVariable String mapNo) {
        return Result.ok(buildingRatingService.buildingMargin(mapNo));
    }

    @Operation(summary = "导入AI值")
    @PostMapping("/import/ai")
    public Result<String> importAiData(@RequestParam(name = "file") MultipartFile file) {
        return Result.ok(buildingRatingService.importAiData(file));
    }


    @Operation(summary = "楼宇top等级值")
    @GetMapping("/top-level")
    public Result<String> topLevel(@RequestParam(name = "projectCode") String projectCode) {
        BusinessOpportunityEntity businessOpportunity = businessOpportunityService.lambdaQuery()
                .select(BusinessOpportunityEntity::getBuildingNo)
                .eq(BusinessOpportunityEntity::getCode, projectCode)
                .one();
        if (Objects.isNull(businessOpportunity)) {
            return Result.ok("");
        }
        return Result.ok(buildingRatingService.topLevel(businessOpportunity.getBuildingNo()));
    }

    @Deprecated
    @Operation(summary = "大屏点位系数计算")
    @PostMapping("/large-screen/calculate")
    public Result<String> largeScreenCalculate(@Validated @RequestBody BigScreenCalculateDTO param) {
        return Result.ok(buildingRatingService.largeScreenCalculate(param));
    }

    @Operation(summary = "大屏所有系数")
    @GetMapping("/large-screen/coefficient")
    public Result<List<String>> allCoefficient() {
        return Result.ok(largeScreenProperties.getCoefficient());
    }

    @Operation(summary = "所有大屏设备字典key")
    @GetMapping("/large-screen/large-dict-key")
    public Result<List<String>> allLargeScreenDictKey() {
        return Result.ok(largeScreenProperties.getLargeDictKey());
    }

    @Operation(summary = "获取楼宇位置信息")
    @GetMapping("/detail/{buildingNo}")
    public Result<BuildingVO> getBuildingLocation(@PathVariable String buildingNo) {

        BuildingVO result = buildingRatingService.getBuildingLocation(buildingNo);
        return Result.ok(result);
    }


    @Operation(summary = "校验商机是否完成评级 [0 数据异常  1成功 2未完成大屏 3未完成小屏]")
    @PostMapping("/verify")
    public Result<Integer> isFinishRating(@RequestBody @Validated List<VerifyRatingDTO> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return Result.ok(0);
        }
        return Result.ok(buildingRatingService.isFinishRating(dto));
    }

    @Anonymous
    @Operation(summary = "导出")
    @PostMapping("/export")
    public Result<Void> export(HttpServletResponse response, @RequestBody BuildingRatingExportDTO dto) {
        buildingRatingService.export(response, dto);
        return Result.ok();
    }
}
