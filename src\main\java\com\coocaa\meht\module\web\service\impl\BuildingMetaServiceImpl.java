package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.converter.CodeNameHelper;
import com.coocaa.meht.module.building.entity.CompleteRatingEntity;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.crm.service.CrmCustomerService;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dao.BuildingMetaMapper;
import com.coocaa.meht.module.web.dao.BuildingPropertyCompanyMapper;
import com.coocaa.meht.module.web.dao.PropertyCompanyMapper;
import com.coocaa.meht.module.web.dao.PropertyCompanyPersonMapper;
import com.coocaa.meht.module.web.dto.BuildingMetaDetailDto;
import com.coocaa.meht.module.web.dto.BuildingMetaDto;
import com.coocaa.meht.module.web.dto.BuildingMetaUpdateDto;
import com.coocaa.meht.module.web.dto.ProjectForbidIndustriesDTO;
import com.coocaa.meht.module.web.dto.convert.PropertyCompanyPersonConvert;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaImgRelationEntity;
import com.coocaa.meht.module.web.entity.BuildingPropertyCompanyEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.PropertyCompanyEntity;
import com.coocaa.meht.module.web.entity.PropertyCompanyPersonEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.enums.BuildingMetaCreateTypeEnum;
import com.coocaa.meht.module.web.enums.BuildingMetaImgTypeEnum;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.service.BuildingGeneService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.IBuildingMetaImgRelationService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.vo.BuildingGeneVO;
import com.coocaa.meht.module.web.vo.BuildingPropertyCompanyVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignSspRpc;
import com.coocaa.meht.utils.RsaExample;
import com.coocaa.meht.utils.StringUtils2;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Service
@Slf4j
public class BuildingMetaServiceImpl extends ServiceImpl<BuildingMetaMapper, BuildingMetaEntity> implements IBuildingMetaService {

    @Autowired
    FeignSspRpc feignSspRpc;

    @Autowired
    private BuildingMetaMapper buildingMetaMapper;

    @Autowired
    private SysFileService sysFileService;

    @Autowired
    private IBuildingMetaImgRelationService buildingMetaImgRelationService;


    @Autowired
    private BuildingRatingService buildingRatingService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private PropertyCompanyMapper companyMapper;

    @Autowired
    private PropertyCompanyPersonMapper personMapper;

    @Autowired
    private BuildingPropertyCompanyMapper buildingCompanyMapper;

    @Autowired
    private FeignAuthorityRpc authorityRpc;

    @Autowired
    private RsaExample rsaExample;

    @Autowired
    private BusinessOpportunityService businessOpportunityService;

    @Resource
    private IBuildingMetaImgRelationService imgRelationService;

    @Resource
    private BuildingGeneService buildingGeneService;

    @Resource
    private CodeNameHelper codeNameHelper;

    @Resource
    private CrmCustomerService crmCustomerService;

    @Resource
    private CompleteRatingService completeRatingService;

    @Resource
    private BuildingScreenService buildingScreenService;

    @Override
    public BuildingMetaDetailDto findByBuildRatingNo(String buildingRatingNo) {
        LambdaQueryWrapper<BuildingMetaEntity> wrapper = Wrappers.lambdaQuery(BuildingMetaEntity.class).eq(BuildingMetaEntity::getBuildingRatingNo, buildingRatingNo);
        BuildingMetaEntity metaEntity = baseMapper.selectOne(wrapper);
        BuildingMetaDetailDto buildingMetaDetailDto = new BuildingMetaDetailDto();
        if (ObjectUtil.isNull(metaEntity)) {
            return null;
        }
        //楼宇基础信息
        buildingMetaDetailDto.setBuildingName(metaEntity.getBuildingName());
        buildingMetaDetailDto.setId(metaEntity.getId());
        buildingMetaDetailDto.setBuildingRatingNo(metaEntity.getBuildingRatingNo());
        buildingMetaDetailDto.setMapLongitude(metaEntity.getMapLongitude());
        buildingMetaDetailDto.setMapLatitude(metaEntity.getMapLatitude());
        buildingMetaDetailDto.setMapAddress(metaEntity.getMapAddress());
        buildingMetaDetailDto.setManager(metaEntity.getManager());
        buildingMetaDetailDto.setBuildingType(metaEntity.getBuildingType());
        buildingMetaDetailDto.setMapCity(metaEntity.getMapCity());
        buildingMetaDetailDto.setMapRegion(metaEntity.getMapRegion());
        buildingMetaDetailDto.setBuildingMetaNo(metaEntity.getBuildingMetaNo());
        if (StringUtils.isNotBlank(metaEntity.getForbiddenIndustry())) {
            buildingMetaDetailDto.setForbiddenIndustry(Arrays.asList(metaEntity.getForbiddenIndustry().split(",")));
        }
        return buildingMetaDetailDto;
    }

    @Override
    public BuildingMetaDetailDto getDetail(String buildingNo) {
        BuildingRatingEntity buildingRatingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo)
                .one();
        if (ObjectUtil.isNull(buildingRatingEntity)) {
            return null;
        }
        BuildingMetaDetailDto buildingMetaDetailDto = new BuildingMetaDetailDto();
        BuildingMetaEntity buildingMetaEntity = buildingMetaMapper.selectOne(Wrappers.<BuildingMetaEntity>lambdaQuery().eq(BuildingMetaEntity::getMapNo, buildingRatingEntity.getMapNo()));
        if (ObjectUtil.isNull(buildingMetaEntity)) {
            return null;
        }

        // 物业信息
        buildingMetaDetailDto.setPropertyCompanyVOS(getPropertyCompany(buildingRatingEntity.getBuildingNo()));

        // 楼宇评级id
        buildingMetaDetailDto.setBuildingRatingId(buildingRatingEntity.getId());
        // 楼宇评级状态
        buildingMetaDetailDto.setStatus(buildingRatingEntity.getStatus());
        buildingMetaDetailDto.setBuildingNo(buildingRatingEntity.getBuildingNo());
        buildingMetaDetailDto.setRatingVersion(buildingRatingEntity.getRatingVersion());

        //楼宇基础信息
        buildingMetaDetailDto.setBuildingNameAi(buildingMetaEntity.getBuildingNameAi());
        buildingMetaDetailDto.setId(buildingMetaEntity.getId());
        buildingMetaDetailDto.setBuildingMetaNo(buildingMetaEntity.getBuildingMetaNo());
        buildingMetaDetailDto.setCreateBy(buildingRatingEntity.getSubmitUser());
        buildingMetaDetailDto.setBuildingType(buildingMetaEntity.getBuildingType());
        buildingMetaDetailDto.setBuildingName(buildingMetaEntity.getBuildingName());
        buildingMetaDetailDto.setProjectLevelAi(buildingMetaEntity.getProjectLevelAi());
        buildingMetaDetailDto.setCompetitorPointCount(buildingMetaEntity.getCompetitorPointCount());
        buildingMetaDetailDto.setTargetPointCount(buildingMetaEntity.getTargetPointCount());
        buildingMetaDetailDto.setBuildingTotalNumber(buildingMetaEntity.getBuildingTotalNumber());
        buildingMetaDetailDto.setHallTotalNumber(buildingMetaEntity.getHallTotalNumber());
        buildingMetaDetailDto.setFloorTotalNumber(buildingMetaEntity.getFloorTotalNumber());
        buildingMetaDetailDto.setUnitsTotalNumber(buildingMetaEntity.getUnitsTotalNumber());
        buildingMetaDetailDto.setBuildingRatingNo(buildingMetaEntity.getBuildingRatingNo());
        buildingMetaDetailDto.setProjectLevel(buildingMetaEntity.getProjectLevel());
        // 翻译行业
        if (StringUtils.isNotBlank(buildingMetaEntity.getForbiddenIndustry())) {
            if (buildingMetaEntity.getForbiddenIndustry().equals("无")){
                List<String> list = Arrays.asList(buildingMetaEntity.getForbiddenIndustry().split(","));
                buildingMetaDetailDto.setForbiddenIndustry(list);
                buildingMetaDetailDto.setForbiddenIndustryName("无");
            }else {
                List<String> list = Arrays.asList(buildingMetaEntity.getForbiddenIndustry().split(","));
                buildingMetaDetailDto.setForbiddenIndustry(list);
                List<CodeNameVO> codeNameVOS = RpcUtils.unBox(authorityRpc.listIndustryByCodes(list));
                String forbiddenIndustryName = codeNameVOS.stream().map(CodeNameVO::getName).collect(Collectors.joining(","));
                buildingMetaDetailDto.setForbiddenIndustryName(forbiddenIndustryName);
            }

        } else {
            buildingMetaDetailDto.setForbiddenIndustry(Collections.emptyList());
            buildingMetaDetailDto.setForbiddenIndustryName("");
        }
        //获取提交人信息
        if (StringUtils.isNotBlank(buildingMetaEntity.getManager())) {
            LoginUser user = sysUserService.getUserByEmpCode(buildingRatingEntity.getSubmitUser());
            if (ObjectUtil.isNotNull(user)) {
                buildingMetaDetailDto.setManager(user.getUserCode());
                buildingMetaDetailDto.setManagerName(user.getUserName());
            }
        }


        List<BuildingMetaImgRelationEntity> buildingMetaImgRelationEntityList = buildingMetaImgRelationService.list(Wrappers.<BuildingMetaImgRelationEntity>lambdaQuery().eq(BuildingMetaImgRelationEntity::getBuildingMetaNo, buildingMetaEntity.getBuildingMetaNo()));
        if (CollectionUtils.isNotEmpty(buildingMetaImgRelationEntityList)) {
            List<String> buildingExteriorPic = new ArrayList<>();
            List<String> buildingLobbyPic = new ArrayList<>();
            List<String> buildingHallPic = new ArrayList<>();
            List<String> buildingElevatorPic = new ArrayList<>();
            List<String> buildingGatePic = new ArrayList<>();
            List<String> buildingInstallationPic = new ArrayList<>();
            buildingMetaImgRelationEntityList.forEach(entity -> {
                switch (entity.getImgType()) {
                    case 1:
                        buildingExteriorPic.add(entity.getImgUrl());
                        break;
                    case 2:
                        buildingLobbyPic.add(entity.getImgUrl());
                        break;
                    case 3:
                        buildingHallPic.add(entity.getImgUrl());
                        break;
                    case 5:
                        buildingElevatorPic.add(entity.getImgUrl());
                        break;
                    case 6:
                        buildingGatePic.add(entity.getImgUrl());
                        break;
                    case 7:
                        buildingInstallationPic.add(entity.getImgUrl());
                        break;
                    default:
                        break;
                }
            });
            buildingMetaDetailDto.setBuildingExteriorPic(buildingExteriorPic);
            buildingMetaDetailDto.setBuildingLobbyPic(buildingLobbyPic);
            buildingMetaDetailDto.setBuildingHallPic(buildingHallPic);
            buildingMetaDetailDto.setBuildingElevatorPic(buildingElevatorPic);
            buildingMetaDetailDto.setBuildingGatePic(buildingGatePic);
            buildingMetaDetailDto.setBuildingInstallationPic(buildingInstallationPic);
        } else {
            List<String> pics = new ArrayList<>();
            buildingMetaDetailDto.setBuildingExteriorPic(pics);
            buildingMetaDetailDto.setBuildingLobbyPic(pics);
            buildingMetaDetailDto.setBuildingHallPic(pics);
            buildingMetaDetailDto.setBuildingElevatorPic(pics);
            buildingMetaDetailDto.setBuildingGatePic(pics);
            buildingMetaDetailDto.setBuildingInstallationPic(pics);
        }
        buildingMetaDetailDto.setMapAddress(rsaExample.decryptByPrivate(buildingMetaEntity.getMapAddress()));
        buildingMetaDetailDto.setBuildingNameAi(buildingMetaDetailDto.getBuildingNameAi());
        BuildingGeneVO buildingGene = buildingGeneService.getBuildingGeneByNo(buildingRatingEntity.getBuildingNo());
        translate(buildingGene);
        buildingMetaDetailDto.setBuildingGene(buildingGene);
        buildingMetaDetailDto.setLargeScreen(buildingScreenService.isLargeScreen(buildingRatingEntity));
        buildingMetaDetailDto.setImproveRatingFlag(isImproveRatingFlag(buildingRatingEntity));
        buildingMetaDetailDto.setCompleteRatingNo(getCompleteRatingNo(buildingRatingEntity));
        return buildingMetaDetailDto;
    }

    private String getCompleteRatingNo(BuildingRatingEntity buildingRatingEntity) {

        CompleteRatingEntity completeRating = completeRatingService.lambdaQuery()
                .select(CompleteRatingEntity::getCompleteRatingNo)
                .eq(CompleteRatingEntity::getBuildingRatingNo, buildingRatingEntity.getBuildingNo())
                .eq(CompleteRatingEntity::getStatus, BuildingRatingEntity.Status.DRAFT.getValue())
                .one();
        if (Objects.nonNull(completeRating)) {
            return completeRating.getCompleteRatingNo();
        }
        return null;

    }

    private boolean isImproveRatingFlag(BuildingRatingEntity buildingRatingEntity) {
        // 审核中完善评级数据
        Map<String, String> waitAuditCompleteMapping = completeRatingService.lambdaQuery()
                .select(CompleteRatingEntity::getBuildingRatingNo, CompleteRatingEntity::getStatus, CompleteRatingEntity::getCompleteRatingNo)
                .eq(CompleteRatingEntity::getBuildingRatingNo, buildingRatingEntity.getBuildingNo())
                .eq(CompleteRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.getValue())
                .list().stream()
                .collect(Collectors.toMap(
                        CompleteRatingEntity::getBuildingRatingNo,
                        CompleteRatingEntity::getCompleteRatingNo
                ));
        return crmCustomerService.isImproveRatingFlag(buildingRatingEntity, waitAuditCompleteMapping);
    }

    private void translate(BuildingGeneVO buildingGene) {
        if (buildingGene != null) {
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(buildingGene.getCompetitiveMediaInfos())) {
                // 按逗号截取
                Map<String, String> mediaTypeDictMapping = codeNameHelper.getDictMapping(buildingGene.getCompetitiveMediaInfos());
                // 使用String.join方法进行字符串拼接
                String res = String.join("，", mediaTypeDictMapping.values());
                buildingGene.setCompetitiveMediaInfoName(res);
            }
        }
    }

    @Override
    public BuildingMetaDto getBasicInfo(String buildingNo) {
        BuildingMetaDto buildingMetaDto = new BuildingMetaDto();
        // 1.入参非空校验
        if (StringUtils.isBlank(buildingNo)) {
            return buildingMetaDto;
        }
        // 2.查询rating表
        BuildingRatingEntity buildingRatingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo)
                .one();
        if (ObjectUtil.isNull(buildingRatingEntity)) {
            return buildingMetaDto;
        }
        // 3.根据rating表查询meta表
        BuildingMetaEntity buildingMetaEntity = buildingMetaMapper.selectOne(Wrappers.<BuildingMetaEntity>lambdaQuery()
                .eq(BuildingMetaEntity::getMapNo, buildingRatingEntity.getMapNo()));
        if (ObjectUtil.isNull(buildingMetaEntity)) {
            return buildingMetaDto;
        }
        // 4.返回结果
        BeanUtils.copyProperties(buildingMetaEntity, buildingMetaDto);
        buildingMetaDto.setMapAddress(rsaExample.decryptByPrivate(buildingMetaDto.getMapAddress()));
        return buildingMetaDto;
    }


    public List<BuildingPropertyCompanyVO> getPropertyCompany(String buildingNo) {
        List<BuildingPropertyCompanyVO> resultVOS = new ArrayList<>();
        // code存在则查该商机下物业，如果有客户id则查客户下所有物业
        // 商机物业关联表
        List<BuildingPropertyCompanyEntity> entities = buildingCompanyMapper.selectList(new LambdaQueryWrapper<BuildingPropertyCompanyEntity>()
                .eq(StringUtils.isNotBlank(buildingNo), BuildingPropertyCompanyEntity::getBuildingNo, buildingNo));
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        // 物业
        List<Integer> buildingCompanyIds = entities.stream().map(BuildingPropertyCompanyEntity::getPropertyId).toList();
        Map<Integer, PropertyCompanyEntity> companyMap = companyMapper.selectList(new LambdaQueryWrapper<PropertyCompanyEntity>()
                        .eq(PropertyCompanyEntity::getStatus, BooleFlagEnum.YES.getCode())
                        .in(PropertyCompanyEntity::getId, buildingCompanyIds)).stream()
                .collect(Collectors.toMap(PropertyCompanyEntity::getId, c -> c));
        if (CollectionUtils.isEmpty(companyMap)) {
            return Collections.emptyList();
        }
        // 物业联系人
        Map<Integer, List<PropertyCompanyPersonEntity>> personMap = personMapper.selectList(new LambdaQueryWrapper<PropertyCompanyPersonEntity>()
                        .in(PropertyCompanyPersonEntity::getCompanyId, companyMap.keySet())).stream()
                .collect(Collectors.groupingBy(PropertyCompanyPersonEntity::getCompanyId));
        // 组装
        entities.forEach(entity -> {
            if (Objects.nonNull(companyMap.get(entity.getPropertyId()))) {
                BuildingPropertyCompanyVO resultVO = new BuildingPropertyCompanyVO();
                BeanUtils.copyProperties(entity, resultVO);
                BeanUtils.copyProperties(companyMap.get(entity.getPropertyId()), resultVO);
                resultVO.setPersonVOS(PropertyCompanyPersonConvert.INSTANCE.toVOs(personMap.getOrDefault(entity.getPropertyId(), Collections.emptyList())));
                resultVOS.add(resultVO);
            }
        });
        return resultVOS;
    }

    @Transactional
    @Override
    public boolean updateBuildMeta(BuildingMetaUpdateDto dto) {
        BuildingMetaEntity buildingMetaEntity = this.lambdaQuery().eq(BuildingMetaEntity::getBuildingMetaNo, dto.getBuildingMetaNo()).one();
        if (ObjectUtil.isNull(buildingMetaEntity)) {
            throw new ServerException("楼宇信息不存在");
        }
        List<BuildingMetaImgRelationEntity> buildingMetaImgRelationEntityList = new ArrayList<>();
        buildingMetaImgRelationService.remove(Wrappers.<BuildingMetaImgRelationEntity>lambdaQuery()
                .in(BuildingMetaImgRelationEntity::getImgType, List.of(BuildingMetaImgTypeEnum.EXTERIOR_PIC.getType()
                        , BuildingMetaImgTypeEnum.LOBBY_PIC.getType(), BuildingMetaImgTypeEnum.HALL_PIC.getType(),
                        BuildingMetaImgTypeEnum.ELEVATOR_PIC.getType(), BuildingMetaImgTypeEnum.GATE_PIC.getType(),
                        BuildingMetaImgTypeEnum.INSTALL_PIC.getType(),
                        BuildingMetaImgTypeEnum.ELEVATOR_PIC.getType(), BuildingMetaImgTypeEnum.GATE_PIC.getType(),
                        BuildingMetaImgTypeEnum.INSTALL_PIC.getType()))
                .eq(BuildingMetaImgRelationEntity::getBuildingMetaNo, dto.getBuildingMetaNo()));
        //外墙材料附件地址
        if (CollectionUtil.isNotEmpty(dto.getBuildingExteriorPic())) {
            dto.getBuildingExteriorPic().forEach(entity -> {
                BuildingMetaImgRelationEntity imgEntity = new BuildingMetaImgRelationEntity();
                imgEntity.setImgUrl(entity);
                imgEntity.setImgType(BuildingMetaImgTypeEnum.EXTERIOR_PIC.getType());
                buildingMetaImgRelationEntityList.add(imgEntity);
            });
        }

        //大堂高度及装饰附件地址
        if (CollectionUtil.isNotEmpty(dto.getBuildingLobbyPic())) {
            dto.getBuildingLobbyPic().forEach(entity -> {
                BuildingMetaImgRelationEntity imgEntity = new BuildingMetaImgRelationEntity();
                imgEntity.setImgUrl(entity);
                imgEntity.setImgType(BuildingMetaImgTypeEnum.LOBBY_PIC.getType());
                buildingMetaImgRelationEntityList.add(imgEntity);
            });
        }
        //楼梯厅装饰附件地址
        if (CollectionUtil.isNotEmpty(dto.getBuildingHallPic())) {
            dto.getBuildingHallPic().forEach(entity -> {
                BuildingMetaImgRelationEntity imgEntity = new BuildingMetaImgRelationEntity();
                imgEntity.setImgUrl(entity);
                imgEntity.setImgType(BuildingMetaImgTypeEnum.HALL_PIC.getType());
                buildingMetaImgRelationEntityList.add(imgEntity);
            });
        }
        //梯厅环境图附件地址
        if (CollectionUtil.isNotEmpty(dto.getBuildingElevatorPic())) {
            dto.getBuildingElevatorPic().forEach(entity -> {
                BuildingMetaImgRelationEntity imgEntity = new BuildingMetaImgRelationEntity();
                imgEntity.setImgUrl(entity);
                imgEntity.setImgType(BuildingMetaImgTypeEnum.ELEVATOR_PIC.getType());
                buildingMetaImgRelationEntityList.add(imgEntity);
            });
        }
        //闸口图附件地址
        if (CollectionUtil.isNotEmpty(dto.getBuildingGatePic())) {
            dto.getBuildingGatePic().forEach(entity -> {
                BuildingMetaImgRelationEntity imgEntity = new BuildingMetaImgRelationEntity();
                imgEntity.setImgUrl(entity);
                imgEntity.setImgType(BuildingMetaImgTypeEnum.GATE_PIC.getType());
                buildingMetaImgRelationEntityList.add(imgEntity);
            });
        }
        //安装示意图附件地址
        if (CollectionUtil.isNotEmpty(dto.getBuildingInstallationPic())) {
            dto.getBuildingInstallationPic().forEach(entity -> {
                BuildingMetaImgRelationEntity imgEntity = new BuildingMetaImgRelationEntity();
                imgEntity.setImgUrl(entity);
                imgEntity.setImgType(BuildingMetaImgTypeEnum.INSTALL_PIC.getType());
                buildingMetaImgRelationEntityList.add(imgEntity);
            });
        }


        if (CollectionUtil.isNotEmpty(buildingMetaImgRelationEntityList)) {
            buildingMetaImgRelationEntityList.forEach(entity -> {
                entity.setBuildingMetaNo(dto.getBuildingMetaNo());
                entity.setCreateBy(SecurityUser.getUserCode());
                entity.setCreateTime(new Date());
            });
            buildingMetaImgRelationService.saveBatch(buildingMetaImgRelationEntityList);
        }
        BuildingMetaEntity metaEntity = dto2Entity(dto);
        boolean b = this.updateById(metaEntity);

        // 修改了楼宇信息  同步修改商机（待洽谈，初步洽谈，达成意向，方案报价）中的项目名称
        updateBuildingRatingAndBusiness(buildingMetaEntity, metaEntity);
        if (b && !metaEntity.getForbiddenIndustry().equals("无") ) {
            //修改成功则查询楼宇是否有对应的项目，无则不处理，有则修改项目对应的禁忌行业
            ProjectForbidIndustriesDTO pDto = ProjectForbidIndustriesDTO.builder()
                    .forbidIndustry(metaEntity.getForbiddenIndustry())
                    .buildingNo(buildingMetaEntity.getBuildingRatingNo()).build();
            RpcUtils.unBox(feignSspRpc.updateProjectForbidIndustries(pDto));
        }
        BuildingGeneEntity gene = dto.getBuildingGene();
        if (gene != null && gene.getId() != null) {
            if (CollectionUtils.isNotEmpty(gene.getCompetitiveMediaInfos())) {
                String mediaInfo = String.join(",", gene.getCompetitiveMediaInfos());
                gene.setCompetitiveMediaInfo(mediaInfo);
            }
            if (StrUtil.isNotBlank(metaEntity.getForbiddenIndustry())) {
                gene.setForbiddenIndustry(metaEntity.getForbiddenIndustry());
            }
            buildingGeneService.updateById(dto.getBuildingGene());
        }
        if (gene != null && gene.getId() == null && StrUtil.isNotBlank(gene.getBuildingRatingNo())) {
            if (CollectionUtils.isNotEmpty(gene.getCompetitiveMediaInfos())) {
                String mediaInfo = String.join(",", gene.getCompetitiveMediaInfos());
                gene.setCompetitiveMediaInfo(mediaInfo);
            }
            if (StrUtil.isNotBlank(metaEntity.getForbiddenIndustry())) {
                gene.setForbiddenIndustry(metaEntity.getForbiddenIndustry());
            }
            buildingGeneService.save(gene);
        }
        return b;
    }

    private void updateBuildingRatingAndBusiness(BuildingMetaEntity buildingMetaEntity, BuildingMetaEntity metaEntity) {
        if (StringUtils.isNotEmpty(buildingMetaEntity.getBuildingName())
                && !buildingMetaEntity.getBuildingName().equals(metaEntity.getBuildingName())) {
            buildingRatingService.lambdaUpdate().set(BuildingRatingEntity::getBuildingName, metaEntity.getBuildingName())
                    .eq(BuildingRatingEntity::getBuildingNo, buildingMetaEntity.getBuildingRatingNo()).update();
            List<String> updateStatus = Arrays.asList(BusinessChangeStatusEnum.TO_BE_DISCUSSED.getCode(),
                    BusinessChangeStatusEnum.PRELIMINARY_NEGOTIATIONS.getCode(),
                    BusinessChangeStatusEnum.REACHING_INTENTION.getCode(),
                    BusinessChangeStatusEnum.PROPOSAL_QUOTATION.getCode());
            List<BusinessOpportunityEntity> opportunityEntityList = businessOpportunityService.lambdaQuery()
                    .eq(BusinessOpportunityEntity::getBuildingNo, buildingMetaEntity.getBuildingRatingNo())
                    .in(BusinessOpportunityEntity::getStatus, updateStatus).list();
            List<BusinessOpportunityEntity> updateList = opportunityEntityList.stream().map(opportunityEntity -> {
                String originalName = opportunityEntity.getName();
                String newName = metaEntity.getBuildingName();
                int separatorIndex = originalName.lastIndexOf('-');
                if (separatorIndex != -1) {
                    newName += originalName.substring(separatorIndex);
                } else {
                    log.info("商机名称'{}'中没有'-'，商机名称将直接更新为楼宇名称'{}'", originalName, newName);
                }
                BusinessOpportunityEntity entity = new BusinessOpportunityEntity();
                entity.setId(opportunityEntity.getId());
                entity.setName(newName);
                return entity;
            }).collect(Collectors.toList());
            businessOpportunityService.updateBatchById(updateList);
        }
    }

    public BuildingMetaEntity dto2Entity(BuildingMetaUpdateDto dto) {
        BuildingMetaEntity entity = new BuildingMetaEntity();
        entity.setId(dto.getId());
        entity.setBuildingName(dto.getBuildingName());
        entity.setCompetitorPointCount(dto.getCompetitorPointCount());
        entity.setTargetPointCount(dto.getTargetPointCount());
        entity.setUpdateBy(SecurityUser.getUserCode());
        entity.setUpdateTime(new Date());
        entity.setBuildingTotalNumber(dto.getBuildingTotalNumber());
        entity.setHallTotalNumber(dto.getHallTotalNumber());
        entity.setFloorTotalNumber(dto.getFloorTotalNumber());
        entity.setUnitsTotalNumber(dto.getUnitsTotalNumber());
        if (dto.getForbiddenIndustry().size() == 1 && "无".equals(dto.getForbiddenIndustry().get(0))) {
            entity.setForbiddenIndustry("无");
        } else {
            //如果包含了无，并且长度大于1，抛出异常
            if (dto.getForbiddenIndustry().contains("无") && dto.getForbiddenIndustry().size() > 1) {
                throw new ServerException("禁止行业选择无不能再选其他的行业");
            }
            entity.setForbiddenIndustry(String.join(",", dto.getForbiddenIndustry()));
        }
        return entity;
    }

    @Override
    public void saveOrUpdate(BuildingRatingEntity newEntity, boolean isLargeScreen, boolean isSubmit) {
        String userCode = UserThreadLocal.getUser().getWno();

        String mapNo = newEntity.getMapNo();
        BuildingMetaEntity buildingMetaEntity = getByMapNo(mapNo);
        String metaNo;
        boolean saveFlag = true;
        if (Objects.nonNull(buildingMetaEntity)) {
            metaNo = buildingMetaEntity.getBuildingMetaNo();
            saveFlag = false;
        } else {
            buildingMetaEntity = new BuildingMetaEntity();
            metaNo = getBuildingMetaNo();
            buildingMetaEntity.setCreateBy(userCode);

            // 新建时设置值，后续撤回重提，编辑评级不再修改
            buildingMetaEntity.setBuildingTypeAi(newEntity.getBuildingType());
            buildingMetaEntity.setBuildingNameAi(newEntity.getBuildingName());
        }

        if (isSubmit) {
            // 楼宇评级提交设置状态为认证中
            buildingMetaEntity.setBuildingStatus(BuildingRatingEntity.BuildingStatus.CONFIRMING.getValue());
        }

        buildingMetaEntity.setUpdateBy(userCode);
        buildingMetaEntity.setBuildingType(newEntity.getBuildingType());
        buildingMetaEntity.setBuildingName(newEntity.getBuildingName());
        buildingMetaEntity.setMapLongitude(newEntity.getMapLongitude());
        buildingMetaEntity.setMapLatitude(newEntity.getMapLatitude());
        buildingMetaEntity.setMapCity(newEntity.getMapCity());
        buildingMetaEntity.setMapAdCode(newEntity.getMapAdCode());
        buildingMetaEntity.setMapProvince(newEntity.getMapProvince());
        buildingMetaEntity.setMapAddress(newEntity.getMapAddress());
        buildingMetaEntity.setMapRegion(newEntity.getMapRegion());
        buildingMetaEntity.setBuildingScore(newEntity.getBuildingScore());
        buildingMetaEntity.setProjectLevel(StrUtil.isNotBlank(newEntity.getProjectReviewLevel())
                ? newEntity.getProjectReviewLevel() : newEntity.getProjectLevel());
        buildingMetaEntity.setSuccessTime(newEntity.getApproveTime());
        buildingMetaEntity.setBuildingRatingNo(newEntity.getBuildingNo());
        buildingMetaEntity.setManager(newEntity.getSubmitUser());
        buildingMetaEntity.setTargetPointCount(newEntity.getTargetPointCount());
        buildingMetaEntity.setUpdateBy(userCode);
        buildingMetaEntity.setBuildingRatingNo(newEntity.getBuildingNo());
        buildingMetaEntity.setProjectLevelAi(newEntity.getProjectAiLevel());
        buildingMetaEntity.setBuildingAiScore(newEntity.getBuildingAiScore());

        //需要插入数据
        buildingMetaEntity.setBuildingMetaNo(metaNo);
        buildingMetaEntity.setMapNo(mapNo);
        buildingMetaEntity.setCreateType(BuildingMetaCreateTypeEnum.PERSONAL.getCode());

        //存储图片
        imgRelationService.remove(Wrappers.<BuildingMetaImgRelationEntity>lambdaQuery()
                .eq(BuildingMetaImgRelationEntity::getBuildingMetaNo, metaNo));

        List<BuildingMetaImgRelationEntity> imgList = new ArrayList<>();
        //楼盘大堂
        String buildingLobbyPic = newEntity.getBuildingLobbyPic();
        imgList.addAll(saveMetaImg(buildingLobbyPic, 2, metaNo));
        //侯梯厅
        String buildingHallPic = newEntity.getBuildingHallPic();
        imgList.addAll(saveMetaImg(buildingHallPic, 3, metaNo));
        //外墙材料
        String buildingExteriorPic = newEntity.getBuildingExteriorPic();
        imgList.addAll(saveMetaImg(buildingExteriorPic, 1, metaNo));

        //大屏处理
        if (isLargeScreen) {
            String buildingLobbyEnvPic = newEntity.getBuildingLobbyEnvPic();
            imgList.addAll(saveMetaImg(buildingLobbyEnvPic, 4, metaNo));

            String buildingElevatorPic = newEntity.getBuildingElevatorPic();
            imgList.addAll(saveMetaImg(buildingElevatorPic, 5, metaNo));

            String buildingGatePic = newEntity.getBuildingGatePic();
            imgList.addAll(saveMetaImg(buildingGatePic, 6, metaNo));

            String buildingInstallationPic = newEntity.getBuildingInstallationPic();
            imgList.addAll(saveMetaImg(buildingInstallationPic, 7, metaNo));

        }

        if (ObjectUtil.isNotEmpty(imgList)) {
            imgRelationService.saveBatch(imgList);
        }

        if (saveFlag) {
            //需要插入数据
            save(buildingMetaEntity);
        } else {
            //修改数据
            updateById(buildingMetaEntity);
        }
    }


    public List<BuildingMetaImgRelationEntity> saveMetaImg(String ids, Integer imgType, String buildingMetaNo) {
        List<BuildingMetaImgRelationEntity> imgRelationEntityList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(ids)) {
            List<String> list = Arrays.asList(ids.split(","));
            List<Long> fileIds = list.stream().map(a -> Long.valueOf(a)).collect(Collectors.toList());
            List<SysFileEntity> bySysFileIdList = sysFileService.getBySysFileIdList(fileIds);
            if (ObjectUtil.isNotEmpty(bySysFileIdList)) {
                bySysFileIdList.stream().forEach(file -> {
                    BuildingMetaImgRelationEntity entity = new BuildingMetaImgRelationEntity();
                    entity.setImgUrl(file.getUrl());
                    entity.setImgType(imgType);
                    entity.setCreateBy(SecurityUser.getUserCode());
                    entity.setBuildingMetaNo(buildingMetaNo);
                    imgRelationEntityList.add(entity);
                });
            }
        }
        return imgRelationEntityList;
    }

    @Override
    public String getBuildingMetaNo() {
        String localDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String key = "meta:id:" + localDate;
        Long increment = redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
        return "BC" + localDate + StringUtils2.file5Code(increment.intValue());
    }

    @Override
    public BuildingMetaEntity getByMapNo(String mapNo) {
        return lambdaQuery().eq(BuildingMetaEntity::getMapNo, mapNo)
                .one();
    }

    /**
     * 楼宇状态：0待审核，1已审核 2已驳回 3审核不通过
     */
    @Override
    public void updateStatus(String buildingNo, Integer status) {
        BuildingRatingEntity buildingRatingEntity = buildingRatingService.getByBuildingNo(buildingNo);
        String mapNo = buildingRatingEntity.getMapNo();
        String userCode = SecurityUser.getUserCode();
        if (status == 3) {
            LambdaUpdateWrapper<BuildingMetaEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(BuildingMetaEntity::getMapNo, mapNo)
                    .set(BuildingMetaEntity::getBuildingStatus, BuildingRatingEntity.BuildingStatus.UN_CONFIRM.getValue())
                    .set(BuildingMetaEntity::getUpdateBy, userCode)
                    .set(BuildingMetaEntity::getManager, null)
                    .set(BuildingMetaEntity::getProjectLevel, "")
                    .set(BuildingMetaEntity::getBuildingScore, null);
            update(wrapper);

            businessOpportunityService.lambdaUpdate()
                    .set(BusinessOpportunityEntity::getStatus, BusinessChangeStatusEnum.CLOSE.getCode())
                    .eq(BusinessOpportunityEntity::getBuildingNo, buildingRatingEntity.getBuildingNo())
                    .update();

            businessOpportunityService.updateFollowRecord(buildingRatingEntity.getBuildingNo());

        } else if (status == 1) {
            BuildingMetaEntity buildingMetaEntity = new BuildingMetaEntity();
            buildingMetaEntity.setBuildingStatus(BuildingRatingEntity.BuildingStatus.CONFIRMED.getValue());
            buildingMetaEntity.setSuccessTime(buildingRatingEntity.getApproveTime());
            LambdaUpdateWrapper<BuildingMetaEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(BuildingMetaEntity::getMapNo, mapNo);
            update(buildingMetaEntity, wrapper);
        }
    }

    @Override
    public List<BuildingMetaEntity> listByBuildingNos(Collection<String> buildingNos) {
        if (CollectionUtil.isNotEmpty(buildingNos)) {
            return lambdaQuery().in(BuildingMetaEntity::getBuildingRatingNo, buildingNos).list();
        }
        return new ArrayList<>();
    }

    @Override
    public void updateBuildMetaUnConfirmStatus(String mapNo) {
        LambdaUpdateWrapper<BuildingMetaEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BuildingMetaEntity::getMapNo, mapNo)
                .set(BuildingMetaEntity::getBuildingStatus, 0)
                .set(BuildingMetaEntity::getManager, null)
                .set(BuildingMetaEntity::getProjectLevel, "")
                .set(BuildingMetaEntity::getBuildingScore, null);
        update(updateWrapper);
    }

    /**
     * 获取楼宇元数据图片关系，并按图片类型分组
     *
     * @param mapNo   楼宇编码
     * @param imgType 图片类型列表
     * @return 按图片类型分组的关系数据集合
     */
    @Override
    public Map<String, List<BuildingMetaImgRelationEntity>> metaPic(String mapNo, List<Integer> imgType) {
        // 参数校验，如果楼宇编码或图片类型为空，返回空集合
        if (ObjectUtil.isEmpty(mapNo) || ObjectUtil.isEmpty(imgType)) {
            return Map.of();
        }
        BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(mapNo);
        if (buildingRating == null) {
            return Map.of();
        }
        if (!(0 == buildingRating.getBuildingType())) {
            imgType = List.of(1, 2, 3);
        }
        // 1. 从数据库查询关系数据
        BuildingMetaEntity buildingMetaEntity = buildingMetaMapper.selectOne(Wrappers.<BuildingMetaEntity>lambdaQuery().eq(BuildingMetaEntity::getMapNo, buildingRating.getMapNo()));
        if (buildingMetaEntity == null) {
            return Map.of();
        }
        List<BuildingMetaImgRelationEntity> relationList = buildingMetaImgRelationService.list(Wrappers.<BuildingMetaImgRelationEntity>lambdaQuery()
                .in(BuildingMetaImgRelationEntity::getImgType, imgType)
                .eq(BuildingMetaImgRelationEntity::getBuildingMetaNo, buildingMetaEntity.getBuildingMetaNo()));

        if (CollectionUtil.isEmpty(relationList)) {
            return Map.of();
        }
        // 2. 按图片类型分组并返回结果
        Map<String, List<BuildingMetaImgRelationEntity>> res = new HashMap<>();
        res.put("buildingExteriorPic", Collections.emptyList());
        res.put("buildingLobbyPic", Collections.emptyList());
        res.put("buildingHallPic", Collections.emptyList());
        Map<Integer, List<BuildingMetaImgRelationEntity>> map = relationList.stream()
                .collect(Collectors.groupingBy(BuildingMetaImgRelationEntity::getImgType));

        map.forEach((k, v) -> {
            if (k.equals(1)) {
                res.put("buildingExteriorPic", v);
            } else if (k.equals(2)) {
                res.put("buildingLobbyPic", v);
            } else {
                res.put("buildingHallPic", v);
            }
        });
        return res;
    }

    @Override
    public void fillAiData(String buildingNo, BigDecimal buildingAiScore, String projectAiLevel) {
        if (StrUtil.isBlank(buildingNo)) {
            return;
        }

        LambdaUpdateWrapper<BuildingMetaEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BuildingMetaEntity::getBuildingRatingNo, buildingNo)
                .set(BuildingMetaEntity::getProjectLevelAi, projectAiLevel)
                .set(BuildingMetaEntity::getBuildingAiScore, buildingAiScore);
        update(updateWrapper);
    }

}

