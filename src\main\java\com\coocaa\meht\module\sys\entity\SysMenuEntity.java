package com.coocaa.meht.module.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * 菜单管理
 */
@Data
@Accessors(chain = true)
@TableName("sys_menu")
public class SysMenuEntity extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 父ID
     */
    private Long parentId;
    /**
     * 菜单名称
     */
    @TableField("`name`")
    private String name;
    /**
     * 菜单URL
     */
    private String url;
    /**
     * 授权标识
     */
    private String authority;
    /**
     * 类型：0目录，1菜单，2按钮
     */
    @TableField("`type`")
    private Integer type;
    /**
     * 菜单图标
     */
    private String icon;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 打开方式：0内部，1外部
     */
    private Integer openStyle;
    /**
     * 删除标识
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        SysMenuEntity that = (SysMenuEntity) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), id);
    }
}