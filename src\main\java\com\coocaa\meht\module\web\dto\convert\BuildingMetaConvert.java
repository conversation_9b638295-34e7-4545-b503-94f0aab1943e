package com.coocaa.meht.module.web.dto.convert;

import com.coocaa.meht.module.dataimport.pojo.BasicInfoAllVO;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import org.mapstruct.Mapper;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/12
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface BuildingMetaConvert {
    BuildingMetaConvert INSTANCE = Mappers.getMapper(BuildingMetaConvert.class);
    List<BasicInfoAllVO> toBasicInfoAllVoList(List<BuildingMetaEntity> buildingMetaEntities);
}
