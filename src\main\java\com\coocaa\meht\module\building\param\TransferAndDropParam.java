package com.coocaa.meht.module.building.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TransferAndDropParam {
    @Schema(description = "楼宇编号")
    private List<String> buildingNos;

    @Schema(description = "用户编号")
    private String userCode;
}
