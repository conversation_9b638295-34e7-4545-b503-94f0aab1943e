package com.coocaa.meht.module.building.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("building_screen")
public class BuildingScreenEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 评分编号
     */
    private String buildingRatingNo;

    /**
     * 规格
     */
    private String spec;

    /**
     * 总层数
     */
    private Integer totalBuildingCount;

    /**
     * 入驻企业数量
     */
    private Integer companyCount;

    /**
     * 电梯数量
     */
    private Integer elevatorCount;

    /**
     * 间距
     */
    private BigDecimal buildingSpacing;

    /**
     * 挑高
     */
    private BigDecimal buildingCeilingHeight;

    /**
     * 提交系数
     */
    private BigDecimal submitCoefficient;

    /**
     * 复核系数
     */
    private BigDecimal finalCoefficient;

    /**
     * 特殊说明
     */
    private String specialDesc;

}
