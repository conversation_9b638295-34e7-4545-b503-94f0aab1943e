package com.coocaa.meht.module.sys.controller;

import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.api.tencent.TencentCosService;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.utils.Converts;
import com.coocaa.meht.utils.FtpUtils;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * 附件管理
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sys/file")
public class SysFileController {
    private final SysFileService sysFileService;

    private final TencentCosService tencentCosService;

    /**
     * 文件上传
     *
     * @param file
     * @return
     */
    @PostMapping("/upload")
    @Reqlog(value = "文件上传", type = Reqlog.LogType.INSERT)
    public Result<SysFileEntity> upload(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return Result.error("请选择需要上传的文件");
        }
        return Result.ok(sysFileService.upload(file));
    }


    /**
     * cos文件上传
     * @param files
     * @return
     * @throws IOException
     */
    @PostMapping("/uploadCos")
    @Reqlog(value = "文件上传", type = Reqlog.LogType.INSERT)
    public Result<?> uploadCos(MultipartFile[] files) throws IOException {
        if (files == null ) {
            return Result.error("请选择需要上传的文件");
        }
        List<Map<String, Object>> mapList = tencentCosService.uploadCos(files);
        for (Map<String,Object> map : mapList){
            SysFileEntity sysFileEntity = new SysFileEntity();
            sysFileEntity.setName(Converts.toStr(map.get("name"),""));
            sysFileEntity.setUrl(Converts.toStr(map.get("url"),""));
            sysFileEntity.setSize(Converts.toLong(map.get("size"),0L));
            sysFileEntity.setAttachmentType(Converts.toStr(map.get("attachmentType"),""));
            sysFileService.save(sysFileEntity);
            map.put("id",sysFileEntity.getId());
        }
        return Result.ok(mapList);
    }

    /**
     * 下载
     *
     * @param url
     * @param response
     * @throws Exception
     */
    @GetMapping("/download")
    @Reqlog(value = "文件下载", type = Reqlog.LogType.SELECT)
    public void downLoadFile(String url, HttpServletResponse response) throws Exception {
        SysFileEntity attachment = sysFileService.getByUrl(url);
        if (attachment != null) {
            String filename = URLEncoder.encode(attachment.getName(), "UTF-8");
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;filename=" + filename);
            response.addHeader("Content-Length", attachment.getSize().toString());
            FtpUtils.download(url, response.getOutputStream());
        }
    }

}