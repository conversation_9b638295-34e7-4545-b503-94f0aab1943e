package com.coocaa.meht.module.dataimport.pojo;

import com.coocaa.meht.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10
 */
@Data
public class BuildingRatingVO {
    /**
     * 楼宇编码
     */
    private String buildingNo;

    /**
     * 用户工号
     */
    private String wno;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private LocalDateTime createTime;
}
