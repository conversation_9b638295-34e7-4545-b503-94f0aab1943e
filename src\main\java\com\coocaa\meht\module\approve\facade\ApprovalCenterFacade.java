package com.coocaa.meht.module.approve.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.dto.ApprovalDetailVO;
import com.coocaa.meht.module.approve.dto.ApprovalOperationDTO;
import com.coocaa.meht.module.approve.dto.ScreenApproveRecordDTO;
import com.coocaa.meht.module.approve.dto.TaskDealCountParam;
import com.coocaa.meht.module.approve.dto.TaskDealCountVO;
import com.coocaa.meht.module.approve.dto.TodoListQueryDTO;
import com.coocaa.meht.module.approve.dto.TodoTaskDTO;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;
import com.coocaa.meht.module.approve.service.ApprovalProcessService;
import com.coocaa.meht.module.approve.service.ApprovalQueryService;
import com.coocaa.meht.rpc.vo.InnerInstanceTaskVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 审批中台门面类
 * 对外提供统一的审批操作接口，屏蔽内部实现细节
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Service
@Slf4j
@Validated
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ApprovalCenterFacade {
    private final ApprovalProcessService processService;
    private final ApprovalQueryService queryService;

    /**
     * 提交审批
     *
     * @param dto 审批提交数据
     * @return 审批结果，包含审批实例ID
     */
    public String submitApproval(@Valid @NotNull(message = "审批数据不能为空") ApprovalDTO dto) {
        log.info("接收审批提交请求: {}", dto);
        try {
            return processService.submit(dto);
        } catch (ApprovalBusinessException e) {
            log.warn("审批提交业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("审批提交未知异常", e);
            throw new ApprovalBusinessException("审批提交失败，发生未知错误");
        }
    }

    /**
     * 审批通过
     *
     * @param dto 审批操作数据
     * @return 操作结果
     */
    public String approveTask(@Valid @NotNull(message = "审批操作数据不能为空") ApprovalOperationDTO dto) {
        log.info("接收审批通过请求: {}", dto);
        try {
            return processService.approve(dto);
        } catch (ApprovalBusinessException e) {
            log.warn("审批通过业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("审批通过未知异常", e);
            throw new ApprovalBusinessException("审批通过失败，发生未知错误");
        }
    }

    /**
     * 审批拒绝
     *
     * @param dto 审批操作数据
     * @return 操作结果
     */
    public String rejectTask(@Valid @NotNull(message = "审批操作数据不能为空") ApprovalOperationDTO dto) {
        log.info("接收审批拒绝请求: {}", dto);
        try {
            return processService.reject(dto);
        } catch (ApprovalBusinessException e) {
            log.warn("审批拒绝业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("审批拒绝未知异常", e);
            throw new ApprovalBusinessException("审批拒绝失败，发生未知错误");
        }
    }

    /**
     * 撤销审批
     *
     * @param dto 审批操作数据
     * @return 操作结果
     */
    public String revokeApproval(@Valid @NotNull(message = "审批操作数据不能为空") ApprovalOperationDTO dto) {
        log.info("接收撤销审批请求: {}", dto);
        try {
            return processService.revoke(dto);
        } catch (ApprovalBusinessException e) {
            log.warn("撤销审批业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("撤销审批未知异常", e);
            throw new ApprovalBusinessException("撤销审批失败，发生未知错误");
        }
    }

    /**
     * 查询待办列表
     *
     * @param queryDTO 查询条件
     * @return 待办列表
     */
    public Result<IPage<TodoTaskDTO>> queryTodoList(@Valid @NotNull(message = "查询条件不能为空")
                                                    PageRequestVO<TodoListQueryDTO> queryDTO) {
        log.info("查询待办列表, query: {}", queryDTO);
        return queryService.queryTodoList(queryDTO);
    }

    /**
     * 查询已办列表
     *
     * @param queryDTO 查询条件
     * @return 已办列表
     */
    public Result<IPage<TodoTaskDTO>> queryDoneList(@Valid @NotNull(message = "查询条件不能为空")
                                                    PageRequestVO<TodoListQueryDTO> queryDTO) {
        log.info("查询已办列表, query: {}", queryDTO);
        return queryService.queryDoneList(queryDTO);
    }

    /**
     * 查询审批详情
     *
     * @param instanceCode 审批实例编码
     * @return 审批详情
     */
    public Result<ApprovalDetailVO> queryDetail(@NotBlank(message = "审批实例编码不能为空") String instanceCode) {
        log.info("查询审批详情, instanceCode: {}", instanceCode);
        return queryService.queryDetail(instanceCode);
    }

    /**
     * 根据业务键查询审批详情
     *
     * @param businessKey 业务唯一标识
     * @param approveType 审批类型
     * @param version     版本
     * @return 审批详情
     */
    public Result<ApprovalDetailVO> queryDetailByBusinessKey(String businessKey, String approveType, String version) {
        log.info("根据业务键查询审批详情, businessKey: {}, sceneType: {}, version: {}", businessKey, approveType, version);
        return queryService.queryDetailByBusinessKey(businessKey, approveType, version);
    }

    /**
     * 获取任务处理数量
     *
     * @param param
     * @return
     */
    public Result<List<TaskDealCountVO>> getTaskDealCount(TaskDealCountParam param) {
        return queryService.getTaskDealCount(param);
    }

    /**
     * 根据审批实例获取当前审核节点
     *
     * @param instanceCode 审批实例
     * @return
     */
    public InnerInstanceTaskVO getCurrentTask(String instanceCode) {
        return queryService.getCurrentTask(instanceCode);
    }

    /**
     * 根据业务编码和版本号查询当前审核节点
     *
     * @param businessKey 业务编码
     * @param version     版本号
     * @return
     */
    public InnerInstanceTaskVO getCurrentTask(String businessKey, String version) {
        return queryService.getCurrentTask(businessKey, version);
    }

    /**
     * 同步审批实例和节点
     *
     * @param instanceCodeList
     * @return
     */
    public Boolean syncInstanceAndNodes(List<String> instanceCodeList) {
        return processService.syncInstanceAndNodes(instanceCodeList);
    }

    /**
     * 查询本地审核节点
     *
     * @param businessKey 业务编码
     * @return 审批详情
     */
    public Result<List<ScreenApproveRecordDTO>> queryLocalNodes(String businessKey) {
        return queryService.queryLocalNodes(businessKey);
    }

    /**
     * 飞书审核链接查询跳转参数
     *
     * @param instanceCode
     * @return
     */
    public Object getDetailParams(String instanceCode, Integer type) {
        return queryService.getDetailParams(instanceCode, type);
    }

    /**
     * 判断审批任务是否存在
     *
     * @param businessKey 业务键
     * @param version     版本号
     * @param type        审批类型
     * @return
     */
    public Boolean isTaskExist(String businessKey, String version, ApprovalTypeEnum type) {
        return queryService.isTaskExist(businessKey, version, type);
    }

}