-- -----------------------------------------------------------------------------------------------
USE dev_merchant;


CREATE TABLE `message_record`
(
    `id`             bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `target_user`    varchar(10)  NOT NULL DEFAULT '' COMMENT '目标用户',
    `send_user`      varchar(10)  NOT NULL DEFAULT '' COMMENT '发送用户',
    `send_type`      tinyint      NOT NULL DEFAULT 0 COMMENT '类型：0用户发送，1系统发送',
    `classify`       varchar(50)  NOT NULL DEFAULT '' COMMENT '消息类别',
    `content`        json                  DEFAULT NULL COMMENT '消息内容',
    `fs_message_id`  varchar(100) NOT NULL DEFAULT '' COMMENT '飞书消息ID',
    `fs_read_status` tinyint      NOT NULL DEFAULT '0' COMMENT '飞书读状态: 0未读，1已读',
    `fs_read_time`   datetime              DEFAULT NULL COMMENT '飞书读时间',
    `create_by`      varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`      varchar(10)  NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_targetUser` (`target_user`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='消息记录';


CREATE TABLE `agent_personnel`
(
    `id`          bigint(20)  NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `emp_code`    varchar(10) NOT NULL DEFAULT '' COMMENT '员工编码',
    `emp_name`    varchar(50) NOT NULL DEFAULT '' COMMENT '员工姓名',
    `emp_mobile`  varchar(50) NOT NULL DEFAULT '' COMMENT '员工手机号码',
    `agent_code`  varchar(50) NOT NULL DEFAULT '' COMMENT '代理商账号',
    `agent_name`  varchar(50) NOT NULL DEFAULT '' COMMENT '代理商名称',
    `deleted`     tinyint(4)  NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
    `create_by`   varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_empMobile` (`emp_mobile`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='外部代理商信息';


CREATE TABLE `sys_user`
(
    `id`                         bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'id',
    `emp_code`                   varchar(16)  NOT NULL COMMENT '工号',
    `real_name`                  varchar(50)  NOT NULL DEFAULT '' COMMENT '姓名',
    `avatar`                     varchar(255) NOT NULL DEFAULT '' COMMENT '头像',
    `email`                      varchar(100) NOT NULL DEFAULT '' COMMENT '邮箱',
    `gender`                     tinyint(4)   NOT NULL DEFAULT 2 COMMENT '性别   0：男   1：女   2：未知',
    `joined_date`                date                  DEFAULT NULL COMMENT '入职日期',
    `termination_date`           date                  DEFAULT NULL COMMENT '离职日期',
    `mobile`                     varchar(20)  NOT NULL DEFAULT '' COMMENT '手机号',
    `fs_user_id`                 varchar(100) NOT NULL DEFAULT '' COMMENT '飞书user_id',
    `fs_open_id`                 varchar(100) NOT NULL DEFAULT '' COMMENT '飞书open_id',
    `fs_union_id`                varchar(100) NOT NULL DEFAULT '' COMMENT '飞书union_id',
    `beisen_id`                  varchar(100) NOT NULL DEFAULT '' COMMENT '北森ID',
    `beisen_post_name`           varchar(50)  NOT NULL DEFAULT '' COMMENT '北森岗位名称',
    `status`                     tinyint(4)   NOT NULL DEFAULT '0' COMMENT '员工状态  0：离职   1：在职',
    `emp_type`                   tinyint(4)   NOT NULL DEFAULT '1' COMMENT '员工类型 1：正式员工 2：外包员工 3：实习生 4:未知',
    `performance_evaluator`      varchar(50)  NOT NULL DEFAULT '' COMMENT '绩效评定人',
    `performance_evaluator_code` varchar(16)  NOT NULL DEFAULT '' COMMENT '绩效评定人工号',
    `org_code`                   varchar(20)  NOT NULL DEFAULT '' COMMENT '主组织id',
    `assess_dept`                varchar(32)  NOT NULL DEFAULT '' COMMENT '考核部门',
    `po_code`                    varchar(10)  NOT NULL DEFAULT '' COMMENT '岗位编码',
    `po_name`                    varchar(50)  NOT NULL DEFAULT '' COMMENT '岗位名称',
    `deleted`                    tinyint(4)   NOT NULL DEFAULT '0' COMMENT '删除标识  0：正常   1：已删除',
    `create_by`                  varchar(10)  NOT NULL DEFAULT '' COMMENT '创建者',
    `create_time`                datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`                  varchar(10)  NOT NULL DEFAULT '' COMMENT '更新者',
    `update_time`                datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_empCode` (`emp_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='员工';


CREATE TABLE `sys_role`
(
    `id`          bigint(20)    NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `code`        varchar(50)   NOT NULL DEFAULT '' COMMENT '编号',
    `name`        varchar(100)  NOT NULL DEFAULT '' COMMENT '名称',
    `status`      tinyint(4)    NOT NULL DEFAULT '0' COMMENT '状态：0启用，1禁用',
    `authority`   varchar(1000) NOT NULL DEFAULT '' COMMENT '数据权限',
    `remark`      varchar(255)  NOT NULL DEFAULT '' COMMENT '备注',
    `deleted`     tinyint(4)    NOT NULL DEFAULT '0' COMMENT '删除标识：0正常，1已删除',
    `create_by`   varchar(10)   NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(10)   NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_code` (`code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='角色管理';


CREATE TABLE `sys_menu`
(
    `id`          bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`   bigint(20)   NOT NULL DEFAULT '0' COMMENT '上级ID',
    `name`        varchar(100) NOT NULL DEFAULT '' COMMENT '菜单名称',
    `url`         varchar(255) NOT NULL DEFAULT '' COMMENT '菜单URL',
    `authority`   varchar(500) NOT NULL DEFAULT '' COMMENT '授权标识',
    `type`        tinyint(4)   NOT NULL DEFAULT '0' COMMENT '类型：0目录，1菜单，2按钮',
    `icon`        varchar(50)  NOT NULL DEFAULT '' COMMENT '菜单图标',
    `sort`        int(11)      NOT NULL DEFAULT '0' COMMENT '排序',
    `open_style`  tinyint(4)   NOT NULL DEFAULT '0' COMMENT '打开方式：0内部，1外部',
    `deleted`     tinyint(4)   NOT NULL DEFAULT '0' COMMENT '删除标识：0正常，1已删除',
    `create_by`   varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(10)  NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='菜单管理';


CREATE TABLE `sys_role_menu`
(
    `id`          bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `role_code`   varchar(100) NOT NULL DEFAULT '' COMMENT '角色ID',
    `menu_id`     bigint(20)   NOT NULL DEFAULT '0' COMMENT '菜单ID',
    `create_by`   varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(10)  NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_roleCode` (`role_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='角色菜单关系';


CREATE TABLE `sys_user_role`
(
    `id`          bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_code`   varchar(10)  NOT NULL DEFAULT '' COMMENT '用户工号',
    `role_code`   varchar(100) NOT NULL DEFAULT '' COMMENT '角色ID',
    `create_by`   varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(10)  NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_userCode` (`user_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户角色关系';


CREATE TABLE `sys_config`
(
    `id`          bigint(20)    NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`        varchar(100)  NOT NULL DEFAULT '' COMMENT '名称',
    `key`         varchar(100)  NOT NULL DEFAULT '' COMMENT '键名',
    `value`       varchar(5000) NOT NULL DEFAULT '' COMMENT '键值',
    `status`      tinyint(4)    NOT NULL DEFAULT '0' COMMENT '状态：0启用，1禁用',
    `sort`        tinyint(4)    NOT NULL DEFAULT '0' COMMENT '顺序',
    `remark`      varchar(255)  NOT NULL DEFAULT '' COMMENT '备注',
    `create_by`   varchar(10)   NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(10)   NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_key` (`key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='参数配置';


CREATE TABLE `sys_file`
(
    `id`              bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`            varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
    `url`             varchar(255) NOT NULL DEFAULT '' COMMENT '附件地址',
    `size`            bigint(20)   NOT NULL DEFAULT '0' COMMENT '大小(字节)',
    `attachment_type` varchar(100) NOT NULL DEFAULT '' COMMENT '类型',
    `create_by`       varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`       varchar(10)  NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_url` (`url`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='附件管理';

CREATE TABLE `sys_request_log`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_code`   varchar(10)         DEFAULT '' COMMENT '用户',
    `types_of`    tinyint(4)          DEFAULT '0' COMMENT '类型：0未解析，1查看，2新增，3修改，4删除，5登录',
    `title`       varchar(100)        DEFAULT '' COMMENT '标题',
    `request_url` varchar(255)        DEFAULT '' COMMENT '请求url',
    `time`        int(11)             DEFAULT '0' COMMENT '耗时（ms）',
    `status`      tinyint(4)          DEFAULT '0' COMMENT '是否失败：0否，1是',
    `ip`          varchar(100)        DEFAULT '' COMMENT 'IP',
    `address`     varchar(100)        DEFAULT '' COMMENT '地点',
    `browser`     varchar(100)        DEFAULT '' COMMENT '浏览器',
    `os`          varchar(100)        DEFAULT '' COMMENT '系统',
    `platform`    varchar(30)         DEFAULT '' COMMENT '平台',
    `create_time` datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_requestUrl` (`request_url`),
    KEY `idx_createTime` (`create_time`),
    KEY `idx_user_code_create_time` (`user_code`, `create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='请求日志';


CREATE TABLE `sys_request_log_desc`
(
    `id`            bigint(20) NOT NULL COMMENT 'ID',
    `request_param` text COMMENT '请求参数',
    `error_log`     text COMMENT '异常日志',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='请求日志详情';


CREATE TABLE `sys_error_log`
(
    `id`          bigint(20)    NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `title`       varchar(5000) NOT NULL DEFAULT '' COMMENT '标题',
    `error_log`   json COMMENT '异常日志',
    `create_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='异常日志';


CREATE TABLE `sys_token`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_code`   varchar(10)         DEFAULT '' COMMENT '用户',
    `platform`    varchar(100)        DEFAULT '' COMMENT '平台',
    `token`       varchar(100)        DEFAULT '' COMMENT 'token',
    `create_time` datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_userCode` (`user_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='token';


CREATE TABLE `building_details` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `building_no` varchar(50) NOT NULL COMMENT '楼宇ID',
  `building_grade` bigint(20) NOT NULL DEFAULT '0' COMMENT '等级',
  `building_location` bigint(20) NOT NULL DEFAULT '0' COMMENT '地理位置',
  `building_number` bigint(20) NOT NULL DEFAULT '0' COMMENT '楼层数',
  `building_number_input` varchar(200) NOT NULL DEFAULT '' COMMENT '楼层数(输入)',
  `building_price` bigint(20) NOT NULL DEFAULT '0' COMMENT '月租金',
  `building_price_input` varchar(200) NOT NULL DEFAULT '' COMMENT '月租金(输入)',
  `building_age` bigint(20) NOT NULL DEFAULT '0' COMMENT '楼龄',
  `building_age_input` varchar(200) NOT NULL DEFAULT '' COMMENT '楼龄(输入)',
  `building_exterior` bigint(20) NOT NULL DEFAULT '0' COMMENT '外观造型',
  `building_lobby` bigint(20) NOT NULL DEFAULT '0' COMMENT '楼盘大堂',
  `building_garage` bigint(20) NOT NULL DEFAULT '0' COMMENT '地下车库',
  `building_hall` bigint(20) NOT NULL DEFAULT '0' COMMENT '侯梯厅',
  `building_brand` bigint(20) NOT NULL DEFAULT '0' COMMENT '综合体品牌',
  `building_rating` bigint(20) NOT NULL DEFAULT '0' COMMENT '点评评分',
  `building_settled` bigint(20) NOT NULL DEFAULT '0' COMMENT '入驻率',
  `third_building_grade` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方等级',
  `third_building_location` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方地理位置',
  `third_building_number` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方楼层数',
  `third_building_price` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方月租金',
  `third_building_age` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方楼龄',
  `third_building_exterior` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方外观造型',
  `third_building_lobby` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方楼盘大堂',
  `third_building_garage` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方地下车库',
  `third_building_hall` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方侯梯厅',
  `third_building_brand` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方综合体品牌',
  `third_building_rating` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方点评评分',
  `third_building_settled` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方入驻率',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
  `create_by` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_ratingId` (`building_no`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='楼宇选择';


CREATE TABLE `building_parameter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `building_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区',
  `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '上级ID',
  `parameter_name` varchar(50) NOT NULL DEFAULT '' COMMENT '参数名称',
  `parameter_code` varchar(50) NOT NULL DEFAULT '' COMMENT '参数编码',
  `parameter_score` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '参数值',
  `weight_value` decimal(28,8) NOT NULL DEFAULT '0.00000000' COMMENT '权重值',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
  `create_by` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='楼宇参数';


CREATE TABLE `building_rating` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `building_no` varchar(50) NOT NULL DEFAULT '' COMMENT '楼宇编码',
  `building_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '楼宇认证状态：0未认证，1认证中 2冻结中  3已认证',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '楼宇状态：0待审核，1已审核 2已驳回 3审核不通过   4已放弃',
  `building_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区',
  `building_name` varchar(2500) NOT NULL DEFAULT '' COMMENT '楼宇名称',
  `map_no` varchar(50) NOT NULL DEFAULT '' COMMENT '地图楼宇编码',
  `map_province` varchar(50) NOT NULL DEFAULT '' COMMENT '省名称',
  `map_city` varchar(50) NOT NULL DEFAULT '' COMMENT '市名称',
  `map_region` varchar(50) NOT NULL DEFAULT '' COMMENT '区名称',
  `map_address` varchar(300) NOT NULL DEFAULT '' COMMENT '详细地址',
  `map_latitude` varchar(50) NOT NULL DEFAULT '' COMMENT '纬度',
  `map_longitude` varchar(50) NOT NULL DEFAULT '' COMMENT '经度',
  `map_ad_code` varchar(20) NOT NULL DEFAULT '0' COMMENT '行政区域编码',
  `authentication_start` datetime DEFAULT NULL COMMENT '认证生效开始日期',
  `authentication_end` datetime DEFAULT NULL COMMENT '认证生效结束日期',
  `authentication_period` bigint(20) NOT NULL DEFAULT '0' COMMENT '认证有效期限',
  `freeze_start` datetime DEFAULT NULL COMMENT '冻结开始日期',
  `freeze_end` datetime DEFAULT NULL COMMENT '冻结结束日期',
  `freeze_period` bigint(20) NOT NULL DEFAULT '0' COMMENT '冻结有效期限',
  `building_desc` varchar(200) NOT NULL DEFAULT '' COMMENT '描述',
  `submit_user` varchar(10) NOT NULL DEFAULT '' COMMENT '提交人工号',
  `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
  `building_score` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '综合得分',
  `project_level` varchar(20) NOT NULL DEFAULT '' COMMENT '等级评价',
  `first_floor_exclusive` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '一楼独享',
  `first_floor_share` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '一楼共享',
  `negative_first_floor` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '负一楼',
  `negative_two_floor` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '负二楼',
  `two_floor_above` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '二楼及以上',
  `third_floor_below` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '负三楼及以下',
  `approve_user` varchar(10) NOT NULL DEFAULT '' COMMENT '审批人工号',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approve_desc` varchar(2000) NOT NULL DEFAULT '' COMMENT '审批描述',
  `reject_user` varchar(10) DEFAULT '' COMMENT '驳回人工号',
  `reject_time` datetime DEFAULT NULL COMMENT '驳回时间',
  `reject_desc` varchar(2000) DEFAULT '' COMMENT '驳回描述',
  `building_exterior_pic` varchar(50) NOT NULL DEFAULT '' COMMENT '外墙材料附件地址',
  `building_lobby_pic` varchar(200) NOT NULL DEFAULT '' COMMENT '大堂高度及装饰附件地址',
  `building_hall_pic` varchar(200) NOT NULL DEFAULT '' COMMENT '楼梯厅装饰附件地址',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
  `create_by` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_buildingType` (`building_type`),
  KEY `idx_submitUser` (`submit_user`),
  KEY `idx_approveUser` (`approve_user`) COMMENT '添加审批人索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='楼宇基本信息';



CREATE TABLE `city_rent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `province` varchar(50) NOT NULL DEFAULT '' COMMENT '省名称',
  `city` varchar(50) NOT NULL DEFAULT '' COMMENT '市名称',
  `ad_code` bigint(20) NOT NULL COMMENT '行政区域编码',
  `office_rent` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '写字楼租金',
  `resid_rent` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '商住楼租金',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
  `create_by` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='城市平均租金';


CREATE TABLE `city_coefficient` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `province` varchar(50) NOT NULL DEFAULT '' COMMENT '省名称',
  `city` varchar(50) NOT NULL DEFAULT '' COMMENT '市名称',
  `region` varchar(50) NOT NULL DEFAULT '' COMMENT '区名称',
  `ad_code` bigint(20) NOT NULL COMMENT '行政区域编码',
  `coefficient` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '系数值',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
  `create_by` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='城市系数';


CREATE TABLE `personnel_approval` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `three_org` varchar(10) NOT NULL DEFAULT '' COMMENT '三级组织名称',
  `four_org` varchar(10) NOT NULL DEFAULT '' COMMENT '三级组织名称',
  `emp_name` varchar(50) NOT NULL DEFAULT '' COMMENT '员工姓名',
  `emp_code` varchar(20) NOT NULL DEFAULT '' COMMENT '员工编码',
  `emp_post` varchar(50) NOT NULL DEFAULT '' COMMENT '员工岗位',
  `approval_code` varchar(20) NOT NULL DEFAULT '' COMMENT '审批人工号',
  `approval_name` varchar(50) NOT NULL DEFAULT '' COMMENT '审批人姓名',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
  `create_by` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_empCode` (`emp_code`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='员工审批列表';


CREATE TABLE `building_brand` (
    `id`          INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name`        VARCHAR(128)     NOT NULL DEFAULT '' COMMENT '名称',
    `type`        VARCHAR(128)     NOT NULL DEFAULT '1' COMMENT '编码',
    `index`       DECIMAL(10, 2) UNSIGNED   DEFAULT '0.00' COMMENT '品牌指数',
    `rank`        INT(11) UNSIGNED          DEFAULT '0' COMMENT '排名(自然顺序，越小越靠前)',
    `status`      TINYINT(1)       NOT NULL DEFAULT '1' COMMENT '状态 [0:禁用, 1:启用]',
    `create_by`   VARCHAR(10)      NOT NULL DEFAULT '' COMMENT '创建人编码',
    `create_time` DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   VARCHAR(10)      NOT NULL DEFAULT '' COMMENT '修改人编码',
    `update_time` DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT ='楼宇品牌';