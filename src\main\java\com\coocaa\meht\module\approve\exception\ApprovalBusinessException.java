package com.coocaa.meht.module.approve.exception;

/**
 * 审批业务异常
 * 用于表示业务规则验证失败等情况
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public class ApprovalBusinessException extends RuntimeException {

    /**
     * 审批业务异常
     *
     * @param message 异常信息
     */
    public ApprovalBusinessException(String message) {
        super(message);
    }

    /**
     * 审批业务异常
     *
     * @param message 错误信息
     * @param cause   错误原因
     */
    public ApprovalBusinessException(String message, Throwable cause) {
        super(message, cause);
    }
} 