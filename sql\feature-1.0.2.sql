CREATE TABLE `price_apply` (
   `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `apply_code` varchar(20) DEFAULT NULL COMMENT '申请编号(JGSQ+年月日+流水号)',
   `building_no` varchar(50) NOT NULL DEFAULT '' COMMENT '楼宇编码',
   `building_name` varchar(200) DEFAULT NULL COMMENT '楼宇名称',
   `contract_duration` decimal(6,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '合同年限',
   `total_amount` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '合同总金额(元)',
   `payment_type` varchar(10) NOT NULL DEFAULT '' COMMENT '付款方式 [一次性、一年付、半年付、季度付、其他]',
   `is_deposit` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '是否有押金 [0:否, 1:是]',
   `deposit_amount` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '押金金额(元)',
   `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '其他信息',
   `status` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '状态 [0:草稿 1:待审核 2:已审核 3:已驳回]',
   `approve_by` varchar(10) NOT NULL DEFAULT '' COMMENT '审批人',
   `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
   `approve_remark` varchar(500) NOT NULL DEFAULT '' COMMENT '审批备注',
   `crm_user_id` varchar(20) DEFAULT '' COMMENT 'CRM用户ID',
   `file_ids` varchar(200) DEFAULT '' COMMENT '附件ID列表',
   `create_by` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_by` varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
   PRIMARY KEY (`id`),
   KEY `idx_building_no` (`building_no`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COMMENT='价格申请';


CREATE TABLE `price_apply_device` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `apply_id` int(11) unsigned NOT NULL COMMENT '价格申请ID',
  `type` varchar(50) NOT NULL DEFAULT '液晶电视' COMMENT '设备类型',
  `size` varchar(50) NOT NULL DEFAULT '' COMMENT '设备尺寸',
  `quantity` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '设备数量',
  `sign_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '签约单价(元/台/月)',
  `location` varchar(200) NOT NULL DEFAULT '' COMMENT '安装位置',
  `location_detail` varchar(500) DEFAULT '' COMMENT '安装位置详情(JSON)',
  `create_by` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_apply_id` (`apply_id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COMMENT='价格申请设备明细表';

