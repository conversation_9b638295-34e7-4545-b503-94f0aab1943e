package com.coocaa.meht.module.web.dto.crm;


import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/8
 * @description 商机修改
 */
@Data
@Accessors(chain = true)
public class CrmBusinessUpdateParamDto {

    private CrmBusinessUpdateParamDto.EntityDto entity;

    private List<CrmBusinessParamDto.FieldDto> field;


    @Data
    @Accessors(chain = true)
    public static class EntityDto {

        /**
         * 商机名称
         */
        private String businessName;

        private String batchId;

        private String businessId;

        private String money = "";

        private String typeId;

        private int discountRate = 0;

        private int totalPrice = 0;

        /**
         * 客户信息
         */
        private List<Map<String,String>> customer;

    }


}
