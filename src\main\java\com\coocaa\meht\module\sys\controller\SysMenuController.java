package com.coocaa.meht.module.sys.controller;

import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 菜单管理
 */
@RestController
@RequestMapping("/sys/menu")
@AllArgsConstructor
public class SysMenuController {
//    private final SysMenuService sysMenuService;
//    private final TokenStoreCache tokenStoreCache;
//
//    @GetMapping("/nav")
//    @Reqlog(value = "用户菜单", type = LogType.SELECT)
//    public Result<List<SysMenuDto>> nav() {
//        UserDetail user = SecurityUser.getUserAuth();
//        List<SysMenuEntity> list = sysMenuService.getListByUser(user.getEmpCode());
//        Set<Long> menuId = list.stream().map(SysMenuEntity::getId).collect(Collectors.toSet());
//        Set<String> permi = sysMenuService.convertPermi(list);
//        List<SysMenuDto> dtoList = list.stream().filter(ele -> !Objects.equals(ele.getType(), 2))
//                .map(SysMenuDto::toDto)
//                .collect(Collectors.toList());
//        user.setMenuIds(menuId);
//        user.setAuthoritySet(permi);
//        tokenStoreCache.saveUser(user.getToken(), user);
//        return Result.ok(TreeUtils.build(dtoList));
//    }
//
//    @GetMapping("/authority")
//    @Reqlog(value = "用户权限标识", type = LogType.SELECT)
//    public Result<Map<String, Object>> authority() {
//        return Result.ok(sysMenuService.authorityAll());
//    }

}