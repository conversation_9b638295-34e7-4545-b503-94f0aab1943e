package com.coocaa.meht.module.approve.dto;

import com.coocaa.meht.module.approve.constant.Constants;
import com.coocaa.meht.rpc.vo.InnerApproveNodeVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审批详情视图对象
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "审批详情视图对象")
public class ApprovalDetailVO {

    @Schema(description = "任务ID")
    private Integer id;

    @Schema(description = "规则表id")
    private Integer ruleId;

    @Schema(description = "规则编号")
    private Integer ruleCode;

    @Schema(description = "审批实例code")
    private String instanceCode;

    @Schema(description = "审批任务名称")
    private String approvalName;

    @Schema(description = "审批结果，字典0138")
    private String approvalResult;

    @Schema(description = "审批状态，字典0141")
    private String approvalStatus;

    @Schema(description = "取消原因，字典0140")
    private String cancelReason;

    @Schema(description = "审批单提交人ID")
    private Integer userId;

    @Schema(description = "审批单抄送人ID")
    private Integer ccReviewer;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "审批单结束时间")
    private LocalDateTime endTime;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "审批单创建时间")
    private LocalDateTime createTime;

    /**
     * 审批节点列表
     */
    @Schema(description = "审批节点列表")
    private List<InnerApproveNodeVO> nodes;
}