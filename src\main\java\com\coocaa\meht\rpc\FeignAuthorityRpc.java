package com.coocaa.meht.rpc;

import com.coocaa.ad.common.config.FeignConfig;
import com.coocaa.meht.common.LoginResultVO;
import com.coocaa.meht.common.UserAuthVO;
import com.coocaa.meht.common.bean.CityNameParam;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.DictCodeVO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.TokenResultVO;
import com.coocaa.meht.common.bean.cos.CosVO;
import com.coocaa.meht.module.sys.dto.SmsSendParam;
import com.coocaa.meht.module.web.dto.approval.UserFeishuVO;
import com.coocaa.meht.module.web.vo.common.UserVO;
import com.coocaa.meht.rpc.dto.UserBatchMessageParam;
import com.coocaa.meht.rpc.dto.UserFeiShuMessageParam;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/1
 */
@FeignClient(value = "cheese-authority-api",
        contextId = "meht-authority-rpc",
        configuration = FeignConfig.class)
public interface FeignAuthorityRpc {

    //获取城市
    @PostMapping("/sys/city/list/ids")
    ResultTemplate<List<CodeNameVO>> listCityByIds(@RequestBody Collection<Integer> ids);

    //获取字典
    @PostMapping("/sys/dict/list/codes")
    ResultTemplate<List<CodeNameVO>> listDictByCodes(@RequestBody Collection<String> codes);


    //获取用户拥有的城市权限 用于查询数据限制用户的数据范围
    @GetMapping("/sys/city/list/select/auth")
    ResultTemplate<List<CodeNameVO>> listCityByAuthority();

    /**
     * 查询用户拥有的城市列表
     * 如果是全部，仅返回一条 id = 0的数据
     */
    @GetMapping("/sys/user/city")
    ResultTemplate<List<CodeNameVO>> listUserCities();

    /**
     * 根据用户ID查询用户名字
     *
     * @param userIds 用户IDs
     * @return 用户名字列表
     */
    @PostMapping("/sys/user/list/ids")
    ResultTemplate<List<CodeNameVO>> listUserByIds(@RequestBody Collection<Integer> userIds);

    /**
     * 根据用户工号查询用户名字
     *
     * @param userWnos 用户工号集合
     * @return 用户名字列表
     */
    @PostMapping("/sys/user/list/wnos")
    ResultTemplate<List<UserVO>> listUserByWnos(@RequestBody Collection<String> userWnos);

    /**
     * 根据行业编码获取行业名称
     *
     * @param codes 行业编码
     * @return 行业名称列表
     */
    @PostMapping("/sys/industry/list/codes")
    ResultTemplate<List<CodeNameVO>> listIndustryByCodes(@RequestBody Collection<String> codes);


    /**
     * 根据城市名称查询城市
     */
    @GetMapping("/sys/city/name/{name}")
    ResultTemplate<CodeNameVO> getByCityName(@PathVariable("name") String name);


    /**
     * 根据城市名称+区县名称查询城市
     */
    @PostMapping("/sys/city/name/county")
    ResultTemplate<DictCodeVO> getCounty(@RequestBody @Validated CityNameParam cityNameParam);


    /**
     * 根据字典父code查询字典的下拉列表
     */
    @GetMapping("/sys/dict/select/{code}")
    ResultTemplate<List<CodeNameVO>> listByParentCode(@PathVariable("code") String code);


    @PostMapping("/sys/city/gb-code")
    ResultTemplate<List<CodeNameVO>> listByGbCode(@RequestBody @Validated @NotEmpty List<String> gbCodes);

    @GetMapping("/sys/city/gb-code/{code}")
    ResultTemplate<CodeNameVO> getByGbCode(@PathVariable("code") String code);

    @PostMapping("/sys/user/feishu/getUserFeishuList")
    ResultTemplate<List<UserFeishuVO>> getUserFeishuList(@RequestBody List<Integer> ids);

    @GetMapping("/sys/cos/temp/key/private")
    ResultTemplate<CosVO> getPrivateTempKey();
    @GetMapping("/sys/user/getUserByAuth/{identity}")
    ResultTemplate<UserAuthVO> getUserByAuth(@PathVariable(name = "identity") String identity);

    @GetMapping("/sys/user/generateToken/{hash}")
    ResultTemplate<LoginResultVO> generateToken(@PathVariable(name = "hash") String hash);

    @GetMapping("/sys/user/userInfoByIdentity/{identity}")
    ResultTemplate<UserVO> userInfoByIdentity(@PathVariable(name = "identity") String identity);

    /**
     * 获取当前登陆用户信息
     * 如果传了工号，则优先查询工号对应的用户信息
     */
    @GetMapping("/sys/user/info?encrypt=false")
    ResultTemplate<UserVO> getUserInfo(@RequestParam(name = "wno") String wno);

    /**
     * 获取行业末级列表
     */
    @GetMapping("/sys/industry/second/select")
    ResultTemplate<List<CodeNameVO>> getSecondIndustry();

    /**
     * 查询所有已启用的城市列表
     */
    @GetMapping("/sys/city/list/select")
    ResultTemplate<List<CodeNameVO>> getAvailableCities();

    @GetMapping("/sys/user/token/h5/{refreshToken}")
     ResultTemplate<TokenResultVO> getTokenByRefreshToken(@PathVariable("refreshToken") String refreshToken);

    /**
     * 查询启用的城市,城市组等查询使用,这里是全量城市
     */
    @GetMapping("/sys/city/list/select")
    ResultTemplate<List<DictCodeVO>> selectList();


    /**
     * 获取当前用户详情
     * @return
     */
    @GetMapping("/sys/user/{id}")
    ResultTemplate<UserVO> getDetail(@PathVariable("id") Integer id,
                                     @RequestParam(name = "encrypt", required = false, defaultValue = "true") boolean encrypt);


    /**
     * 发送飞书用户消息
     */
    @PostMapping("/sys/message/feishu/user-message")
    ResultTemplate<?> sendFeishuMessage(@RequestBody UserFeiShuMessageParam param);

    /**
     * 模糊查询用户
     * @return
     */
    @GetMapping("/sys/user/list-simple")
    ResultTemplate<?> listSimple(@RequestParam("name") String name);

    /**
     * 获取飞书信息
     */
    @PostMapping("/sys/user/feishu/user-feishu-wno")
    ResultTemplate<List<UserFeishuVO>> getUserFeiShuByWno(@RequestBody List<String> wnoList);

    /**
     * 批量发送用户消息
     */
    @PostMapping("/sys/message/station/batch/user-message")
    ResultTemplate<Void> sendUserMessageBatch(@RequestBody UserBatchMessageParam param);


    /**
     * 创世短信
     */
    @PostMapping("/sys/sms/send")
    ResultTemplate<Void> sendSms(@RequestBody SmsSendParam param);
}
