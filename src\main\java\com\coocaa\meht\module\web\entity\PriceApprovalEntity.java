package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 合同审批关系表
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Data
@TableName("price_approval")
public class PriceApprovalEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 价格申请id
     */
    private Integer priceApplyId;

    /**
     * 审批实例编码
     */
    private String approvalInstanceCode;

    /**
     * 审批实例UUID，规则：合同编码-申请次数
     */
    private String approvalInstanceUuid;

    /**
     * 审批状态 -1 审批拒绝   0 审批中   1 审批通过
     */
    private Integer approvalStatus;

    /**
     * 审批版本
     */
    private Integer approvalVersion;

    /**
     * 审批结束时间
     */
    private LocalDateTime approvalEndTime;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
