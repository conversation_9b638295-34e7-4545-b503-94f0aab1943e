package com.coocaa.meht.config.aspect;

import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.utils.RedisLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.StringJoiner;

/**
 * Redis分布式锁切面
 */
@Slf4j
@Aspect
@Component
public class RedisLockAspect {

    @Autowired
    private RedisLockUtil redisLockUtil;

    @Around("@annotation(redisLock)")
    public Object around(ProceedingJoinPoint joinPoint, RedisLock redisLock) throws Throwable {
        String lockKey = getLockKey(joinPoint, redisLock);
        log.info("尝试获取分布式锁，lockKey: {}", lockKey);

        boolean locked = redisLockUtil.tryLock(lockKey,
                redisLock.expireTime(),
                redisLock.waitTime());

        if (!locked) {
            throw new ServerException("获取分布式锁失败");
        }

        try {
            return joinPoint.proceed();
        } finally {
            redisLockUtil.unlock(lockKey);
            log.info("释放分布式锁，lockKey: {}", lockKey);
        }
    }

    //只支持第一个参数
    private String getLockKey(ProceedingJoinPoint joinPoint, RedisLock redisLock) throws Exception {
        Object[] args = joinPoint.getArgs();
        if(args.length>0){
            Object o = args[0];
            String key = redisLock.key();
            String[] split = key.split(",");
            StringJoiner joiner = new StringJoiner(":");
            for(String s : split){
                Field declaredField = o.getClass().getDeclaredField(s);
                declaredField.setAccessible(true);
                joiner.add(s);
                joiner.add(declaredField.get(o).toString());
            }
            joiner.add(joinPoint.getSignature().getName());
            return joiner.toString();

        }
        throw new ServerException("没有参数无法加锁");
    }
} 