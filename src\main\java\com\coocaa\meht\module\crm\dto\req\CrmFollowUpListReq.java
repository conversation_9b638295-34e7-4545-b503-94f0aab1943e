package com.coocaa.meht.module.crm.dto.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CrmFollowUpListReq extends CrmPageReq {

    private String activityTypeId;

    private Integer crmType = 2;

    private Integer queryType = 1;

    @Schema(description = "楼栋号")
    private String buildingNo;

    @Schema(description = "商机编码")
    private String businessCode;

    private Integer id;

}
