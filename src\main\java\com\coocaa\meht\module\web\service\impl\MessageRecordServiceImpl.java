package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.api.fs.FsApiService;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dao.MessageRecordDao;
import com.coocaa.meht.module.web.entity.MessageRecordEntity;
import com.coocaa.meht.module.web.service.MessageRecordService;
import com.coocaa.meht.utils.Converts;
import com.coocaa.meht.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import org.apache.groovy.util.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @Date 2024-11-07 16:56
 */
@Service
@RequiredArgsConstructor
public class MessageRecordServiceImpl extends ServiceImpl<MessageRecordDao, MessageRecordEntity> implements MessageRecordService {

    private final FsApiService fsApiService;
    private final SysUserService sysUserService;

    @Value("${domainNameApp}")
    private String appUrl;

    @Value("${domainNameApp}")
    private String pcUrl;

    @Override
    public void sendApplyForMsg(String targetUser, String sendUser, String applyForUserName,
                                String buildingName, String buildingArea, String buildingNo) {
        try {
            MessageRecordEntity message = new MessageRecordEntity();
            message.setTargetUser(targetUser);
            message.setSendUser(sendUser);
            message.setSendType(1);
            message.setClassify("building-eva-apply-for");
            message.setContent(JsonUtils.toJson(Maps.of(
                    "buildingName", buildingName,
                    "buildingArea", buildingArea,
                    "applyForUserName", applyForUserName)));
            this.save(message);
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            String title = "楼宇评级申请";
            Map<String, String> msgText = new LinkedHashMap<>();
            msgText.put("楼宇名称", buildingName);
            msgText.put("楼宇地区信息", buildingArea);
            msgText.put("申请人姓名", applyForUserName);
            msgText.put("申请时间", message.getCreateTime().format(dtf));
            msgText.put("$url", "/audditDetail?buildingNo=" + buildingNo + "&type=2&feishu=1");
            this.sendFs_AAqDGLddVxLLW(sysUserService.getFsUserId(targetUser),
                    title, msgText,
                    message.getId().toString()).thenAccept(fsId -> {
                if (fsId != null) {
                    this.updateById(new MessageRecordEntity().setId(message.getId())
                            .setFsMessageId(fsId));
                }
            });
        } catch (Exception e) {
            log.error("发送飞书消息出错:", e);
        }
    }

    public void sendPriceApplyForMsg(String targetUser, String applyForUserName,
                                     String buildingName, String buildingArea, String applyId) {
        try {
            MessageRecordEntity message = new MessageRecordEntity();
            message.setTargetUser(targetUser);
            message.setSendUser("系统");
            message.setSendType(1);
            message.setClassify("price-apply-for");
            message.setContent(JsonUtils.toJson(Maps.of(
                    "buildingName", buildingName,
                    "buildingArea", buildingArea,
                    "applyForUserName", applyForUserName)));
            this.save(message);
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            String title = "价格申请";
            Map<String, String> msgText = new LinkedHashMap<>();
            msgText.put("楼宇名称", buildingName);
            msgText.put("楼宇地区信息", buildingArea);
            msgText.put("申请人姓名", applyForUserName);
            msgText.put("申请时间", message.getCreateTime().format(dtf));
            msgText.put("$url", "/price-detail?applyId=" + applyId + "&feishu=1");
            this.sendFs_AAqDGLddVxLLW(sysUserService.getFsUserId(targetUser),
                    title, msgText,
                    message.getId().toString()).thenAccept(fsId -> {
                if (fsId != null) {
                    this.updateById(new MessageRecordEntity().setId(message.getId())
                            .setFsMessageId(fsId));
                }
            });
        } catch (Exception e) {
            log.error("发送飞书消息出错:", e);
        }
    }

    /**
     * 飞书已读
     *
     * @param messageIdList
     * @param readTime
     */
    @Override
    public void setFsRead(List<String> messageIdList, LocalDateTime readTime) {
        this.lambdaUpdate().set(MessageRecordEntity::getFsReadStatus, 1)
                .set(MessageRecordEntity::getFsReadTime, readTime)
                .in(MessageRecordEntity::getFsMessageId, messageIdList)
                .update();
    }

    /**
     * 消息模板（AAqDGLddVxLLW）
     *
     * @param fsUserId
     * @param title
     * @param msgText
     * @param msgId
     */
    private CompletableFuture<String> sendFs_AAqDGLddVxLLW(String fsUserId, String title,
                                                           Map<String, String> msgText,
                                                           String msgId) {
        String url = Converts.toStr(msgText.remove("$url"), "");
        Map<String, Object> variable = new HashMap<>();
        variable.put("title", title);
        variable.put("content", buildTemplateOne(msgText));
        String app_url = this.appUrl + url;
        String pc_url = this.pcUrl + url;
        variable.put("mainUrl", Maps.of("url", pc_url, "pc_url", pc_url,
                "android_url", app_url,
                "ios_url", app_url));
        return fsApiService.sendTemplateMsg(fsUserId, "AAqDGLddVxLLW", variable, msgId);
    }

    /**
     * 消息模板（AAqDGLddVxLLW）
     *
     * @param fsUserId
     * @param title
     * @param msgText
     * @param msgId
     */
    private CompletableFuture<String> sendPriceFsMessage(String fsUserId, String title,
                                                           Map<String, String> msgText,
                                                           String msgId) {
        String url = Converts.toStr(msgText.remove("$url"), "");
        Map<String, Object> variable = new HashMap<>();
        variable.put("title", title);
        variable.put("content", buildTemplateOne(msgText));
        String app_url = this.appUrl + url;
        String pc_url = this.pcUrl + url;
        variable.put("mainUrl", Maps.of("url", pc_url, "pc_url", pc_url,
                "android_url", app_url,
                "ios_url", app_url));
        return fsApiService.sendPriceApplyTemplateMsg(fsUserId, "AAqDGLddVxLLW", variable, msgId);
    }

    private String buildTemplateOne(Map<String, String> msgText) {
        StringBuilder sb = new StringBuilder();
        msgText.forEach((k, v) -> {
            if (sb.length() != 0) {
                sb.append("\n");
            }
            sb.append("<font color='grey'>").append(k).append("：</font>**").append(v).append("**");
        });
        return sb.toString();
    }

}