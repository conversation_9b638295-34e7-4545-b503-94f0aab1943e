package com.coocaa.meht.utils;


import com.alibaba.fastjson2.JSONObject;
import com.coocaa.meht.common.OperateLogDTO;
import com.coocaa.meht.kafka.KafkaProducerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Slf4j
@Component
public class OperateLogUtils {

    private static final String topicName = "cheese-operate-log";

    @Autowired
    private KafkaProducerService kafkaProducerService;

    /**
     * 记录操作日志
     *
     * @param operateLog 操作日志
     */
    public void log(OperateLogDTO operateLog) {
        kafkaProducerService.sendMessage(topicName, operateLog.getSystemCode(), JSONObject.toJSONString(operateLog));
    }

    /**
     * 静态方法调用示例，通过 getInstance 获取当前实例
     */
    private static OperateLogUtils instance;

    @Autowired
    public void setInstance(OperateLogUtils operateLogUtils) {
        instance = operateLogUtils;
    }

    public static void staticLog(OperateLogDTO operateLog) {
        if (instance != null) {
            instance.log(operateLog);
        } else {
            log.error("OperateLogUtils instance is not initialized.");
        }
    }
}
