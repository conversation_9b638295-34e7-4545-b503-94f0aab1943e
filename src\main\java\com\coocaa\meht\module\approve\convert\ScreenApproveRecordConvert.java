package com.coocaa.meht.module.approve.convert;

import com.coocaa.meht.module.approve.dto.ScreenApproveRecordDTO;
import com.coocaa.meht.module.web.entity.ScreenApproveRecordEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-06-19
 */
@Mapper
public interface ScreenApproveRecordConvert {

    ScreenApproveRecordConvert INSTANCE = Mappers.getMapper(ScreenApproveRecordConvert.class);

    ScreenApproveRecordDTO toDto(ScreenApproveRecordEntity entity);

    List<ScreenApproveRecordDTO> toDtoList(List<ScreenApproveRecordEntity> entityList);
}
