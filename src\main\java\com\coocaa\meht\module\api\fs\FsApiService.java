package com.coocaa.meht.module.api.fs;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public interface FsApiService {

    /**
     * 获取token
     *
     * @return
     */
    String getTenantAccessToken();

    /**
     * 获取用户token
     *
     * @param code
     * @return
     */
    Map<String, String> getUserToken(String code);

    /**
     * 获取用户信息
     *
     * @param userToken
     * @return
     */
    Map<String, Object> getUserInfo(String userToken);

    /**
     * 获取用户信息
     *
     * @param userId
     * @return
     */
    Map<String, Object> getUserInfoByUserId(String userId);

    /**
     * 批量获取UserId 邮箱或手机号不可超过50
     *
     * @param emails
     * @param mobiles
     * @return
     */
    Map<String, String> getUserIds(List<String> emails, List<String> mobiles);

    /**
     * 发送模板信息（异步）
     *
     * @param fsUserId
     * @param templateId
     * @param variable
     * @param uuid
     * @return
     */
    CompletableFuture<String> sendTemplateMsg(String fsUserId, String templateId,
                                  Map<String, Object> variable, String uuid);

    /**
     * 发送价格申请模板信息（异步）
     *
     * @param fsUserId
     * @param templateId
     * @param variable
     * @param uuid
     * @return
     */
    CompletableFuture<String> sendPriceApplyTemplateMsg(String fsUserId, String templateId,
                                              Map<String, Object> variable, String uuid);

    /**
     * 发送文本消息（异步）
     *
     * @param fsUserId
     * @param content
     * @return
     */
    CompletableFuture<String> sendTextMsgAsync(String fsUserId, String content);

    /**
     * 发送消息（异步）
     *
     * @param fsUserId
     * @param msgType
     * @param content
     * @param uuid
     * @return
     */
    CompletableFuture<FsMessageResp> sendMsg(String fsUserId, String msgType, String content, String uuid, Integer resetNumber);

    /**
     * 发送消息（异步）
     *
     * @param fsUserId
     * @param msgType
     * @param content
     * @param uuid
     * @return
     */
    CompletableFuture<FsMessageResp> sendPriceApplyMsg(String fsUserId, String msgType, String content, String uuid, Integer resetNumber);
}
