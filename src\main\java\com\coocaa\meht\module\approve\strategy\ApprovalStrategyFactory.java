package com.coocaa.meht.module.approve.strategy;

import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 审批策略工厂
 * 根据审批类型获取对应的策略实现
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Service
public class ApprovalStrategyFactory {
    private final Map<String, ApprovalStrategy> strategyMap = new ConcurrentHashMap<>();


    /**
     * 构造函数，初始化审批策略工厂
     *
     * @param strategies 审批策略列表
     */
    @Autowired
    public ApprovalStrategyFactory(List<ApprovalStrategy> strategies) {
        log.info("初始化审批策略工厂，加载策略数量: {}", strategies.size());
        for (ApprovalStrategy strategy : strategies) {
            if (strategy == null) {
                log.warn("发现一个空的审批策略实例，已跳过。");
                continue;
            }
            String type = strategy.getApproveType();
            if (type == null) {
                log.warn("审批策略 {} 的类型为空，已跳过。", strategy.getClass().getName());
                continue;
            }
            log.info("注册审批策略: {}", type);
            strategyMap.put(type, strategy);
        }
    }

    /**
     * 获取指定类型的审批策略
     *
     * @param type 审批类型
     * @return 对应的审批策略
     * @throws ApprovalBusinessException 当不支持指定的审批类型时抛出
     */
    public ApprovalStrategy getStrategy(String type) {
        ApprovalStrategy strategy = strategyMap.get(type);
        if (strategy == null) {
            log.error("不支持的审批类型: {}", type);
            throw new ApprovalBusinessException("不支持的审批类型: " + type);
        }
        return strategy;
    }

    /**
     * 获取所有支持的审批类型
     *
     * @return 支持的审批类型集合
     */
    public Set<String> getSupportedTypes() {
        return Collections.unmodifiableSet(strategyMap.keySet());
    }
} 