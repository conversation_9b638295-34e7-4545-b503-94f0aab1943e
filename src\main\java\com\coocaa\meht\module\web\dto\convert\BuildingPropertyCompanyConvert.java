package com.coocaa.meht.module.web.dto.convert;

import com.coocaa.meht.module.web.dto.BuildingPropertyCompanyParam;
import com.coocaa.meht.module.web.dto.property.PropertyCompanyParam;
import com.coocaa.meht.module.web.entity.BuildingPropertyCompanyEntity;
import com.coocaa.meht.module.web.entity.PropertyCompanyEntity;
import com.coocaa.meht.module.web.vo.property.PropertyCompanyVO;
import org.mapstruct.Mapper;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface BuildingPropertyCompanyConvert {
    BuildingPropertyCompanyConvert INSTANCE = Mappers.getMapper(BuildingPropertyCompanyConvert.class);

    /**
     * DTO转Entity
     */
    BuildingPropertyCompanyEntity toEntity(BuildingPropertyCompanyParam param);


}
