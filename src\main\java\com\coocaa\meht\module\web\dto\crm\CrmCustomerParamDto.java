package com.coocaa.meht.module.web.dto.crm;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class CrmCustomerParamDto {

    private EntityDto entity;

    private List<FieldDto> field;


    @Data
    @Accessors(chain = true)
    public static class EntityDto {

        /**
         * 楼盘名称
         */
        private String customerName;

        /**
         * 负责人id
         */
        private String ownerUserId;

        /**
         * 楼盘类型
         */
        private String customerBase;

    }


    @Data
    @Accessors(chain = true)
    public static class FieldDto {

        private String fieldId;

        private String fieldName;

        private Integer fieldType;

        private String name;

        private Integer type;

        private String value;


    }

}
