package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @Date 2024-11-08 15:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("agent_personnel")
public class AgentPersonnelEntity extends BaseEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String empCode;     //员工编码
    private String empName;     //员工姓名
    private String empMobile;   //员工手机号码
    private String agentCode;   //代理商账号
    private String agentName;   //代理商名称
    @TableLogic(value = "0", delval = "1")
    private Integer status;
}
