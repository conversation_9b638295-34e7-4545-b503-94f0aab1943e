package com.coocaa.meht.config;

import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.SecurityUser;
import org.apache.commons.lang3.StringUtils;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024-11-07 11:47
 */
public class AuthenticationFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            String accessToken = SecurityUser.getAccessToken(httpRequest);
            if (StringUtils.isNotBlank(accessToken)) {
                LoginUser user = SecurityUser.getUser(accessToken);
                if (Objects.nonNull(user)) {
                    SecurityUser.login(user);
                }
            }
        }
        try {
            chain.doFilter(request, response);
        } finally {
            SecurityUser.clearLogin();
        }
    }

}
