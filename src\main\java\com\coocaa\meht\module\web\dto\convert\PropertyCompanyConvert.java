package com.coocaa.meht.module.web.dto.convert;

import com.coocaa.meht.module.web.dto.BuildingPropertyCompanyDTO;
import com.coocaa.meht.module.web.dto.BuildingPropertyCompanyParam;
import com.coocaa.meht.module.web.dto.property.PropertyCompanyParam;
import com.coocaa.meht.module.web.entity.PropertyCompanyEntity;
import com.coocaa.meht.module.web.vo.property.PropertyCompanyVO;
import org.mapstruct.Mapper;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface PropertyCompanyConvert{
    PropertyCompanyConvert INSTANCE = Mappers.getMapper(PropertyCompanyConvert.class);

    /**
     * DTO转Entity
     */
    PropertyCompanyEntity toEntity(PropertyCompanyParam param);

    /**
     * Entity转VO
     */
    PropertyCompanyVO toVO(PropertyCompanyEntity entity);

    /**
     * Entity数组转VO数组
     */
    List<PropertyCompanyVO> toVOs(List<PropertyCompanyEntity> entities);


    /**
     * DTO转param
     */
    BuildingPropertyCompanyParam toPropertyCompanyParam(BuildingPropertyCompanyDTO dto);

}
