package com.coocaa.meht.common;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-23
 */
@Data
@Accessors(chain = true)
@JsonPropertyOrder({"id", "name", "wno", "mobile", "email", "userName", "status", "roleNames", "cityGroupNames", "roles", "cityGroups"})
public class UserVO {
    @Schema(description = "ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "姓名", type = "String", example = "张三")
    private String name;

    @Schema(description = "工号", type = "String", example = "CC2362")
    private String wno;

    @Schema(description = "手机号", type = "String", example = "13012345678")
    private String mobile;

    @Schema(description = "邮箱", type = "String", example = "<EMAIL>")
    private String email;

    @Schema(description = "登陆名", type = "String", example = "admin")
    private String userName;

    @Schema(description = "密码, 需要对明文进行MD5", type = "String", hidden = true)
    private String password;

    @Schema(description = "状态 [false:禁用, true:启用]", type = "Boolean", example = "true")
    private Boolean status;

    @Schema(description = "用户类型 [1:内部用户, 2:外部代理商]", type = "Integer", example = "1")
    private Integer type;

    @Schema(description = "创建时间", type = "Date", example = "2024-10-22 15:26:37")
    private Date createTime;

    @Schema(description = "创建人", type = "Integer", example = "1")
    private Integer creator;

    @Schema(description = "更新时间", type = "Date", example = "2024-10-22 15:26:37")
    private Date updateTime;

    @Schema(description = "操作人", type = "Integer", example = "1")
    private Integer operator;

}
