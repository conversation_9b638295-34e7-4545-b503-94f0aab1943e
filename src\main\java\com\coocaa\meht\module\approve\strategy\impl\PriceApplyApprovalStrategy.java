package com.coocaa.meht.module.approve.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.dto.ApprovalOperationDTO;
import com.coocaa.meht.module.approve.dto.InnerApproveTemplateParam;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;
import com.coocaa.meht.module.approve.strategy.ApprovalStrategy;
import com.coocaa.meht.module.web.dao.PriceApplyDao;
import com.coocaa.meht.module.web.dao.PriceApplyDeviceDao;
import com.coocaa.meht.module.web.dao.PriceApplyDevicePointDao;
import com.coocaa.meht.module.web.dto.PriceApplyDetailDto;
import com.coocaa.meht.module.web.dto.PriceApplyDeviceDto;
import com.coocaa.meht.module.web.dto.PriceApplyDto;
import com.coocaa.meht.module.web.entity.PriceApplyDeviceEntity;
import com.coocaa.meht.module.web.entity.PriceApplyDevicePointEntity;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.coocaa.meht.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 价格申请审批策略实现
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PriceApplyApprovalStrategy implements ApprovalStrategy {

    private final PriceApplyDao priceApplyDao;
    private final PriceApplyDeviceDao priceApplyDeviceDao;
    private final PriceApplyDevicePointDao priceApplyDevicePointDao;


    @Override
    public String getApproveType() {
        return ApprovalTypeEnum.PRICE_APPROVAL.getCode();
    }

    @Override
    public void validateData(ApprovalDTO data) {
        log.info("验证价格审批数据: {}", data);

    }

    @Override
    public List<InnerApproveTemplateParam> buildFormData(ApprovalDTO data) {
        return data.getForm();
    }

    @Override
    public void onFinish(String businessKey, ApprovalOperationDTO operationDTO) {
        try {
            updatePriceApplyStatus(businessKey, PriceApplyEntity.Status.PASSED.getCode(), operationDTO);
        } catch (Exception e) {
            log.error("处理价格审批通过异常", e);
            throw new ApprovalBusinessException("处理价格审批通过失败: " + e.getMessage());
        }
    }

    /**
     * 更新价格申请状态
     *
     * @param businessKey  业务标识
     * @param status       状态
     * @param operationDTO 审批操作数据
     */
    private void updatePriceApplyStatus(String businessKey, Integer status, ApprovalOperationDTO operationDTO) {
        String opinion = operationDTO.getOpinion();
        String operator = operationDTO.getOperator();
        // 更新价格申请状态
        LambdaUpdateWrapper<PriceApplyEntity> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(PriceApplyEntity::getApplyCode, businessKey);
        updateWrapper.set(PriceApplyEntity::getStatus, status);
        if (PriceApplyEntity.Status.PASSED.getCode().equals(status)) {
            updateWrapper.set(PriceApplyEntity::getApproveTime, LocalDateTime.now());
        }
        updateWrapper.set(StringUtils.isNotBlank(operator), PriceApplyEntity::getApproveBy, operator);
        updateWrapper.set(StringUtils.isNotBlank(opinion), PriceApplyEntity::getApproveRemark, opinion);
        priceApplyDao.update(new PriceApplyEntity(), updateWrapper);
    }

    @Override
    public void onRejected(String businessKey, ApprovalOperationDTO operationDTO) {
        log.info("处理价格审批拒绝, businessKey: {}, operationDTO: {}", businessKey, operationDTO);

        // 实际业务场景下，这里应该调用价格申请服务更新价格申请状态
        try {
            updatePriceApplyStatus(businessKey, PriceApplyEntity.Status.REJECTED.getCode(), operationDTO);
        } catch (Exception e) {
            log.error("处理价格审批拒绝异常", e);
            throw new ApprovalBusinessException("处理价格审批拒绝失败: " + e.getMessage());
        }
    }

    @Override
    public void onRevoke(String businessKey, ApprovalOperationDTO operationDTO) {
        log.info("处理价格审批取消, businessKey: {}, operationDTO: {}", businessKey, operationDTO);
        if (StringUtils.isEmpty(businessKey)) {
            throw new ApprovalBusinessException("价格审批取消失败: 业务标识不能为空");
        }
        try {
            // 改回草稿状态  draft字段设置
            LambdaQueryWrapper<PriceApplyEntity> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(PriceApplyEntity::getApplyCode, businessKey);
            PriceApplyEntity priceApply = priceApplyDao.selectOne(queryWrapper);
            priceApply.setStatus(PriceApplyEntity.Status.DRAFT.getCode());

            // 构建保存草稿的参数并保存
            PriceApplyDto applyDto = BeanUtil.copyProperties(priceApply, PriceApplyDto.class);
            // 获取设备点位信息
            List<PriceApplyDeviceEntity> deviceList = priceApplyDeviceDao.selectList(
                    new LambdaQueryWrapper<>(PriceApplyDeviceEntity.class)
                            .eq(PriceApplyDeviceEntity::getApplyId, priceApply.getId())
            );
            // 获取设备点位信息
            List<PriceApplyDevicePointEntity> pointList = priceApplyDevicePointDao.selectList(
                    new LambdaQueryWrapper<>(PriceApplyDevicePointEntity.class)
                            .eq(PriceApplyDevicePointEntity::getApplyId, priceApply.getId())
            );
            Map<Integer, List<PriceApplyDevicePointEntity>> pointByDeviceId = pointList.stream()
                    .collect(Collectors.groupingBy(PriceApplyDevicePointEntity::getPriceApplyDeviceId));

            // 处理附件信息
            if (StringUtils.isNotBlank(priceApply.getFileIds())) {
                List<Integer> list = Arrays.stream(priceApply.getFileIds().split(","))
                        .map(str -> Integer.parseInt(str.trim()))
                        .toList();
                applyDto.setFileIds(list);
            }

            // 退回草稿时候的样子
            ArrayList<PriceApplyDeviceDto> priceApplyDevices = new ArrayList<>();
            for (PriceApplyDeviceEntity device : deviceList) {
                PriceApplyDeviceDto priceApplyDeviceDto = BeanUtil.copyProperties(device, PriceApplyDeviceDto.class);
                List<PriceApplyDevicePointEntity> priceApplyDevicePointEntities = pointByDeviceId.get(device.getId());
                if (CollectionUtil.isNotEmpty(priceApplyDevicePointEntities)) {
                    priceApplyDeviceDto.setPoints(priceApplyDevicePointEntities.stream()
                            .map(point -> new PriceApplyDetailDto.DevicePointDto(point.getPointCode(), point.getPointName(), true))
                            .toList());
                }
                priceApplyDevices.add(priceApplyDeviceDto);
            }
            applyDto.setDevices(priceApplyDevices);
            priceApply.setDraft(JsonUtils.toJson(applyDto));
            priceApplyDao.updateById(priceApply);
        } catch (Exception e) {
            log.error("价格申请撤回异常", e);
            throw e;
        }
    }

    @Override
    public void afterApprove(ApprovalOperationDTO operationDTO) {
        log.info("价格审批通过后回调, operationDTO: {}", operationDTO);

        // 实现审批通过后的回调逻辑
        // 例如：发送通知、更新统计数据等
    }


    @Override
    public void beforeSubmit(ApprovalDTO dto) {
        log.info("价格审批策略-预处理, dto: {}", dto);
        // 可实现特定预处理逻辑
    }

    @Override
    public void afterSubmit(ApprovalDTO dto, String instanceCode) {
        log.info("价格审批策略-后处理, dto: {}, instanceCode: {}", dto, instanceCode);
        // 保存价格申请的实例编码
        String applyCode = dto.getBusinessKey();
        if (StringUtils.isBlank(applyCode)) {
            return;
        }
        priceApplyDao.update(new LambdaUpdateWrapper<PriceApplyEntity>()
                .eq(PriceApplyEntity::getApplyCode, applyCode)
                .set(PriceApplyEntity::getInstanceCode, instanceCode));
    }
} 