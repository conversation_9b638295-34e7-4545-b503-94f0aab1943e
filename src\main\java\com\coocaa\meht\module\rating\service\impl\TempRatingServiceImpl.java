package com.coocaa.meht.module.rating.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.rating.dao.TempRatingScoreMapper;
import com.coocaa.meht.module.rating.dto.RatingDTO;
import com.coocaa.meht.module.rating.entity.TempRating;
import com.coocaa.meht.module.rating.entity.TempThirdBuildingDb;
import com.coocaa.meht.module.rating.entity.TempThirdBuildingDeepseek;
import com.coocaa.meht.module.rating.entity.TempThirdBuildingOpenAi;
import com.coocaa.meht.module.rating.entity.TempThirdBuildingQw;
import com.coocaa.meht.module.rating.service.TempRatingService;
import com.coocaa.meht.module.rating.service.TempThirdBuildingDbService;
import com.coocaa.meht.module.rating.service.TempThirdBuildingDeepseekService;
import com.coocaa.meht.module.rating.service.TempThirdBuildingOpenAiService;
import com.coocaa.meht.module.rating.service.TempThirdBuildingQwService;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.CityCoefficientEntity;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingParameterService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.CityCoefficientService;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-28
 */
@Slf4j
@Service
public class TempRatingServiceImpl extends ServiceImpl<TempRatingScoreMapper, TempRating> implements TempRatingService {

    // 声明类级别线程池
    private final ExecutorService ratingExecutor = Executors.newFixedThreadPool(
            Runtime.getRuntime().availableProcessors() * 2,
            r -> {
                Thread t = new Thread(r, "rating-worker-");
                t.setDaemon(true);
                return t;
            });

    @Autowired
    private BuildingRatingService buildingRatingService;

    @Autowired
    private BuildingDetailsService buildingDetailsService;

    @Autowired
    private BuildingParameterService buildingParameterService;

    @Autowired
    private TempThirdBuildingDbService thirdBuildingDbService;

    @Autowired
    private TempThirdBuildingDeepseekService thirdBuildingDeepseekService;

    @Autowired
    private TempThirdBuildingQwService thirdBuildingQwService;

    @Autowired
    private TempThirdBuildingOpenAiService thirdBuildingOpenAiService;

    @Resource
    private CityCoefficientService cityCoefficientService;

    @PreDestroy
    public void destroy() {
        // 确保应用关闭时能够优雅地关闭线程池
        ratingExecutor.shutdown();
        try {
            if (!ratingExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                ratingExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            ratingExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    @Override
    public void rating(RatingDTO param) {
        // 查询需要评级的楼盘
        List<BuildingRatingEntity> buildingRatingEntities = buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getBuildingNo,
                        BuildingRatingEntity::getBuildingType,
                        BuildingRatingEntity::getMapAdCode)
                .in(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.getValue(),
                        BuildingRatingEntity.Status.AUDITED.getValue(), BuildingRatingEntity.Status.REJECTED.getValue())
                .in(CollUtil.isNotEmpty(param.getBuildingNos()), BuildingRatingEntity::getBuildingNo, param.getBuildingNos())
                .ge(StrUtil.isNotBlank(param.getStartTime()), BuildingRatingEntity::getCreateTime, param.getStartTime())
                .le(StrUtil.isNotBlank(param.getEndTime()), BuildingRatingEntity::getCreateTime, param.getEndTime())
                .list();

        if (CollUtil.isEmpty(buildingRatingEntities)) {
            log.info("没有需要评级的楼盘");
            return;
        }

        // 缓存参数实体，避免重复查询
        Map<Long, BuildingParameterEntity> parameterMapping = buildingParameterService.lambdaQuery()
                .ne(BuildingParameterEntity::getParentId, 0)
                .list()
                .stream()
                .collect(Collectors.toMap(BuildingParameterEntity::getId, Function.identity()));

        // 缓存新版规则
        List<BuildingParameterEntity> rules = parameterMapping.values().stream()
                .filter(parameter -> parameter.getDataFlag().equals(1))
                .toList();
        Map<Integer, List<BuildingParameterEntity>> typeMappingRule = rules.stream()
                .collect(Collectors.groupingBy(BuildingParameterEntity::getBuildingType));

        // 缓存城市系数
        Map<String, BigDecimal> cityCoefficientCache = new ConcurrentHashMap<>();

        // 并行处理所有楼盘的评级，使用类级别线程池
        List<CompletableFuture<Void>> futures = buildingRatingEntities.stream()
                .map(ratingEntity -> processRatingAsync(ratingEntity, parameterMapping, typeMappingRule, cityCoefficientCache))
                .toList();

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    private CompletableFuture<Void> processRatingAsync(BuildingRatingEntity ratingEntity,
                                                       Map<Long, BuildingParameterEntity> parameterMapping,
                                                       Map<Integer, List<BuildingParameterEntity>> typeMappingRule,
                                                       Map<String, BigDecimal> cityCoefficientCache) {
        // 获取城市系数（使用缓存）
        BigDecimal coefficient = cityCoefficientCache.computeIfAbsent(
                ratingEntity.getMapAdCode(),
                this::getCityCoefficient
        );

        // 并行执行四种评级处理，使用类级别线程池
        CompletableFuture<Void> manualFuture = CompletableFuture.runAsync(() -> {
            try {
                processManual(ratingEntity, parameterMapping, typeMappingRule, coefficient);
            } catch (Exception e) {
                log.error("{}，人工评级异常", ratingEntity.getBuildingNo(), e);
            }
        }, ratingExecutor);

        CompletableFuture<Void> dbFuture = CompletableFuture.runAsync(() -> {
            try {
                processDb(coefficient, ratingEntity, parameterMapping, typeMappingRule);
            } catch (Exception e) {
                log.error("{}，豆包评级异常", ratingEntity.getBuildingNo(), e);
            }
        }, ratingExecutor);

        CompletableFuture<Void> deepseekFuture = CompletableFuture.runAsync(() -> {
            try {
                processDeepseek(coefficient, ratingEntity, parameterMapping, typeMappingRule);
            } catch (Exception e) {
                log.error("{}，deepseek评级异常", ratingEntity.getBuildingNo(), e);
            }
        }, ratingExecutor);

        CompletableFuture<Void> qwFuture = CompletableFuture.runAsync(() -> {
            try {
                processQw(coefficient, ratingEntity, parameterMapping, typeMappingRule);
            } catch (Exception e) {
                log.error("{}，千问评级异常", ratingEntity.getBuildingNo(), e);
            }
        }, ratingExecutor);

        CompletableFuture<Void> openAiFuture = CompletableFuture.runAsync(() -> {
            try {
                processOpenAi(coefficient, ratingEntity, parameterMapping, typeMappingRule);
            } catch (Exception e) {
                log.error("{}，openAi评级异常", ratingEntity.getBuildingNo(), e);
            }
        }, ratingExecutor);

        // 合并所有异步任务
        return CompletableFuture.allOf(manualFuture, dbFuture, deepseekFuture, qwFuture, openAiFuture);
    }

    private void processManual(BuildingRatingEntity ratingEntity, Map<Long, BuildingParameterEntity> parameterMapping,
                               Map<Integer, List<BuildingParameterEntity>> typeMappingRule, BigDecimal coefficient) {
        // 人工评级
        BuildingDetailsEntity detailsEntity = buildingDetailsService.lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, ratingEntity.getBuildingNo())
                .last("limit 1")
                .one();

        if (Objects.isNull(detailsEntity)) {
            log.info("{}，人工评级数据不存在", ratingEntity.getBuildingNo());
            return;
        }

        TempRating dbRating = new TempRating();
        dbRating.setBuildingNo(ratingEntity.getBuildingNo());
        dbRating.setBuildingType(BuildingRatingEntity.BuildingType.getNameByValue(ratingEntity.getBuildingType()));
        // 人工数据
        dbRating.setSource(0);

        // 手动输入项目直接设置输入值
        dbRating.setBuildingNumber(detailsEntity.getBuildingNumberInput());
        dbRating.setBuildingPrice(detailsEntity.getBuildingPriceInput());
        dbRating.setBuildingAge(detailsEntity.getBuildingAgeInput());

        // 存储parameter表id的项目存储对应的parameterName
        setParameterName(detailsEntity.getBuildingGrade(), parameterMapping, dbRating::setBuildingGrade);
        setParameterName(detailsEntity.getBuildingLocation(), parameterMapping, dbRating::setBuildingLocation);
        setParameterName(detailsEntity.getBuildingExterior(), parameterMapping, dbRating::setBuildingExterior);
        setParameterName(detailsEntity.getBuildingLobby(), parameterMapping, dbRating::setBuildingLobby);
        setParameterName(detailsEntity.getBuildingGarage(), parameterMapping, dbRating::setBuildingGarage);
        setParameterName(detailsEntity.getBuildingHall(), parameterMapping, dbRating::setBuildingHall);
        setParameterName(detailsEntity.getBuildingBrand(), parameterMapping, dbRating::setBuildingBrand);
        setParameterName(detailsEntity.getBuildingRating(), parameterMapping, dbRating::setBuildingRating);
        setParameterName(detailsEntity.getBuildingSettled(), parameterMapping, dbRating::setBuildingSettled);

        // 转换为detail
        BuildingDetailsEntity details = new BuildingDetailsEntity();
        // 手动输入值设置输入值
        details.setBuildingNumberInput(dbRating.getBuildingNumber());
        details.setBuildingPriceInput(dbRating.getBuildingPrice());
        details.setBuildingAgeInput(dbRating.getBuildingAge());
        // 下拉选择值映射新规则id
        setParameterId(typeMappingRule.get(ratingEntity.getBuildingType()), dbRating.getBuildingGrade(), details::setBuildingGrade);
        setParameterId(typeMappingRule.get(ratingEntity.getBuildingType()), dbRating.getBuildingLocation(), details::setBuildingLocation);
        setParameterId(typeMappingRule.get(ratingEntity.getBuildingType()), dbRating.getBuildingExterior(), details::setBuildingExterior);
        setParameterId(typeMappingRule.get(ratingEntity.getBuildingType()), dbRating.getBuildingLobby(), details::setBuildingLobby);
        setParameterId(typeMappingRule.get(ratingEntity.getBuildingType()), dbRating.getBuildingGarage(), details::setBuildingGarage);
        setParameterId(typeMappingRule.get(ratingEntity.getBuildingType()), dbRating.getBuildingHall(), details::setBuildingHall);
        setParameterId(typeMappingRule.get(ratingEntity.getBuildingType()), dbRating.getBuildingBrand(), details::setBuildingBrand);
        setParameterId(typeMappingRule.get(ratingEntity.getBuildingType()), dbRating.getBuildingRating(), details::setBuildingRating);
        setParameterId(typeMappingRule.get(ratingEntity.getBuildingType()), dbRating.getBuildingSettled(), details::setBuildingSettled);

        // 获取所有评分参数
        Map<String, List<BuildingParameterEntity>> parameterMap = typeMappingRule.get(ratingEntity.getBuildingType()).stream()
                .collect(Collectors.groupingBy(BuildingParameterEntity::getParameterCode));

        // 计算手动输入的参数项：楼层，月租金，楼龄
        buildingRatingService.processNumberMatch(details, "buildingNumber", parameterMap);
        buildingRatingService.processRentMatch(details, "buildingPrice", parameterMap, ratingEntity.getMapAdCode(), ratingEntity.getBuildingType());
        buildingRatingService.processNumberMatch(details, "buildingAge", parameterMap);

        // 计算总分
        BigDecimal score = calculateScoreByBuildingType(details, ratingEntity.getMapAdCode(),
                ratingEntity.getBuildingType(), typeMappingRule.get(ratingEntity.getBuildingType()), coefficient);
        // 计算评级
        String projectLevel = buildingRatingService.calculateProjectLevel2(score);

        dbRating.setBuildingScore(score);
        dbRating.setProjectLevel(projectLevel);

        // 设置各项分数值
        doSetScore(details.getBuildingNumber(), parameterMapping, dbRating::setBuildingNumberScore);
        doSetScore(details.getBuildingPrice(), parameterMapping, dbRating::setBuildingPriceScore);
        doSetScore(details.getBuildingAge(), parameterMapping, dbRating::setBuildingAgeScore);
        doSetScore(details.getBuildingGrade(), parameterMapping, dbRating::setBuildingGradeScore);
        doSetScore(details.getBuildingLocation(), parameterMapping, dbRating::setBuildingLocationScore);
        doSetScore(details.getBuildingExterior(), parameterMapping, dbRating::setBuildingExteriorScore);
        doSetScore(details.getBuildingLobby(), parameterMapping, dbRating::setBuildingLobbyScore);
        doSetScore(details.getBuildingGarage(), parameterMapping, dbRating::setBuildingGarageScore);
        doSetScore(details.getBuildingHall(), parameterMapping, dbRating::setBuildingHallScore);
        doSetScore(details.getBuildingBrand(), parameterMapping, dbRating::setBuildingBrandScore);
        doSetScore(details.getBuildingRating(), parameterMapping, dbRating::setBuildingRatingScore);
        doSetScore(details.getBuildingSettled(), parameterMapping, dbRating::setBuildingSettledScore);

        saveOrUpdateTempRating(dbRating);
    }

    public BigDecimal calculateScoreByBuildingType(BuildingDetailsEntity details, String adCode,
                                                   Integer buildingType, List<BuildingParameterEntity> reules, BigDecimal coefficient) {
        // 根据楼宇类型计算评分, 0 写字楼 1 商住楼 2 综合体 3 产业园区
        List<Long> scoreIds = new ArrayList<>();
        switch (buildingType) {
            case 0:
                scoreIds.add(details.getBuildingGrade());
                scoreIds.add(details.getBuildingLocation());
                scoreIds.add(details.getBuildingNumber());
                scoreIds.add(details.getBuildingPrice());
                scoreIds.add(details.getBuildingAge());
                scoreIds.add(details.getBuildingExterior());
                scoreIds.add(details.getBuildingLobby());
                scoreIds.add(details.getBuildingGarage());
                break;
            case 1:
                scoreIds.add(details.getBuildingAge());
                scoreIds.add(details.getBuildingLocation());
                scoreIds.add(details.getBuildingNumber());
                scoreIds.add(details.getBuildingPrice());
                scoreIds.add(details.getBuildingExterior());
                scoreIds.add(details.getBuildingHall());
                scoreIds.add(details.getBuildingGarage());
                break;
            case 2:
                scoreIds.add(details.getBuildingBrand());
                scoreIds.add(details.getBuildingLocation());
                scoreIds.add(details.getBuildingRating());
                break;
            case 3:
                scoreIds.add(details.getBuildingAge());
                scoreIds.add(details.getBuildingLocation());
                scoreIds.add(details.getBuildingNumber());
                scoreIds.add(details.getBuildingSettled());
                scoreIds.add(details.getBuildingExterior());
                scoreIds.add(details.getBuildingHall());
                break;
            default:
                throw new ServerException("当前楼宇类型不存在");
        }

        List<BuildingParameterEntity> list = reules.stream().filter(entity -> scoreIds.contains(entity.getId())).toList();

        BigDecimal totalScore = BigDecimal.ZERO;
        for (BuildingParameterEntity entity : list) {
            BigDecimal score = entity.getParameterScore().multiply(entity.getWeightValue().divide(new BigDecimal(100)));
            totalScore = totalScore.add(score);
        }
        // 获取城市系数
        return totalScore.multiply(coefficient);
    }

    private void setParameterId(List<BuildingParameterEntity> entities, String name, Consumer<Long> consumer) {
        if (StrUtil.isBlank(name) || CollUtil.isEmpty(entities)) {
            return;
        }

        for (BuildingParameterEntity entity : entities) {
            if (entity.getParameterName().equals(name)) {
                consumer.accept(entity.getId());
            }
        }
    }

    private void processDb(BigDecimal coefficient, BuildingRatingEntity ratingEntity,
                           Map<Long, BuildingParameterEntity> parameterMapping, Map<Integer, List<BuildingParameterEntity>> typeMappingRule) {
        TempThirdBuildingDb aiData = thirdBuildingDbService.lambdaQuery()
                .eq(TempThirdBuildingDb::getBuildingNo, ratingEntity.getBuildingNo())
                .last("limit 1")
                .one();
        if (Objects.isNull(aiData)) {
            log.info("{}，豆包数据不存在", ratingEntity.getBuildingNo());
            return;
        }

        TempRating dbRating = new TempRating();
        dbRating.setBuildingNo(ratingEntity.getBuildingNo());
        dbRating.setBuildingType(aiData.getThirdBuildingType());
        dbRating.setSource(1);
        dbRating.setBuildingPrice(aiData.getThirdBuildingPrice());
        dbRating.setBuildingGrade(aiData.getThirdBuildingGrade());
        dbRating.setBuildingLocation(aiData.getThirdBuildingLocation());
        dbRating.setBuildingAge(aiData.getThirdBuildingAge());
        dbRating.setBuildingExterior(aiData.getThirdBuildingExterior());
        dbRating.setBuildingRating(aiData.getThirdBuildingRate());
        dbRating.setBuildingNumber(aiData.getThirdBuildingNumber());
        dbRating.setBuildingGarage(aiData.getThirdBuildingGarage());
        dbRating.setBuildingBrand(aiData.getThirdBuildingBrand());
        dbRating.setBuildingLobby(aiData.getThirdBuildingLobby());

        Integer buildingType = convertBuildingType(dbRating.getBuildingType());
        if (Objects.isNull(buildingType)) {
            log.info("{}，豆包楼宇类型({})未匹配上", ratingEntity.getBuildingNo(), dbRating.getBuildingType());
            return;
        }

        BuildingDetailsEntity details = toDetails(dbRating);

        // 计算评分
        BuildingDetailsEntity processedDetails = buildingRatingService.processDetailData(typeMappingRule.get(buildingType), details, buildingType, ratingEntity.getMapAdCode());

        // 计算总分
        BigDecimal score = calculateAIScoreByBuildingType(details, ratingEntity.getMapAdCode(), buildingType, coefficient, typeMappingRule.get(buildingType));

        // 计算评级
        String projectLevel = buildingRatingService.calculateProjectLevel2(score);

        dbRating.setBuildingScore(score);
        dbRating.setProjectLevel(projectLevel);

        // 设置各项分数值
        setScore(parameterMapping, processedDetails, dbRating);

        saveOrUpdateTempRating(dbRating);
    }

    private void processDeepseek(BigDecimal coefficient, BuildingRatingEntity ratingEntity,
                                 Map<Long, BuildingParameterEntity> parameterMapping, Map<Integer, List<BuildingParameterEntity>> typeMappingRule) {
        TempThirdBuildingDeepseek aiData = thirdBuildingDeepseekService.lambdaQuery()
                .eq(TempThirdBuildingDeepseek::getBuildingNo, ratingEntity.getBuildingNo())
                .last("limit 1")
                .one();

        if (Objects.isNull(aiData)) {
            log.info("{}，deepseek数据不存在", ratingEntity.getBuildingNo());
            return;
        }

        TempRating dbRating = new TempRating();
        dbRating.setBuildingNo(ratingEntity.getBuildingNo());
        dbRating.setBuildingType(aiData.getThirdBuildingType());
        dbRating.setSource(2);
        dbRating.setBuildingPrice(aiData.getThirdBuildingPrice());
        dbRating.setBuildingGrade(aiData.getThirdBuildingGrade());
        dbRating.setBuildingLocation(aiData.getThirdBuildingLocation());
        dbRating.setBuildingAge(aiData.getThirdBuildingAge());
        dbRating.setBuildingExterior(aiData.getThirdBuildingExterior());
        dbRating.setBuildingRating(aiData.getThirdBuildingRate());
        dbRating.setBuildingNumber(aiData.getThirdBuildingNumber());
        dbRating.setBuildingGarage(aiData.getThirdBuildingGarage());
        dbRating.setBuildingBrand(aiData.getThirdBuildingBrand());
        dbRating.setBuildingLobby(aiData.getThirdBuildingLobby());

        Integer buildingType = convertBuildingType(dbRating.getBuildingType());
        if (Objects.isNull(buildingType)) {
            log.info("{}，deepseek楼宇类型({})未匹配上", ratingEntity.getBuildingNo(), dbRating.getBuildingType());
            return;
        }

        BuildingDetailsEntity details = toDetails(dbRating);

        // 计算评分
        BuildingDetailsEntity processedDetails = buildingRatingService.processDetailData(typeMappingRule.get(buildingType), details, buildingType, ratingEntity.getMapAdCode());

        // 计算总分
        BigDecimal score = calculateAIScoreByBuildingType(details, ratingEntity.getMapAdCode(), buildingType, coefficient, typeMappingRule.get(buildingType));

        // 计算评级
        String projectLevel = buildingRatingService.calculateProjectLevel2(score);

        dbRating.setBuildingScore(score);
        dbRating.setProjectLevel(projectLevel);

        // 设置各项分数值
        setScore(parameterMapping, processedDetails, dbRating);

        saveOrUpdateTempRating(dbRating);
    }

    private void processQw(BigDecimal coefficient, BuildingRatingEntity ratingEntity,
                           Map<Long, BuildingParameterEntity> parameterMapping, Map<Integer, List<BuildingParameterEntity>> typeMappingRule) {
        TempThirdBuildingQw aiData = thirdBuildingQwService.lambdaQuery()
                .eq(TempThirdBuildingQw::getBuildingNo, ratingEntity.getBuildingNo())
                .last("limit 1")
                .one();

        if (Objects.isNull(aiData)) {
            log.info("{}，千问数据不存在", ratingEntity.getBuildingNo());
            return;
        }

        TempRating dbRating = new TempRating();
        dbRating.setBuildingNo(ratingEntity.getBuildingNo());
        dbRating.setBuildingType(aiData.getThirdBuildingType());
        dbRating.setSource(3);
        dbRating.setBuildingPrice(aiData.getThirdBuildingPrice());
        dbRating.setBuildingGrade(aiData.getThirdBuildingGrade());
        dbRating.setBuildingLocation(aiData.getThirdBuildingLocation());
        dbRating.setBuildingAge(aiData.getThirdBuildingAge());
        dbRating.setBuildingExterior(aiData.getThirdBuildingExterior());
        dbRating.setBuildingRating(aiData.getThirdBuildingRate());
        dbRating.setBuildingNumber(aiData.getThirdBuildingNumber());
        dbRating.setBuildingGarage(aiData.getThirdBuildingGarage());
        dbRating.setBuildingBrand(aiData.getThirdBuildingBrand());
        dbRating.setBuildingLobby(aiData.getThirdBuildingLobby());

        Integer buildingType = convertBuildingType(dbRating.getBuildingType());
        if (Objects.isNull(buildingType)) {
            log.info("{}，千问楼宇类型({})未匹配上", ratingEntity.getBuildingNo(), dbRating.getBuildingType());
            return;
        }

        BuildingDetailsEntity details = toDetails(dbRating);

        // 计算评分
        BuildingDetailsEntity processedDetails = buildingRatingService.processDetailData(typeMappingRule.get(buildingType), details, buildingType, ratingEntity.getMapAdCode());

        // 计算总分
        BigDecimal score = calculateAIScoreByBuildingType(details, ratingEntity.getMapAdCode(), buildingType, coefficient, typeMappingRule.get(buildingType));

        // 计算评级
        String projectLevel = buildingRatingService.calculateProjectLevel2(score);

        dbRating.setBuildingScore(score);
        dbRating.setProjectLevel(projectLevel);

        // 设置各项分数值
        setScore(parameterMapping, processedDetails, dbRating);

        saveOrUpdateTempRating(dbRating);
    }

    private void processOpenAi(BigDecimal coefficient, BuildingRatingEntity ratingEntity,
                               Map<Long, BuildingParameterEntity> parameterMapping, Map<Integer, List<BuildingParameterEntity>> typeMappingRule) {
        TempThirdBuildingOpenAi aiData = thirdBuildingOpenAiService.lambdaQuery()
                .eq(TempThirdBuildingOpenAi::getBuildingNo, ratingEntity.getBuildingNo())
                .last("limit 1")
                .one();

        if (Objects.isNull(aiData)) {
            log.info("{}，openAi数据不存在", ratingEntity.getBuildingNo());
            return;
        }

        TempRating dbRating = new TempRating();
        dbRating.setBuildingNo(ratingEntity.getBuildingNo());
        dbRating.setBuildingType(aiData.getThirdBuildingType());
        dbRating.setSource(4);
        dbRating.setBuildingPrice(aiData.getThirdBuildingPrice());
        dbRating.setBuildingGrade(aiData.getThirdBuildingGrade());
        dbRating.setBuildingLocation(aiData.getThirdBuildingLocation());
        dbRating.setBuildingAge(aiData.getThirdBuildingAge());
        dbRating.setBuildingExterior(aiData.getThirdBuildingExterior());
        dbRating.setBuildingRating(aiData.getThirdBuildingRate());
        dbRating.setBuildingNumber(aiData.getThirdBuildingNumber());
        dbRating.setBuildingGarage(aiData.getThirdBuildingGarage());
        dbRating.setBuildingBrand(aiData.getThirdBuildingBrand());
        dbRating.setBuildingLobby(aiData.getThirdBuildingLobby());

        Integer buildingType = convertBuildingType(dbRating.getBuildingType());
        if (Objects.isNull(buildingType)) {
            log.info("{}，openAi楼宇类型({})未匹配上", ratingEntity.getBuildingNo(), dbRating.getBuildingType());
            return;
        }

        BuildingDetailsEntity details = toDetails(dbRating);

        // 计算评分
        BuildingDetailsEntity processedDetails = buildingRatingService.processDetailData(typeMappingRule.get(buildingType), details, buildingType, ratingEntity.getMapAdCode());

        // 计算总分
        BigDecimal score = calculateAIScoreByBuildingType(details, ratingEntity.getMapAdCode(), buildingType, coefficient, typeMappingRule.get(buildingType));

        // 计算评级
        String projectLevel = buildingRatingService.calculateProjectLevel2(score);

        dbRating.setBuildingScore(score);
        dbRating.setProjectLevel(projectLevel);

        // 设置各项分数值
        setScore(parameterMapping, processedDetails, dbRating);

        saveOrUpdateTempRating(dbRating);
    }

    private void saveOrUpdateTempRating(TempRating dbRating) {
        TempRating exist = lambdaQuery()
                .select(TempRating::getId)
                .eq(TempRating::getBuildingNo, dbRating.getBuildingNo())
                .eq(TempRating::getSource, dbRating.getSource())
                .last("limit 1")
                .one();
        if (Objects.nonNull(exist)) {
            dbRating.setId(exist.getId());
            updateById(dbRating);
        } else {
            save(dbRating);
        }
    }

    private void setScore(Map<Long, BuildingParameterEntity> parameterMapping, BuildingDetailsEntity processedDetails, TempRating dbRating) {
        doSetScore(processedDetails.getThirdBuildingGradeId(), parameterMapping, dbRating::setBuildingGradeScore);
        doSetScore(processedDetails.getThirdBuildingLocationId(), parameterMapping, dbRating::setBuildingLocationScore);
        doSetScore(processedDetails.getThirdBuildingNumberId(), parameterMapping, dbRating::setBuildingNumberScore);
        doSetScore(processedDetails.getThirdBuildingPriceId(), parameterMapping, dbRating::setBuildingPriceScore);
        doSetScore(processedDetails.getThirdBuildingAgeId(), parameterMapping, dbRating::setBuildingAgeScore);
        doSetScore(processedDetails.getThirdBuildingExteriorId(), parameterMapping, dbRating::setBuildingExteriorScore);
        doSetScore(processedDetails.getThirdBuildingLobbyId(), parameterMapping, dbRating::setBuildingLobbyScore);
        doSetScore(processedDetails.getThirdBuildingGarageId(), parameterMapping, dbRating::setBuildingGarageScore);
        doSetScore(processedDetails.getThirdBuildingHallId(), parameterMapping, dbRating::setBuildingHallScore);
        doSetScore(processedDetails.getThirdBuildingBrandId(), parameterMapping, dbRating::setBuildingBrandScore);
        doSetScore(processedDetails.getThirdBuildingRatingId(), parameterMapping, dbRating::setBuildingRatingScore);
        doSetScore(processedDetails.getThirdBuildingSettledId(), parameterMapping, dbRating::setBuildingSettledScore);
    }

    private BuildingDetailsEntity toDetails(TempRating tempRating) {
        BuildingDetailsEntity details = new BuildingDetailsEntity();
        details.setThirdBuildingGrade(tempRating.getBuildingGrade());
        details.setThirdBuildingLocation(tempRating.getBuildingLocation());
        details.setThirdBuildingNumber(tempRating.getBuildingNumber());
        details.setThirdBuildingPrice(tempRating.getBuildingPrice());
        details.setThirdBuildingExterior(tempRating.getBuildingExterior());
        details.setThirdBuildingLobby(tempRating.getBuildingLobby());
        details.setThirdBuildingGarage(tempRating.getBuildingGarage());
        details.setThirdBuildingBrand(tempRating.getBuildingBrand());
        details.setThirdBuildingRating(tempRating.getBuildingRating());
        details.setThirdBuildingSettled(tempRating.getBuildingSettled());
        details.setThirdBuildingHall(tempRating.getBuildingHall());
        return details;
    }

    private void setParameterName(Long parameterId, Map<Long, BuildingParameterEntity> parameterMapping, Consumer<String> consumer) {
        if (Objects.isNull(parameterId)) {
            return;
        }
        BuildingParameterEntity parameter = parameterMapping.get(parameterId);
        consumer.accept(Objects.isNull(parameter) ? "" : parameter.getParameterName());
    }

    private void doSetScore(Long parameterId, Map<Long, BuildingParameterEntity> parameterMapping, Consumer<BigDecimal> consumer) {
        if (Objects.isNull(parameterId)) {
            return;
        }
        BuildingParameterEntity parameter = parameterMapping.get(parameterId);
        if (Objects.nonNull(parameter)) {
            consumer.accept(parameter.getParameterScore());
        }
    }

    public BigDecimal getCityCoefficient(String adCode) {
        CityCoefficientEntity coefficient = cityCoefficientService.getCoefficient(adCode);
        if (Objects.nonNull(coefficient)) {
            return coefficient.getCoefficient();
        } else {
            return BigDecimal.ONE;
        }
    }

    public Integer convertBuildingType(String buildingType) {
        if (StrUtil.isBlank(buildingType)) {
            return null;
        }

        if (buildingType.contains(BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getName())) {
            return BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue();
        }

        if (buildingType.contains(BuildingRatingEntity.BuildingType.COMMERCIAL_RESIDENTIAL_BUILDING.getName())) {
            return BuildingRatingEntity.BuildingType.COMMERCIAL_RESIDENTIAL_BUILDING.getValue();
        }

        if (buildingType.contains(BuildingRatingEntity.BuildingType.COMPLEX.getName())) {
            return BuildingRatingEntity.BuildingType.COMPLEX.getValue();
        }

        if (buildingType.contains(BuildingRatingEntity.BuildingType.INDUSTRIAL_PARK.getName())) {
            return BuildingRatingEntity.BuildingType.INDUSTRIAL_PARK.getValue();
        }

        return null;
    }

    public BigDecimal calculateAIScoreByBuildingType(BuildingDetailsEntity details, String adCode,
                                                     Integer buildingType, BigDecimal coefficient, List<BuildingParameterEntity> reules) {
        // 根据楼宇类型计算评分, 0 写字楼 1 商住楼 2 综合体 3 产业园区
        List<Long> scoreIds = new ArrayList<>();
        switch (buildingType) {
            case 0:
                scoreIds.add(details.getThirdBuildingGradeId());
                scoreIds.add(details.getThirdBuildingLocationId());
                scoreIds.add(details.getThirdBuildingNumberId());
                scoreIds.add(details.getThirdBuildingPriceId());
                scoreIds.add(details.getThirdBuildingAgeId());
                scoreIds.add(details.getThirdBuildingExteriorId());
                scoreIds.add(details.getThirdBuildingLobbyId());
                scoreIds.add(details.getThirdBuildingGarageId());
                break;
            case 1:
                scoreIds.add(details.getThirdBuildingAgeId());
                scoreIds.add(details.getThirdBuildingLocationId());
                scoreIds.add(details.getThirdBuildingNumberId());
                scoreIds.add(details.getThirdBuildingPriceId());
                scoreIds.add(details.getThirdBuildingExteriorId());
                scoreIds.add(details.getThirdBuildingHallId());
                scoreIds.add(details.getThirdBuildingGarageId());
                break;
            case 2:
                scoreIds.add(details.getThirdBuildingBrandId());
                scoreIds.add(details.getThirdBuildingLocationId());
                scoreIds.add(details.getThirdBuildingRatingId());
                break;
            case 3:
                scoreIds.add(details.getThirdBuildingAgeId());
                scoreIds.add(details.getThirdBuildingLocationId());
                scoreIds.add(details.getThirdBuildingNumberId());
                scoreIds.add(details.getThirdBuildingSettledId());
                scoreIds.add(details.getThirdBuildingExteriorId());
                scoreIds.add(details.getThirdBuildingHallId());
                break;
            default:
                throw new ServerException("当前楼宇类型不存在");
        }

        List<BuildingParameterEntity> list = reules.stream().filter(entity -> scoreIds.contains(entity.getId())).toList();

        BigDecimal totalScore = BigDecimal.ZERO;
        for (BuildingParameterEntity entity : list) {
            BigDecimal score = entity.getParameterScore().multiply(entity.getWeightValue().divide(new BigDecimal(100)));
            totalScore = totalScore.add(score);
        }
        // 乘以城市系数
        return totalScore.multiply(coefficient);
    }

}
