package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.module.api.ark.DouAiService;
import com.coocaa.meht.common.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * 豆包AI
 */
@RestController
@RequestMapping("douAi")
public class DouAiController {


    @Resource
    private DouAiService douAiService;


    @Anonymous
    @GetMapping("/sendMessage")
    public Result<?>  sendMessage(String message){
        return Result.ok( douAiService.getBuildingAppraiser(message));
    }


}
