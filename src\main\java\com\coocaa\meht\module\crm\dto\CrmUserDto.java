package com.coocaa.meht.module.crm.dto;


import com.coocaa.meht.module.web.dto.crm.CrmResultDto;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CrmUserDto extends CrmResultDto {

    private String realname;

    private String mobile;

    private String deptId;

    private String deptName;

    private String userId;

    private String email;

    private String parentId;

    private String parentName;

}
