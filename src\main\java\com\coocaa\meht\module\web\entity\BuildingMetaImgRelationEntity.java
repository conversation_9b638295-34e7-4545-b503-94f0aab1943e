package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 楼宇主数据图片管理信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("building_meta_img_relation")
public class BuildingMetaImgRelationEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String buildingMetaNo;

    /**
     * 图片类型：1.外墙材料 2.大堂高度 3.楼梯厅装饰 4.大堂环境图附件地址 5.梯厅环境图附件地址 6.闸口图附件地址 7.安装示意图附件地址
     */
    private Integer imgType;

    /**
     * 图片地址url
     */
    private String imgUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;


}
