package com.coocaa.meht.module.web.vo.common;

import com.coocaa.meht.module.web.enums.UserTypeEnum;
import lombok.Data;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-09
 */
@Data
public class UserVO {
    private Integer id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 工号
     */
    private String wno;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户类型 [1:内部用户, 2:外部代理商]
     */
    private Integer type;

    public boolean isInner() {
        return UserTypeEnum.INNER.getType().equals(type);
    }

}
