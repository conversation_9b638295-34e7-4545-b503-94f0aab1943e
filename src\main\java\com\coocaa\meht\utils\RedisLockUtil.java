package com.coocaa.meht.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁工具类
 */
@Slf4j
@Component
public class RedisLockUtil {

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final long DEFAULT_EXPIRE = 30L; // 默认锁过期时间30秒
    private static final long DEFAULT_WAIT_TIME = 3L; // 默认等待获取锁时间3秒
    private static final String LOCK_PREFIX = "lock:"; // 锁key前缀

    private static final String UNLOCK_SCRIPT = 
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
            "    return redis.call('del', KEYS[1]) " +
            "else " +
            "    return 0 " +
            "end";

    /**
     * 获取分布式锁
     *
     * @param lockKey 锁的key
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey) {
        return tryLock(lockKey, DEFAULT_EXPIRE, DEFAULT_WAIT_TIME);
    }

    /**
     * 获取分布式锁
     *
     * @param lockKey    锁的key
     * @param expireTime 锁的过期时间(秒)
     * @param waitTime   等待获取锁的时间(秒)
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, long expireTime, long waitTime) {
        String key = LOCK_PREFIX + lockKey;
        String threadId = Thread.currentThread().getId() + "";
        long startTime = System.currentTimeMillis();

        try {
            while ((System.currentTimeMillis() - startTime) < TimeUnit.SECONDS.toMillis(waitTime)) {
                Boolean success = redisTemplate.opsForValue()
                        .setIfAbsent(key, threadId, expireTime, TimeUnit.SECONDS);
                if (Boolean.TRUE.equals(success)) {
                    return true;
                }
                // 短暂休眠，避免频繁请求redis
                Thread.sleep(100);
            }
        } catch (Exception e) {
            log.error("获取分布式锁异常, key:{}", key, e);
        }
        return false;
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey 锁的key
     */
    public void unlock(String lockKey) {
        String key = LOCK_PREFIX + lockKey;
        String threadId = Thread.currentThread().getId() + "";
        try {
            DefaultRedisScript<Long> unlockScript = new DefaultRedisScript<>(UNLOCK_SCRIPT, Long.class);
            redisTemplate.execute(unlockScript, Collections.singletonList(key), threadId);
        } catch (Exception e) {
            log.error("释放分布式锁异常, key:{}", key, e);
        }
    }

    /**
     * 使用分布式锁执行任务
     *
     * @param lockKey  锁的key
     * @param runnable 要执行的任务
     * @return 是否执行成功
     */
    public boolean executeWithLock(String lockKey, Runnable runnable) {
        if (tryLock(lockKey)) {
            try {
                runnable.run();
                return true;
            } finally {
                unlock(lockKey);
            }
        }
        return false;
    }
} 