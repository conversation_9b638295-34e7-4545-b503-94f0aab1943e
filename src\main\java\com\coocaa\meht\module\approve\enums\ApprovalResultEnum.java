package com.coocaa.meht.module.approve.enums;

import com.coocaa.meht.module.web.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 站内审批状态 字典0141
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Getter
@AllArgsConstructor
public enum ApprovalResultEnum implements IEnumType<String> {

    PENDING("0141-1", "待审批"),
    PROCESSING("0141-2", "审批中"),
    CANCELED("0141-3", "已取消"),
    FINISHED("0141-4", "已完成"),
    REJECTED("0141-5", "已驳回");

    private final String code;
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static ApprovalResultEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (ApprovalResultEnum result : values()) {
            if (result.code.equals(value)) {
                return result;
            }
        }
        return null;
    }
} 