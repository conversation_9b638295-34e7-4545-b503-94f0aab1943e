package com.coocaa.meht.module.api.ark;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.coocaa.meht.utils.HttpUtils;
import com.coocaa.meht.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class DouAiService {


    @Value("${douAi:https://ark.cn-beijing.volces.com/}")
    private String doMain;

    @Value("${apiKey:c257efd9-0476-4911-b6aa-f67ae5699a57}")
    private String apiKey;

    // 获取评级项指标值
    @Value("${aiBot:bot-20250618155101-rhg5d}")
    private String bot;

    // 获取楼宇类型模型
    @Value("${aiTypeBot:bot-20250602164401-wqwvp}")
    private String typeBot;

    // 获取楼宇类型模型apiKey
    @Value("${typeApiKey:e0777c8d-e552-46f8-a29c-e6e43d298beb}")
    private String typeApiKey;


    public Map<String,Object> getBuildingAppraiser(String address){
        Map<String,Object> map = new HashMap<String,Object>();
        Map<String, String> header = new HashMap<>();
        header.put("Accept", "*/*");
        header.put("Connection", "keep-alive");
        header.put("Authorization",  "Bearer " + apiKey);
        header.put("Content-Type", "application/json; charset=utf-8;");
        map.put("model",bot);
        map.put("object","chat.completion");
        Map<String,Object> streamOptions = new HashMap<String,Object>();
        streamOptions.put("include_usage",true);
        map.put("stream_options",streamOptions);
        Map<String,Object> messages = new HashMap<String,Object>();
        messages.put("role","user");
        messages.put("content",address);
        map.put("messages", Arrays.asList(messages));
        try {
            String response = HttpUtils.post(doMain+"api/v3/bots/chat/completions", header, JsonUtils.toJson(map));
            Map<String, Object> objectMap = JsonUtils.fromMap(response);
            Object choices = objectMap.get("choices");
            List<Map<String, Object>> choicesList = JsonUtils.fromListMap(JsonUtils.toJson(choices));
            if (CollectionUtils.isNotEmpty(choicesList)){
                Map<String, Object> stringObjectMap = choicesList.get(0);
                Object message = stringObjectMap.get("message");
                Object content = JsonUtils.fromMap(JsonUtils.toJson(message)).get("content");
                return JsonUtils.fromMap(content.toString());
            }else {
                return new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(response.toString(), new TypeReference<Map<String,Object>>() {
                });
            }

        } catch (Exception e) {
            log.error("获取sqlerror", e);
        }
        return null;
    }

    public List<String> getBuildingTypes(String address) {
        Map<String, Object> map = new HashMap<>();
        Map<String, String> header = new HashMap<>();
        header.put("Accept", "*/*");
        header.put("Connection", "keep-alive");
        header.put("Authorization", "Bearer " + typeApiKey);
        header.put("Content-Type", "application/json; charset=utf-8;");
        map.put("model", typeBot);
        map.put("object", "chat.completion");

        Map<String, Object> streamOptions = new HashMap<>();
        streamOptions.put("include_usage", true);
        map.put("stream_options", streamOptions);

        Map<String, Object> messages = new HashMap<>();
        messages.put("role", "user");
        messages.put("content", address);
        map.put("messages", Arrays.asList(messages));
        try {
            String body = JSON.toJSONString(map);
            log.debug("请求体JSON: {}", body);

            log.info("开始调用豆包AI评估API...");
            String response = HttpUtils.post(doMain + "api/v3/bots/chat/completions", header, body);
            log.info("豆包AI评估API响应: {}", response);

            Map<String, Object> objectMap = JSON.parseObject(response, new TypeReference<Map<String, Object>>() {
            }.getType());
            Object choices = objectMap.get("choices");
            List<Map<String, Object>> choicesList = JSON.parseArray(JSON.toJSONString(choices),
                    new TypeReference<Map<String, Object>>() {
                    }.getType());
            if (CollectionUtils.isEmpty(choicesList)) {
                log.warn("API返回的选择列表为空");
                return Collections.emptyList();
            }
            Map<String, Object> stringObjectMap = choicesList.get(0);
            Object message = stringObjectMap.get("message");
            Object content = JSON.parseObject(JSON.toJSONString(message)).get("content");
            String contentStr = content.toString();
            log.debug("原始响应内容: {}", contentStr);

            // 截取第一个{之后和第一个}之前的字符串
            int startIndex = contentStr.indexOf("[");
            int endIndex = contentStr.indexOf("]", startIndex);
            if (startIndex < 0 || endIndex <= startIndex) {
                log.error("未找到有效的JSON结构");
                return Collections.emptyList();
            }

            String extractedStr = contentStr.substring(startIndex, endIndex + 1);
            log.debug("提取后的JSON内容: {}", extractedStr);
            if (StringUtils.isBlank(extractedStr)) {
                log.warn("提取的JSON内容为空");
                return Collections.emptyList();
            }

            // 解析JSON数组获取楼宇类型
            JSONArray jsonArray = JSON.parseArray(extractedStr);
            List<String> types = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i);
                types.add(item.getString("thirdBuildingType"));
            }

            return types;
        } catch (Exception e) {
            log.error("豆包 API调用失败", e);
        }

        log.warn("获取楼宇评估数据失败，返回空");
        return Collections.emptyList();
    }



}
