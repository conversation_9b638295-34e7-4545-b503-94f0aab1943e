package com.coocaa.meht.common.bean;


import com.alibaba.fastjson2.JSON;
import com.coocaa.meht.common.exception.ServerException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/11/1
 */
@Slf4j
public class RpcUtils {
    public static <T> T unBox(ResultTemplate<T> resultTemplate) {
        String code = resultTemplate.getCode();
        if (!code.equals("1") || !resultTemplate.getSuccess()) {
            log.warn("RPC调用失败：{}", JSON.toJSONString(resultTemplate));
            throw new ServerException(resultTemplate.getMsg());
        }
        return resultTemplate.getData();
    }
}
