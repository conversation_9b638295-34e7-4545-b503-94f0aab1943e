package com.coocaa.meht.module.web.service.property;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.property.PersonParam;
import com.coocaa.meht.module.web.dto.property.PropertyCompanyPersonParam;
import com.coocaa.meht.module.web.entity.PropertyCompanyPersonEntity;
import com.coocaa.meht.module.web.vo.property.PropertyCompanyPersonVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface IPropertyCompanyPersonService extends IService<PropertyCompanyPersonEntity> {

    /**
     * 新增物业公司下的联系人
     * @param param
     * @return
     */
    Integer savePerson(PropertyCompanyPersonParam param);
    List<PropertyCompanyPersonVO> savePerson(PersonParam param);
}
