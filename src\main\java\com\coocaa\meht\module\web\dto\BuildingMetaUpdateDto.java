package com.coocaa.meht.module.web.dto;

import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 楼宇基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Data
public class BuildingMetaUpdateDto {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @NotNull(message = "楼宇id不能为空")
    private Long id;

    /**
     * 楼宇编码BC打头
     */
    @NotNull(message = "楼宇编码不能为空")
    @Schema(description = "楼宇编码BC打头")
    private String buildingMetaNo;
    /**
     * 楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区
     */
    @NotNull(message = "楼宇类型不能为空")
    @Schema(description = "楼宇类型")
    private Integer buildingType;


    /**
     * 楼宇名称
     */
    @NotNull(message = "楼宇名称不能为空")
    @Schema(description = "楼宇名称")
    private String buildingName;

    /**
     * 目标点位数量
     */
    @Max(value = 9999,message = "目标点位数量仅限0到9999的整数")
    @Schema(description = "目标点位数量")
    private Integer targetPointCount;

    /**
     * 竞媒点位数量
     */
    @Max(value = 9999,message = "竞媒点位数量0到9999的整数")
    @Schema(description = "竞媒点位数量")
    private Integer competitorPointCount;

    /**
     * 最高楼层
     */
    @Schema(description = "最高楼层", type = "Int", example = "1")
    private Integer floorTotalNumber;

    /**
     * 总楼栋数量
     */
    @Schema(description = "总楼栋数量", type = "Int", example = "1")
    private Integer buildingTotalNumber;

    /**
     * 总单元数量
     */
    @Schema(description = "总单元数量", type = "Int", example = "1")
    private Integer unitsTotalNumber;

    /**
     * 总等候厅数
     */
    @Schema(description = "总等候厅数", type = "Int", example = "1")
    private Integer hallTotalNumber;

    /**
     * 禁忌行业
     */
    @Schema(description = "禁忌行业", type = "String", example = "1")
    @NotNull(message = "禁忌行业不能为空")
    private List<String> forbiddenIndustry;

    /**
     * 外墙材料附件地址
     */
    @Schema(description = "外墙材料附件地址")
    private List<String> buildingExteriorPic;
    /**
     * 大堂高度及装饰附件地址
     */
    @Schema(description = "大堂高度及装饰附件地址")
    private List<String> buildingLobbyPic;
    /**
     * 楼梯厅装饰附件地址
     */
    @Schema(description = "楼梯厅装饰附件地址")
    private List<String> buildingHallPic;

    /**
     * 梯厅环境图附件地址
     */
    @Schema(description = "梯厅环境图附件地址")
    private List<String> buildingElevatorPic;

    /**
     * 闸口图附件地址
     */
    @Schema(description = "闸口图附件地址")
    private List<String> buildingGatePic;

    /**
     * 安装示意图附件地址
     */
    @Schema(description = "安装示意图附件地址")
    private List<String> buildingInstallationPic;


    @Schema(description = "楼宇基因信息")
    private BuildingGeneEntity buildingGene;



}
