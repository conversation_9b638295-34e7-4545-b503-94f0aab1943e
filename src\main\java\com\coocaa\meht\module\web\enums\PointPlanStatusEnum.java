package com.coocaa.meht.module.web.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 等候厅状态枚举
 */

@AllArgsConstructor
public enum PointPlanStatusEnum {

    WAITING_SIGN("0037-1", "待签约"),
    SIGNING("0037-2", "签约中"),
    SIGNED("0037-3", "已签约"),
    REVIEW("0037-4", "已复核");

    /**
     * 状态码
     */
    @Getter
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static PointPlanStatusEnum getByCode(String code) {
        for (PointPlanStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 