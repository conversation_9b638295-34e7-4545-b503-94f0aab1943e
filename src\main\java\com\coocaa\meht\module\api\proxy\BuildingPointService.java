package com.coocaa.meht.module.api.proxy;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.exception.CommonException;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.module.web.dto.req.BrandPointQueryReq;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.coocaa.meht.module.web.service.BuildingGeneService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.PriceApplyService;
import com.coocaa.meht.module.web.vo.proxy.BrandDataVO;
import com.coocaa.meht.module.web.vo.proxy.BuildingBrandPointVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.dto.UserDataAccessV2DTO;
import com.coocaa.meht.utils.HttpUtils;
import com.coocaa.meht.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.security.MessageDigest;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/3
 * @description 楼宇点位
 */
@Slf4j
@Service
public class BuildingPointService {
    private static final String[] hexDigits = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};
    private final static int SCREEN_FLAG_SMALL = 1, SCREEN_FLAG_LARGE = 2, SCREEN_FLAG_BOTH = 3;
    private final static ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private final String csCshimediaApiUrl;

    @Resource
    private LargeScreenProperties largeScreenProperties;


    @Autowired
    private FeignCmsRpc feignCmsRpc;

    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;

    @Autowired
    private BuildingRatingService buildingRatingService;

    @Autowired
    private PriceApplyService priceApplyService;

    @Autowired
    private BuildingGeneService buildingGeneService;


    @Value("${proxy.cshimedia.api-key}")
    private String csCshimediaApiKey;

    public BuildingPointService(@Value("${proxy.cshimedia.url}") String csCshimediaUrl,
                                @Value("${proxy.cshimedia.api-prefix}") String csCshimediaApiPrefix) {
        this.csCshimediaApiUrl = String.format("%s/%s", csCshimediaUrl, csCshimediaApiPrefix);
    }

    public Map<String, Object> getBuildingPoint(BrandPointQueryReq param, boolean isAnonymous) {
        String part = "/data/pointAnalysis/getBuildingPoint";
        Map<String, String> header = Map.of("Content-Type", "application/json; charset=utf-8;");
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("调用大数据平台接口");
            if (StringUtils.isBlank(param.getToken())) {
                param.setToken(getMD5Str(csCshimediaApiKey).toLowerCase());
            }
            String url = csCshimediaApiUrl + part;
            String body = OBJECT_MAPPER.writeValueAsString(param);
            String response = HttpUtils.post(url, header, body);
            Map<String, Object> objectMap = JSON.parseObject(response, new TypeReference<Map<String, Object>>() {
            }.getType());
            stopWatch.stop();

            // 匿名登录时
            BrandDataVO brandDataVO = OBJECT_MAPPER.convertValue(objectMap.get("data"), BrandDataVO.class);
            if (isAnonymous) {
                List<BuildingBrandPointVO> list = brandDataVO.getSelfBrandPoints();
                // 填充大屏标记
                fillingLargeScreenFlag(list, stopWatch);
                // 返回数据
                brandDataVO.setSelfBrandPoints(list);
                objectMap.put("data", brandDataVO);
            } else {
                // 权限过滤
                stopWatch.start("获取数据权限");
                ResultTemplate<UserDataAccessV2DTO> userDataAccessV2 = feignCmsRpc.getUserDataAccessV2();
                stopWatch.stop();
                if (Objects.nonNull(userDataAccessV2.getData()) && CollectionUtil.isNotEmpty(userDataAccessV2.getData().getCityIds())) {
                    stopWatch.start("获取城市信息");
                    ResultTemplate<List<CodeNameVO>> listResultTemplate = feignAuthorityRpc.listCityByIds(userDataAccessV2.getData().getCityIds());
                    log.info("城市权限数据city:{}", JsonUtils.toJson(listResultTemplate));
                    List<String> cityNames = listResultTemplate.getData().stream().map(CodeNameVO::getName).toList();
                    stopWatch.stop();

                    stopWatch.start("根据权限过滤城市");
                    List<BuildingBrandPointVO> list = brandDataVO.getSelfBrandPoints().stream().filter(item -> cityNames.contains(item.getCity())).toList();
                    stopWatch.stop();

                    // 填充大屏标记
                    fillingLargeScreenFlag(list, stopWatch);
                    // 返回数据
                    brandDataVO.setSelfBrandPoints(list);
                    objectMap.put("data", brandDataVO);
                }
            }

            if (!(Boolean) objectMap.getOrDefault("success", true)) {
                log.error("getBuildingPoint：请求【{}】失败！请求参数：{}，错误详情：{}", url, body, objectMap.get("msg"));
                throw new CommonException(String.format("请求【%s】失败！详情：%s", url, objectMap.get("msg")));
            }
            log.info("获取楼盘信息, 耗时:{}", stopWatch.prettyPrint());
            return objectMap;
        } catch (JsonProcessingException e) {
            log.error("getBuildingPoint：json数据转换失败！详情：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private void fillingLargeScreenFlag(List<BuildingBrandPointVO> points, StopWatch stopWatch) {
        if (CollectionUtil.isEmpty(points)) {
            return;
        }

        // 初始化大屏标记
        points.forEach(point -> point.setLargeScreen(SCREEN_FLAG_SMALL));

        // 提取地图编码
        Set<String> mapNos = points.stream()
                .map(BuildingBrandPointVO::getMapNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(mapNos)) {
            return;
        }

        // 通过地图编码获取楼宇编码，到价格申请表中查询大屏标记， key:mapNo, val:buildingNo
        stopWatch.start("地图编码转楼宇编码");
        Map<String, List<String>> buildingNoMapping = buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getMapNo, BuildingRatingEntity::getBuildingNo)
                .in(BuildingRatingEntity::getMapNo, mapNos)
                .ne(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.DRAFT.getValue())
                .orderByDesc(BuildingRatingEntity::getCreateTime)
                .list().stream()
                .collect(Collectors.groupingBy(BuildingRatingEntity::getMapNo, Collectors.mapping(BuildingRatingEntity::getBuildingNo, Collectors.toList())));
        stopWatch.stop();
        if (CollectionUtil.isEmpty(buildingNoMapping)) {
            return;
        }

        // 拿到所有buildingNo
        Set<String> buildingNos = buildingNoMapping.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(buildingNos)) {
            return;
        }

        stopWatch.start("楼宇编码查价格申请大屏标记");
        Map<String, PriceApplyEntity> largeScreenMapping = priceApplyService.lambdaQuery()
                .select(PriceApplyEntity::getBuildingNo, PriceApplyEntity::getLargeScreenFlag)
                .in(PriceApplyEntity::getBuildingNo, buildingNos)
                .list().stream()
                .filter(priceApply -> Objects.nonNull(priceApply.getLargeScreenFlag()))
                .collect(Collectors.toMap(PriceApplyEntity::getBuildingNo, Function.identity(),
                        (o, n) -> n.getLargeScreenFlag() > o.getLargeScreenFlag() ? n : o));

        // 准备构建楼宇编码的大屏标记
        Map<String, Integer> buildingNoLargeScreenMapping = Maps.newHashMapWithExpectedSize(buildingNos.size());
        Set<String> notMatchedBuildingNos = Sets.newHashSet();
        for (String buildingNo : buildingNos) {
            PriceApplyEntity priceApply = largeScreenMapping.get(buildingNo);
            if (Objects.isNull(priceApply)) {
                notMatchedBuildingNos.add(buildingNo);
            } else {
                buildingNoLargeScreenMapping.put(buildingNo, priceApply.getLargeScreenFlag());
            }
        }
        stopWatch.stop();

        // 价格申请上没找到的楼盘，需要到基因表中查
        stopWatch.start("基因表上查大屏标记");
        Set<String> largeScreenDictCodes = getLargeScreenDictCodes();
        if (CollectionUtil.isNotEmpty(notMatchedBuildingNos) && CollectionUtil.isNotEmpty(largeScreenDictCodes)) {
            buildingNoLargeScreenMapping.putAll(buildingGeneService.lambdaQuery()
                    .select(BuildingGeneEntity::getBuildingRatingNo, BuildingGeneEntity::getSpec)
                    .in(BuildingGeneEntity::getBuildingRatingNo, notMatchedBuildingNos)
                    .list().stream()
                    .filter(item -> StringUtils.isNotBlank(item.getSpec()))
                    .collect(Collectors.toMap(BuildingGeneEntity::getBuildingRatingNo, item -> getLargeScreenFlag(item.getSpec(), largeScreenDictCodes))));

        }
        stopWatch.stop();


        // 填充数据
        stopWatch.start("填充大屏标记");
        for (BuildingBrandPointVO point : points) {
            int largeScreenFlag = SCREEN_FLAG_SMALL;
            List<String> buildingNoList = buildingNoMapping.getOrDefault(point.getMapNo(), Collections.emptyList());
            for (String buildingNo : buildingNoList) {
                largeScreenFlag |= buildingNoLargeScreenMapping.getOrDefault(buildingNo, SCREEN_FLAG_SMALL);
            }
            point.setLargeScreen(largeScreenFlag);
            if (CollUtil.isNotEmpty(buildingNoList)) {
                point.setBuildingNo(buildingNoList.get(0));
            }
        }
        stopWatch.stop();
    }

    /**
     * 通过基因表规格，提取大屏标记
     */
    private Integer getLargeScreenFlag(String spec, Set<String> largeScreenDictCodes) {
        int largeScreenFlag = SCREEN_FLAG_SMALL;
        List<String> dictCodes = JSON.parseArray(spec, String.class);
        if (CollectionUtil.isEmpty(dictCodes)) {
            return largeScreenFlag;
        }

        for (String code : dictCodes) {
            if (largeScreenDictCodes.contains(code)) {
                largeScreenFlag = SCREEN_FLAG_LARGE;
                break;
            }
        }
        return largeScreenFlag;
    }

    /**
     * 获取配置的大屏字典编码
     */
    private Set<String> getLargeScreenDictCodes() {
        List<String> largeScreenDictCodes = largeScreenProperties.getLargeDictKey();
        if (CollectionUtil.isEmpty(largeScreenDictCodes)) {
            return Collections.emptySet();
        }
        return largeScreenDictCodes.stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    }


    public static String getMD5Str(String str) {
        String resStr = "";
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            resStr = byteArrayToHexString(md.digest(str.getBytes()));
        } catch (Exception ex) {
            log.error("ERROR_CODE_00108 md5 fail");
            ex.printStackTrace();
        }
        return resStr;
    }

    private static String byteArrayToHexString(byte[] b) {
        StringBuffer resSb = new StringBuffer();
        for (int i = 0; i < b.length; i++) {
            resSb.append(byteToHexString(b[i]));
        }
        return resSb.toString();
    }

    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0) {
            n = 256 + n;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }
}
