package com.coocaa.meht.utils;

import groovy.util.logging.Slf4j;
import org.ansj.domain.Term;
import org.ansj.splitWord.analysis.ToAnalysis;
import java.util.*;

@Slf4j
public class WordUtils {
    
    /**
     * 计算两个句子的余弦相似度
     * @param sentence1 第一个句子
     * @param sentence2 第二个句子
     * @return 余弦相似度值，范围在[0,1]之间
     */
    public static double calculateCosineSimilarity(String sentence1, String sentence2) {
        if (sentence1 == null || sentence2 == null) {
            return 0.0;
        }
        
        // 使用ansj_seg进行分词
        List<Term> terms1 = ToAnalysis.parse(sentence1).getTerms();
        List<Term> terms2 = ToAnalysis.parse(sentence2).getTerms();
        
        // 获取词向量
        Map<String, Integer> vector1 = getWordVector(terms1);
        Map<String, Integer> vector2 = getWordVector(terms2);
        
        // 计算余弦相似度
        return calculateCosine(vector1, vector2);
    }

    /**
     * 将分词结果转换为词向量
     */
    private static Map<String, Integer> getWordVector(List<Term> terms) {
        Map<String, Integer> vector = new HashMap<>();
        for (Term term : terms) {
            String word = term.getName();
            vector.put(word, vector.getOrDefault(word, 0) + 1);
        }
        return vector;
    }
    
    /**
     * 计算两个向量的余弦相似度
     */
    private static double calculateCosine(Map<String, Integer> vector1, Map<String, Integer> vector2) {
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;
        
        // 计算点积和向量范数
        for (String word : vector1.keySet()) {
            if (vector2.containsKey(word)) {
                dotProduct += vector1.get(word) * vector2.get(word);
            }
            norm1 += Math.pow(vector1.get(word), 2);
        }
        
        for (String word : vector2.keySet()) {
            norm2 += Math.pow(vector2.get(word), 2);
        }
        
        // 计算余弦相似度
        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }
        
        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }
}
