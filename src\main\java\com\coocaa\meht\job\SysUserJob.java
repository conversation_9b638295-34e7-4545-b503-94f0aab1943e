package com.coocaa.meht.job;

import cn.hutool.core.collection.CollectionUtil;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.module.sys.entity.SysUserEntity;
import com.coocaa.meht.module.sys.service.SysConfigService;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dto.approval.UserFeishuVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.utils.Converts;
import com.coocaa.meht.utils.JsonUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
@Slf4j
@Component
public class SysUserJob {
    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private FeignAuthorityRpc feignAuthorityRpc;

    /**
     * 定时任务：同步用户信息
     */
    @XxlJob("syncUser")
    public void syncUser() {
        sysUserService.syncUser();
    }

    /**
     * 刷新参数配置
     */
    @XxlJob("SysConfigInit")
    public void run2() {
        long start = System.currentTimeMillis();
        sysConfigService.init();
        long time = System.currentTimeMillis() - start;
        XxlJobHelper.handleSuccess("耗时: " + (time) + " ms");
    }

    /**
     * 设置参数
     */
    @XxlJob("SysConfigSetValue")
    public void run4() {
        long start = System.currentTimeMillis();
        String param = Converts.toStr(XxlJobHelper.getJobParam());
        if (StringUtils.isBlank(param)) {
            XxlJobHelper.log("param 为空");
        } else {
            Map<String, Object> map = JsonUtils.fromMap(param);
            if (map != null && !map.isEmpty()) {
                map.forEach((k, v) -> sysConfigService.setValue(k, Converts.toStr(v)));
            }
        }
        long time = System.currentTimeMillis() - start;
        XxlJobHelper.handleSuccess("耗时: " + (time) + " ms");
    }


    @XxlJob("sysFeiShuInformation")
    public void sysFeiShuInformation() {
        List<SysUserEntity> userEntities = sysUserService.lambdaQuery()
                .list();

        if (CollectionUtil.isNotEmpty(userEntities)){
            List<String> list = userEntities.stream().map(SysUserEntity::getEmpCode).toList();
            ResultTemplate<List<UserFeishuVO>> userFeiShuByWno = feignAuthorityRpc.getUserFeiShuByWno(list);
            if (CollectionUtil.isNotEmpty(userFeiShuByWno.getData())){
                Set<String> userCodes = userFeiShuByWno.getData().stream().map(UserFeishuVO::getWno).collect(Collectors.toSet());
                Map<String, UserFeishuVO> userFeishuVOMap = userFeiShuByWno.getData().stream().collect(Collectors
                        .toMap(userFeishuVO -> userFeishuVO.getWno(), userFeishuVO -> userFeishuVO, (v1, v2) -> v1));
                log.info("飞书用户信息：{}", JsonUtils.toJson(userFeiShuByWno.getData()));
                for (SysUserEntity userEntity : userEntities) {
                    UserFeishuVO userFeishuVO = userFeishuVOMap.get(userEntity.getEmpCode());
                    if (Objects.nonNull(userFeishuVO)){
                        userEntity.setFsOpenId(userFeishuVO.getOpenId());
                        userEntity.setFsUnionId(userFeishuVO.getUnionId());
                        userEntity.setFsUserId(userFeishuVO.getFeishuUserId());
                    }

                }

                List<SysUserEntity> userEntityList = userEntities.stream().filter(userEntity -> userCodes.contains(userEntity.getEmpCode())).toList();
                log.info("飞书用户信息更新条数：{}", userEntityList.size());
                sysUserService.updateBatchById(userEntityList);

            }

        }

    }
}
