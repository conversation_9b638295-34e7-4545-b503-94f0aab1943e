package com.coocaa.meht.rpc.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-10-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SspPointWatingHallVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer pointId;

    /**
     * 等候厅id
     */
    private Integer waitingHallId;

    /**
     * 点位编码
     */
    private String pointCode;

    /**
     * 点位状态
     */
    private String pointStatus;

    /**
     * 城市id
     */
    private Integer city;

    private Integer number;
    private String buildingName;
    private String unitName;
    private String floor;
    private String waitingHallName;
    private String waitingHallType;


}
