package com.coocaa.meht.module.web.vo.kanban;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 城市维度点位状态统计
 */
@Data
public class ContractPointStatisticsVO {
    /**
     * 城市ID
     */
    @Schema(description = "城市ID", type = "number", example = "21")
    private Integer cityId;
    /**
     * 城市名称
     */
    @Schema(description = "城市名称", type = "String", example = "成都市")
    private String cityName;
    /**
     * 已签合同数量
     */
    @Schema(description = "已签合同数量", type = "number", example = "32")
    private Integer signedCount;
    /**
     * 已安装数量
     */
    @Schema(description = "已安装数量", type = "number", example = "54")
    private Integer installedCount;
    /**
     * 已计费数量
     */
    @Schema(description = "已计费数量", type = "number", example = "23")
    private Integer chargedCount;
    /**
     * 可售数量
     */
    @Schema(description = "可售数量", type = "number", example = "23")
    private Integer availableCount;
    /**
     * 故障数量
     */
    @Schema(description = "故障数量", type = "number", example = "34")
    private Integer faultCount;
    /**
     * 计费可售比
     */
    @Schema(description = "计费可售比", type = "number", example = "82.34")
    private BigDecimal chargedAvailableRatio;

    /**
     * 统计日期
     */
    @Schema(description = "主标题", type = "String", example = "12/31")
    private String statisticsDate;

}
