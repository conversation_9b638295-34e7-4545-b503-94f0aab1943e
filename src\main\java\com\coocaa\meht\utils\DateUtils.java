package com.coocaa.meht.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Month;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 日期处理
 */
public class DateUtils {
    private static final Logger log = LoggerFactory.getLogger(DateUtils.class);

    public final static String DATE_PATTERN = "yyyy-MM-dd";
    public static final String TIME_PATTERN = "HH:mm:ss";
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final DateTimeFormatter DTF_DT = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);
    public static final DateTimeFormatter DTF_TIME = DateTimeFormatter.ofPattern(TIME_PATTERN);
    public static final DateTimeFormatter DTF_DATE = DateTimeFormatter.ofPattern(DATE_PATTERN);

    public static final ZoneOffset zoneOffset8 = ZoneOffset.ofHours(8);
    public static final LocalTime _max = LocalTime.of(23, 59, 59);
    /* 上班时间10:00 - 19:30 */
    public static LocalTime START_WORK = LocalTime.of(10, 0);
    public static LocalTime END_WORK = LocalTime.of(19, 30);

    /**
     * 格式化
     *
     * @param date
     * @param dtf
     * @return
     */
    public static String format(TemporalAccessor date, DateTimeFormatter dtf) {
        return date == null ? "" : dtf.format(date);
    }

    /**
     * 格式化
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String format(TemporalAccessor date, String pattern) {
        return date == null ? "" : DateTimeFormatter.ofPattern(pattern).format(date);
    }

    /**
     * 字符串 转 LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime toDateTime(String date) {
        if (StringUtils.isNotBlank(date)) {
            try {
                return LocalDateTime.parse(date);
            } catch (Exception e) {
                log.error("时间反序列化失败:", e);
            }
        }
        return null;
    }

    /**
     * timestamp 转 LocalDateTime
     *
     * @param timestamp
     * @return
     */
    public static LocalDateTime toDateTime(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date 日期
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        SimpleDateFormat df = new SimpleDateFormat(pattern);
        return df.format(date);
    }

    /**
     * 日期解析
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回Date
     */
    public static Date parse(String date, String pattern) {
        try {
            return new SimpleDateFormat(pattern).parse(date);
        } catch (ParseException ignored) {
        }
        return null;
    }

    /**
     * 今天开始时间
     *
     * @return
     */
    public static LocalDateTime todayStart() {
        return LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
    }

    /**
     * 今天结束时间
     *
     * @return
     */
    public static LocalDateTime todayEnd() {
        return LocalDateTime.of(LocalDate.now(), _max);
    }

    /**
     * 月份开始时间
     *
     * @return
     */
    public static LocalDateTime monthStart(LocalDate date) {
        return LocalDateTime.of(date.with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
    }

    /**
     * 月份结束时间
     *
     * @return
     */
    public static LocalDateTime monthEnd(LocalDate date) {
        return LocalDateTime.of(date.with(TemporalAdjusters.lastDayOfMonth()), _max);
    }

    /**
     * 本季度开始时间
     *
     * @return
     */
    public static LocalDateTime quarterStart() {
        LocalDate now = LocalDate.now();
        Month month = Month.of(now.getMonth().firstMonthOfQuarter().getValue());
        return LocalDateTime.of(LocalDate.of(now.getYear(), month, 1), LocalTime.MIN);
    }

    /**
     * 本季度结束时间
     *
     * @return
     */
    public static LocalDateTime quarterEnd() {
        LocalDate now = LocalDate.now();
        Month month = Month.of(now.getMonth().firstMonthOfQuarter().getValue()).plus(2L);
        return LocalDateTime.of(LocalDate.of(now.getYear(), month, month.length(now.isLeapYear())), _max);
    }

    /**
     * 季度开始时间
     *
     * @param date
     * @return
     */
    public static LocalDateTime quarterStart(LocalDate date) {
        Month month = Month.of(date.getMonth().firstMonthOfQuarter().getValue());
        return LocalDateTime.of(LocalDate.of(date.getYear(), month, 1), LocalTime.MIN);
    }

    /**
     * 季度结束时间
     *
     * @param date
     * @return
     */
    public static LocalDateTime quarterEnd(LocalDate date) {
        Month month = Month.of(date.getMonth().firstMonthOfQuarter().getValue()).plus(2L);
        return LocalDateTime.of(LocalDate.of(date.getYear(), month, month.length(date.isLeapYear())), _max);
    }

    /**
     * 年开始时间
     *
     * @return
     */
    public static LocalDateTime yearStart(LocalDate date) {
        return LocalDateTime.of(date.with(TemporalAdjusters.firstDayOfYear()), LocalTime.MIN);
    }

    /**
     * 年结束时间
     *
     * @return
     */
    public static LocalDateTime yearEnd(LocalDate date) {
        return LocalDateTime.of(date.with(TemporalAdjusters.lastDayOfYear()), _max);
    }

    /**
     * 获取对应季度的日期
     *
     * @param quarterYear 年份
     * @param quarter     季度：Q1, Q2, Q3, Q4
     * @return
     */
    public static LocalDateTime[] getQuarterDate(int quarterYear, String quarter) {
        int quarterMonth;
        switch (quarter) {
            case "Q1":
                quarterMonth = 1;
                break;
            case "Q2":
                quarterMonth = 4;
                break;
            case "Q3":
                quarterMonth = 7;
                break;
            case "Q4":
                quarterMonth = 10;
                break;
            default:
                throw new IllegalArgumentException("季度格式错误: " + quarter);
        }
        LocalDate date = LocalDate.of(quarterYear, quarterMonth, 1);
        return new LocalDateTime[]{quarterStart(date), quarterEnd(date)};
    }

    /**
     * 计算工作日内的有效时间差（秒）
     *
     * @param start
     * @param end
     * @return
     */
    public static long workingMin(LocalDateTime start, LocalDateTime end) {
        if (!start.isBefore(end)) {
            return 0;
        }
        if (start.toLocalDate().isEqual(end.toLocalDate())) {
            if (isHoliday(start.toLocalDate())
                    || !end.toLocalTime().isAfter(START_WORK) || !start.toLocalTime().isBefore(END_WORK)) {
                return 0;
            }
            return (start.toLocalTime().isBefore(START_WORK) ? START_WORK
                    : start.toLocalTime()).until(end.toLocalTime().isAfter(END_WORK) ? END_WORK
                    : end.toLocalTime(), ChronoUnit.SECONDS);
        }
        long duration = 0;
        if (isWorkday(start.toLocalDate())) {
            duration += start.toLocalTime().isBefore(START_WORK)
                    ? START_WORK.until(END_WORK, ChronoUnit.SECONDS) : start.toLocalTime().isBefore(END_WORK)
                    ? start.toLocalTime().until(END_WORK, ChronoUnit.SECONDS)
                    : 0;
        }
        LocalDate nextDay = start.toLocalDate();
        while ((nextDay = nextWorkday(nextDay)).isBefore(end.toLocalDate())) {
            duration += START_WORK.until(END_WORK, ChronoUnit.SECONDS);
        }
        if (isWorkday(end.toLocalDate())) {
            duration += end.toLocalTime().isAfter(END_WORK)
                    ? START_WORK.until(END_WORK, ChronoUnit.SECONDS) : end.toLocalTime().isAfter(START_WORK)
                    ? START_WORK.until(end.toLocalTime(), ChronoUnit.SECONDS)
                    : 0;
        }
        return duration;
    }

    /**
     * 基于当前时间，获取时间进度
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static double timeSpeed(LocalDateTime nowTime, LocalDateTime startTime, LocalDateTime endTime) {
        Duration duration1 = Duration.between(startTime, nowTime);
        if (duration1.getSeconds() <= 0) return 0;
        Duration duration2 = Duration.between(startTime, endTime);
        return ((double) duration1.getSeconds() / duration2.getSeconds()) * 100;
    }

    /**
     * 是否工作日（包括法定补班日）
     *
     * @param date
     * @return
     */
    public static boolean isWorkday(LocalDate date) {
        return !isHoliday(date);
    }

    /**
     * 是否节假日（包括法定假日、周六、周日）
     *
     * @param date
     * @return
     */
    public static boolean isHoliday(LocalDate date) {
        long second = date.atStartOfDay(zoneOffset8).toEpochSecond();
        if (Workday.CMPENSATORY.contains(second)) {
            return false;
        }
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        return Workday.LEGAL_HOLIDAYS.contains(second) || dayOfWeek == DayOfWeek.SATURDAY
                || dayOfWeek == DayOfWeek.SUNDAY;
    }

    /**
     * 上一个工作日
     *
     * @param date
     * @return
     */
    public static LocalDate prevWorkday(LocalDate date) {
        LocalDate t = date;
        do {
            t = t.plusDays(-1);
        } while (isHoliday(t));
        return t;
    }

    /**
     * 下一个工作日
     *
     * @param date
     * @return
     */
    public static LocalDate nextWorkday(LocalDate date) {
        LocalDate t = date;
        do {
            t = t.plusDays(1);
        } while (isHoliday(t));
        return t;
    }

    private static final class Workday {
        static final Set<Long> CMPENSATORY = new HashSet<>();
        static final Set<Long> LEGAL_HOLIDAYS = new HashSet<>();

        static {
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            for (String date : holiday_2023()) {
                LEGAL_HOLIDAYS.add(LocalDate.parse(date, dtf).atStartOfDay(zoneOffset8).toEpochSecond());
            }
            for (String date : cmpensatory_2023()) {
                CMPENSATORY.add(LocalDate.parse(date, dtf).atStartOfDay(zoneOffset8).toEpochSecond());
            }
            for (String date : holiday_2024()) {
                LEGAL_HOLIDAYS.add(LocalDate.parse(date, dtf).atStartOfDay(zoneOffset8).toEpochSecond());
            }
            for (String date : cmpensatory_2024()) {
                CMPENSATORY.add(LocalDate.parse(date, dtf).atStartOfDay(zoneOffset8).toEpochSecond());
            }
        }

        private Workday() {
        }

        private static String[] holiday_2023() {
            return new String[]{
                    "2023-01-01", "2023-01-02",//元旦
                    "2023-01-21", "2023-01-22", "2023-01-23", "2023-01-24", "2023-01-25", "2023-01-26", "2023-01-27",//春节
                    "2023-04-05",//清明
                    "2023-04-29", "2023-04-30", "2023-05-01", "2023-05-02", "2023-05-03",//五一
                    "2023-06-22", "2023-06-23", "2023-06-24",//端午
                    "2023-09-29", "2023-09-30", "2023-10-01", "2023-10-02", "2023-10-03", "2023-10-04", "2023-10-05", "2023-10-06"};//中秋-国庆
        }

        private static String[] cmpensatory_2023() {
            return new String[]{
                    "2023-01-28", "2023-01-29",
                    "2023-04-23", "2023-05-06",
                    "2023-06-25",
                    "2023-10-07", "2023-10-08"};
        }

        private static String[] holiday_2024() {
            return new String[]{
                    "2023-12-30", "2023-12-31", "2024-01-01",//元旦
                    "2024-02-10", "2024-02-11", "2024-02-12", "2024-02-13", "2024-02-14", "2024-02-15", "2024-02-16", "2024-02-17",//春节
                    "2024-04-04", "2024-04-05", "2024-04-06",//清明
                    "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-04", "2024-05-05",//五一
                    "2024-06-08", "2024-06-09", "2024-06-10",//端午
                    "2024-09-15", "2024-09-16", "2024-09-17",//中秋
                    "2024-10-01", "2024-10-02", "2024-10-03", "2024-10-04", "2024-10-05", "2024-10-06", "2024-10-07"};//国庆
        }

        private static String[] cmpensatory_2024() {
            return new String[]{
                    "2024-02-04", "2024-02-18",
                    "2024-04-07", "2024-04-28",
                    "2024-05-11",
                    "2024-09-14", "2024-09-29",
                    "2024-10-12"};
        }
    }

}