package com.coocaa.meht.module.web.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 商机
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("business_opportunity")
public class BusinessOpportunityEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 商机名称
     */
    private String name;
    /**
     * 商机编号
     */
    private String code;
    /**
     * 商机状态
     */
    private String status;
    /**
     * 客户ID
     */
    private String customerId;

    /**
     * building_rating表的building_no
     */
    private String buildingNo;

    /**
     * crm商机id
     */
    private String crmBusinessId;
    /**
     * 提交人
     */
    private String submitUser;
    /**
     * 提交时间
     */
    private String owner;

}
