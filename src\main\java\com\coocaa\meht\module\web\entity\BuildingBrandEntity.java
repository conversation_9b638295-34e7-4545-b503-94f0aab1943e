package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 楼宇品牌
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("building_brand")
public class BuildingBrandEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String type;

    /**
     * 品牌指数
     */
    @TableField(value = "`index`")
    private BigDecimal index;

    /**
     * 排名(自然顺序，越小越靠前)
     */
    private Integer rank;

    /**
     * 状态 [0:禁用, 1:启用]
     */
    private Integer status;
}