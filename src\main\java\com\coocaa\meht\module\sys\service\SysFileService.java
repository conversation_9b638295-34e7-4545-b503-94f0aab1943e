package com.coocaa.meht.module.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 附件管理
 *
 * <AUTHOR>
 */
public interface SysFileService extends IService<SysFileEntity> {

    SysFileEntity getByUrl(String url);

    SysFileEntity uploadByte(String originalFilename,  byte[] byt);

    SysFileEntity upload(MultipartFile file);

    List<SysFileEntity> getByUrls(Collection<String> urls);

    void delete(Collection<String> urls);

    List<SysFileEntity> getBySysFileIdList(List<Long> ids);
}