package com.coocaa.meht.job;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.module.sys.service.SysConfigService;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.ScreenApproveRecordEntity;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.enums.SceneTypeEnum;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.service.IBuildingStatusChangeLogService;
import com.coocaa.meht.module.web.service.ScreenApproveRecordService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BusinessJob {


    @Resource
    private BuildingRatingService buildingRatingService;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private IBuildingMetaService buildingMetaService;

    @Autowired
    private IBuildingStatusChangeLogService changeLogService;

    @Autowired
    private BusinessOpportunityService businessOpportunityService;

    @Autowired
    private ScreenApproveRecordService screenApproveRecordService;


    @XxlJob("resurrectedTimeout")
    public void resurrectedTimeout() {
        List<BuildingRatingEntity> list = buildingRatingService.list(Wrappers.<BuildingRatingEntity>lambdaQuery()
                .eq(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.REJECTED.value)
                .eq(BuildingRatingEntity::getBuildingStatus, BuildingRatingEntity.BuildingStatus.CONFIRMING.value)
                .isNotNull(BuildingRatingEntity::getRejectTime)
        );
        if (CollectionUtils.isNotEmpty(list)) {
            List<BuildingStatusChangeLogEntity> changes = new ArrayList<>();
            String outTime = sysConfigService.getVal("out_time", "3");

            List<String> BuildingNos = list.stream().map(BuildingRatingEntity::getBuildingNo).toList();
            Map<String, List<ScreenApproveRecordEntity>> screenMap = screenApproveRecordService.lambdaQuery()
                    .eq(ScreenApproveRecordEntity::getSceneType, SceneTypeEnum.BUILDING.getType())
                    .in(ScreenApproveRecordEntity::getNaturalKey, BuildingNos)
                    .orderByDesc(ScreenApproveRecordEntity::getApproveTime)
                    .list().stream().collect(Collectors.groupingBy(ScreenApproveRecordEntity::getNaturalKey));

            for (BuildingRatingEntity entity : list) {
                LocalDateTime rejectTime = entity.getRejectTime();
                LocalDateTime dateTime = rejectTime.plusDays(Integer.valueOf(outTime));

                List<ScreenApproveRecordEntity> screenApproveRecordEntities = screenMap.get(entity.getBuildingNo());
                Integer operateType = 1;
                if (CollectionUtils.isNotEmpty(screenApproveRecordEntities)) {
                    ScreenApproveRecordEntity screenApproveRecordEntity = screenApproveRecordEntities.get(0);
                    operateType = screenApproveRecordEntity.getOperateType();
                }

                try {
                    if (LocalDateTime.now().isAfter(dateTime) && operateType.equals(1)) {
                        /*buildingRatingService.updateById(new BuildingRatingEntity().setId(entity.getId())
                                .setBuildingStatus(BuildingRatingEntity.BuildingStatus.UN_CONFIRM.value)
                                .setStatus(BuildingRatingEntity.Status.ABANDONED.value));*/

                        LambdaUpdateWrapper<BuildingRatingEntity> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(BuildingRatingEntity::getId, entity.getId());
                        updateWrapper.set(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.ABANDONED.value);
                        updateWrapper.set(BuildingRatingEntity::getBuildingStatus, BuildingRatingEntity.BuildingStatus.UN_CONFIRM.value);
                        if (BuildingRatingEntity.HighSeaFlagEnum.NO.getCode().equals(entity.getHighSeaFlag())) {
                            // 非公海客户放弃需要清空入公海时间
                            updateWrapper.set(BuildingRatingEntity::getEnterSeaTime, null);
                        }
                        buildingRatingService.update(updateWrapper);

                        //修改buildingMeta表的状态
                        buildingMetaService.updateBuildMetaUnConfirmStatus(entity.getMapNo());

                        //商机处理
                        businessOpportunityService.lambdaUpdate()
                                .set(BusinessOpportunityEntity::getStatus, BusinessChangeStatusEnum.CLOSE.getCode())
                                .eq(BusinessOpportunityEntity::getBuildingNo, entity.getBuildingNo())
                                .update();

                        businessOpportunityService.updateFollowRecord(entity.getBuildingNo());

                        changes.add(addBuildingChangeLog(entity.getId(), entity.getBuildingNo()));
                    } else if (LocalDateTime.now().isAfter(dateTime) && operateType.equals(2)) {
                        buildingRatingService.rollBack(entity.getBuildingNo());
                    }
                } catch (Exception e) {
                    log.error("驳回数据异常，buildingNo {}，错误信息{}", entity.getBuildingNo(), e.getMessage());
                }
            }
            changeLogService.saveBatch(changes);
        }
    }

    public BuildingStatusChangeLogEntity addBuildingChangeLog(Long buildingId, String buildingNo) {
        BuildingStatusChangeLogEntity changeLogEntity = new BuildingStatusChangeLogEntity();
        changeLogEntity.setBizId(buildingId);
        changeLogEntity.setBizCode(buildingNo);
        changeLogEntity.setType(BuildingStatusChangeLogEntity.BizType.RATING.getCode());
        changeLogEntity.setChangeTime(LocalDateTime.now());
        changeLogEntity.setOperator(0L);
        changeLogEntity.setStatus(BuildingStatusChangeLogEntity.RatingApplicationStatus.ABANDONED.getCode());
        return changeLogEntity;
    }

    @XxlJob("cronJobBuildingPushCrm")
    public void cronJobBuildingPushCrm() {
        String param = XxlJobHelper.getJobParam();
        buildingRatingService.cronBuildingPushCrm(param);
    }

}
