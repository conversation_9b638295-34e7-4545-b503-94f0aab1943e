package com.coocaa.meht.module.approve.enums;

import com.coocaa.meht.module.web.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 站内审批任务状态
 * @since 2025-06-16
 */
@Getter
@AllArgsConstructor
public enum ApproveStatusEnum implements IEnumType<String> {

    /**
     * 未开始
     */
    NOT_STARTED("0139-1", "未开始"),

    /**
     * 待完成
     */
    PENDING("0139-2", "待完成"),

    /**
     * 已取消
     */
    CANCELED("0139-3", "已取消"),

    /**
     * 已完成
     */
    COMPLETED("0139-4", "已完成");

    private final String code;
    private final String desc;

    /**
     * 是否已完成
     *
     * @param status
     * @return
     */
    public static boolean isFinished(String status) {
        return COMPLETED.getCode().equals(status);
    }
}