package com.coocaa.meht.module.web.dto.point;

import cn.hutool.core.date.DatePattern;
import com.coocaa.meht.converter.Convert;
import com.coocaa.meht.converter.ConvertType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
public class PointDetail {
    private String buildingRatingNo;
    private String buildingName;
    private String unitName;
    @Convert(type = ConvertType.DICT)
    private String floor;
    private String floorName;
    private String waitingHallName;
    @Convert(type = ConvertType.DICT)
    private String waitingHallType;
    private String waitingHallTypeName;
    private Integer waitingHallId;
    private String pointCode;

    private String businessCode;

    private String pointRemark;
    @Convert(type = ConvertType.DICT, targetFieldName = "deviceSizeName")
    private String deviceSize;
    private String deviceSizeName;
    private Integer pointId;
    private List<String> pointPics = new ArrayList<>();
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime pointCreateTime;
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime expireTime;

    /**
     * 安装位置
     */
    public String getInstallLocation() {
        return String.format("%s_%s_%s", buildingName, unitName, floorName);
    }

    /**
     * 安装区域
     */
    public String getInstallArea() {
        return String.format("%s_%s", waitingHallTypeName, waitingHallName);
    }

    /**
     * 生成分组的Key
     */
    public String getGroupKey() {
        return String.format("%s|%s|%s|%s|%s|%s", deviceSize, buildingName, unitName, floor, waitingHallType, waitingHallName);
    }

    @Data
    public static class Device {
        /**
         * 类型
         */
        private String type = "创维液晶电视";

        /**
         * 设备尺寸
         */
        private String model;

        /**
         * 安装区域
         * 等候厅
         */
        private String area;

        /**
         * 安装位置
         * 楼栋单元楼层
         */
        private String location;

        /**
         * 安装数量
         */
        private Integer count;
    }

}
