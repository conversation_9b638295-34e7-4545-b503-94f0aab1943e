package com.coocaa.meht.kafka.handler;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.kafka.TopicCallback;
import com.coocaa.meht.kafka.TopicHandler;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.HighSeaCustomerService;
import com.coocaa.meht.module.web.service.IBuildingStatusChangeLogService;
import com.coocaa.meht.module.web.vo.BusinessStatusChangeVO;
import com.coocaa.meht.utils.DateUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/8
 * @description 商机变化处理器
 */
@Slf4j
@Component
@TopicHandler("business-status-change")
public class BusinessStatusChangeHandler implements TopicCallback {

    @Autowired
    private BusinessOpportunityService businessService;

    @Resource
    private HighSeaCustomerService highSeaCustomerService;

    @Autowired
    private IBuildingStatusChangeLogService changeLogService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void process(String message) {
        log.info("商机状态变化处理器处理消息，message:{}", message);

        if (StrUtil.isBlank(message)) {
            log.warn("商机状态变化处理器处理消息，消息内容为空");
            return;
        }

        try {
            BusinessStatusChangeVO businessStatusChangeVO = JSON.parseObject(message, BusinessStatusChangeVO.class);
            BusinessOpportunityEntity businessOpportunity = businessService.getOne(Wrappers.lambdaQuery(BusinessOpportunityEntity.class)
                    .eq(BusinessOpportunityEntity::getCode, businessStatusChangeVO.getBusinessCode()));

            if (Objects.isNull(businessOpportunity)) {
                log.warn("商机状态变化处理器处理消息，未找到商机，BusinessCode:{}", businessStatusChangeVO.getBusinessCode());
                return;
            }

            businessService.lambdaUpdate()
                    .set(BusinessOpportunityEntity::getStatus, businessStatusChangeVO.getStatus())
                    .eq(BusinessOpportunityEntity::getCode, businessStatusChangeVO.getBusinessCode())
                    .update();

            // 添加商机状态变更记录
            addBusinessChangeLog(businessOpportunity.getId(), businessOpportunity.getCode(), businessStatusChangeVO.getStatus(), businessStatusChangeVO);

            // 刷新客户自动入公海时间
            highSeaCustomerService.refreshEnterSeaTime(List.of(businessOpportunity.getBuildingNo()));
        } catch (Exception e) {
            log.error("更新商机状态异常", e);
            throw new ServerException("更新商机状态异常", e);
        }
    }

    private void addBusinessChangeLog(Integer businessId, String businessCode, String status, BusinessStatusChangeVO businessStatusChangeVO) {
        BuildingStatusChangeLogEntity changeLogEntity = new BuildingStatusChangeLogEntity();
        changeLogEntity.setBizId(businessId.longValue());
        changeLogEntity.setBizCode(businessCode);
        changeLogEntity.setType(BuildingStatusChangeLogEntity.BizType.BUSINESS.getCode());
        changeLogEntity.setStatus(status);
        if (StrUtil.isNotBlank(businessStatusChangeVO.getChangeTime())) {
            changeLogEntity.setChangeTime(LocalDateTime.parse(businessStatusChangeVO.getChangeTime(), DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN)));
        } else {
            changeLogEntity.setChangeTime(LocalDateTime.now());
        }
        changeLogEntity.setOperatorWno(businessStatusChangeVO.getOperatorWno());
        changeLogEntity.setOperatorName(businessStatusChangeVO.getOperatorName());
        if (Objects.nonNull(businessStatusChangeVO.getOperatorId())) {
            changeLogEntity.setOperator(businessStatusChangeVO.getOperatorId().longValue());
        }

        // 逻辑删除老数据后新增
        changeLogService.update(new LambdaUpdateWrapper<BuildingStatusChangeLogEntity>()
                .eq(BuildingStatusChangeLogEntity::getBizId, changeLogEntity.getBizId())
                .eq(BuildingStatusChangeLogEntity::getType, changeLogEntity.getType())
                .eq(BuildingStatusChangeLogEntity::getStatus, changeLogEntity.getStatus())
                .eq(BuildingStatusChangeLogEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .set(BuildingStatusChangeLogEntity::getDeleteFlag, BooleFlagEnum.YES.getCode()));
        changeLogService.save(changeLogEntity);
    }

}
