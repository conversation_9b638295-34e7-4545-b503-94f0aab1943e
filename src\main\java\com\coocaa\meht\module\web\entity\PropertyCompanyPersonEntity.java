package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.common.handler.EncryptHandler;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@TableName(value = "property_company_person", autoResultMap = true)
public class PropertyCompanyPersonEntity implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 物业公司id
     */
    private Integer companyId;

    /**
     * 角色
     */
    private String role;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号码
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String phone;

    /**
     * 电子邮箱
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String email;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String  createBy;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String  updateBy;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
