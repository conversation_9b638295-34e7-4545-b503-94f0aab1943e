package com.coocaa.meht.converter;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-05
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ConverterFactory implements InitializingBean {
    private volatile static List<Converter> CONVERTERS = Collections.emptyList();
    private final ApplicationContext applicationContext;

    public <T> void convert(Collection<T> values) {
        CONVERTERS.forEach(converter -> converter.convert(values));
    }

    @Override
    public void afterPropertiesSet() {
        Map<String, Converter> map = applicationContext.getBeansOfType(Converter.class);
        if (MapUtil.isNotEmpty(map)) {
            CONVERTERS = ImmutableList.copyOf(map.values());
        }
    }
}
