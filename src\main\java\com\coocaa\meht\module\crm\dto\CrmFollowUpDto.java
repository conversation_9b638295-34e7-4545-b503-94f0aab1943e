package com.coocaa.meht.module.crm.dto;

import com.coocaa.meht.common.PageResult;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 跟进记录
 */

@Data
@Accessors(chain = true)
public class CrmFollowUpDto {

    private int id;

    /**
     * 跟进记录id
     */
    private String followUpId;

    /**
     * 客户id
     */
    private String activityId;

    /**
     * 商机名称
     */
    @Schema(description = "商机名称")
    private String businessName;

    /**
     * 跟进对象
     */
    private String followTarget;

    /**
     * 跟进方式 面访|电话
     */
    private String followMethod;

    /**
     * 跟进方式 面访|电话
     */
    private String category;

    /**
     * 沟通时间
     */
    private String followUpTime;

    /**
     * 沟通目的
     */
    private String followUpGoal;

    /**
     * 沟通结果
     */
    private String followUpResult;

    private String batchId;

}
