package com.coocaa.meht.utils;

import org.springframework.context.ApplicationEvent;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * Exception工具类
 */
public class ExceptionUtils {

    /**
     * 获取异常信息
     *
     * @param e 异常
     * @return 返回异常信息
     */
    public static String getMessage(Throwable e) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw, true);
        e.printStackTrace(pw);
        try {
            pw.close();
            sw.close();
        } catch (IOException ignored) {
        }
        return sw.toString();
    }

    /**
     * 获取事件对象
     *
     * @param title
     * @param e
     * @return
     */
    public static ErrorEvent bindEvent(String title, Throwable e) {
        Map<String, Object> map = new HashMap<>();
        map.put("title", title);
        map.put("errorLog", ExceptionUtils.getMessage(e));
        return new ErrorEvent(map);
    }

    public static class ErrorEvent extends ApplicationEvent {
        private static final long serialVersionUID = 1431950126416509982L;

        public ErrorEvent(Object source) {
            super(source);
        }
    }

}