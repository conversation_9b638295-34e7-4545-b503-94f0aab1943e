package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.sys.dto.SysUserDto;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dao.CommentDao;
import com.coocaa.meht.module.web.dto.CommentCreateDTO;
import com.coocaa.meht.module.web.dto.CommentDTO;
import com.coocaa.meht.module.web.dto.CommentQueryDto;
import com.coocaa.meht.module.web.entity.Comment;
import com.coocaa.meht.module.web.service.CommentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 评论服务实现类
 * @since 2025-04-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommentServiceImpl extends ServiceImpl<CommentDao, Comment> implements CommentService {


    private final SysUserService userService;

    private final SysFileService fileService;

    private final ApplicationEventPublisher applicationEventPublisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createComment(CommentCreateDTO request, String userId) {
        // 参数校验
        if (request == null) {
            throw new ServerException("评论请求不能为空");
        }
        if (StringUtils.isBlank(userId)) {
            throw new ServerException("用户ID不能为空");
        }
        if (request.getBusinessType() == null) {
            throw new ServerException("业务类型不能为空");
        }
        if (StringUtils.isBlank(request.getBusinessId())) {
            throw new ServerException("业务ID不能为空");
        }
        if (StringUtils.isBlank(request.getContent())) {
            throw new ServerException("评论内容不能为空");
        }

        log.info("创建评论: 业务类型={}, 业务ID={}, 用户ID={}", request.getBusinessType(), request.getBusinessId(), userId);

        Comment comment = new Comment();
        comment.setBusinessType(request.getBusinessType());
        comment.setBusinessId(request.getBusinessId());
        comment.setContent(request.getContent());
        comment.setCreateBy(userId);
        comment.setUpdateBy(userId);

        // 处理附件列表
        if (CollUtil.isNotEmpty(request.getAttachmentIds())) {
            List<SysFileEntity> fileEntities = fileService.getByUrls(request.getAttachmentIds());
            if (CollUtil.isNotEmpty(fileEntities)) {
                List<Long> fileIds = fileEntities.stream()
                        .map(SysFileEntity::getId)
                        .filter(Objects::nonNull)
                        .toList();
                comment.setAttachmentIds(JSON.toJSONString(fileIds));
            }
        }

        // 处理通知用户
        if (CollUtil.isNotEmpty(request.getNotifiedUsers())) {
            // 过滤空值
            List<String> validUsers = request.getNotifiedUsers().stream()
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .toList();
            if (CollUtil.isNotEmpty(validUsers)) {
                comment.setNotifiedUsers(JSON.toJSONString(validUsers));
            }
        }

        // 保存评论
        boolean success = save(comment);
        if (!success) {
            throw new ServerException("保存评论失败");
        }
        // 发布事件通知
        request.setSender(UserThreadLocal.getUser().getWno());
        applicationEventPublisher.publishEvent(request);

        log.info("创建评论成功: commentId={}", comment.getId());
        return comment.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteComment(Integer commentId, String userId) {
        // 参数校验
        if (commentId == null || commentId <= 0) {
            log.warn("删除评论失败: 评论ID无效, commentId={}", commentId);
            return false;
        }
        if (StringUtils.isBlank(userId)) {
            log.warn("删除评论失败: 用户ID为空");
            return false;
        }
        // 查询评论
        Comment comment = getById(commentId);
        if (comment == null) {
            log.warn("删除评论失败: 评论不存在, commentId={}", commentId);
            return false;
        }

        // 权限检查: 只有评论创建者才能删除评论
        if (!userId.equals(comment.getCreateBy())) {
            log.warn("删除评论失败: 没有权限, commentId={}, 当前用户={}, 创建者={}",
                    commentId, userId, comment.getCreateBy());
            throw new ServerException("没有权限删除他人评论");
        }

        log.info("删除评论: commentId={}, 用户ID={}", commentId, userId);
        boolean result = removeById(commentId);

        if (result) {
            log.info("删除评论成功: commentId={}", commentId);
        } else {
            log.warn("删除评论失败: 删除操作返回false, commentId={}", commentId);
        }

        return result;
    }

    @Override
    public Page<CommentDTO> pageComments(CommentQueryDto request) {
        // 参数校验
        if (request == null) {
            log.warn("查询评论列表失败: 请求参数为空");
            return new Page<>(1, 10);
        }

        // 默认值处理
        long current = Math.max(request.getPage(), 1L);
        long size = request.getLimit() > 0 ? request.getLimit() : 10L;

        try {
            log.info("查询评论列表: 业务类型={}, 业务ID={}, 页码={}, 每页数量={}",
                    request.getBusinessType(), request.getBusinessId(), current, size);

            // 构建查询条件
            LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(request.getBusinessType() != null, Comment::getBusinessType, request.getBusinessType())
                    .eq(StringUtils.isNotBlank(request.getBusinessId()), Comment::getBusinessId, request.getBusinessId())
                    .orderByDesc(Comment::getCreateTime);

            // 执行分页查询
            Page<Comment> page = new Page<>(current, size);
            Page<Comment> commentPage = page(page, queryWrapper);

            if (commentPage.getRecords().isEmpty()) {
                log.info("查询评论列表结果为空: 业务类型={}, 业务ID={}",
                        request.getBusinessType(), request.getBusinessId());
                return new Page<CommentDTO>(current, size, 0).setRecords(Collections.emptyList());
            }

            // 转换为DTO - 使用批量转换提高性能
            Page<CommentDTO> dtoPage = new Page<>(commentPage.getCurrent(), commentPage.getSize(), commentPage.getTotal());
            List<CommentDTO> dtoList = convertToDTOList(commentPage.getRecords());
            dtoPage.setRecords(dtoList);

            log.info("查询评论列表成功: 业务类型={}, 业务ID={}, 总数={}, 当前页={}",
                    request.getBusinessType(), request.getBusinessId(), dtoPage.getTotal(), dtoPage.getCurrent());
            return dtoPage;
        } catch (Exception e) {
            log.error("查询评论列表异常: 业务类型={}, 业务ID={}, 错误={}",
                    request.getBusinessType(), request.getBusinessId(), e.getMessage(), e);
            return new Page<CommentDTO>(current, size, 0).setRecords(Collections.emptyList());
        }
    }

    @Override
    public CommentDTO getCommentDetail(Integer commentId) {
        // 参数校验
        if (commentId == null || commentId <= 0) {
            log.warn("获取评论详情失败: 评论ID无效, commentId={}", commentId);
            return null;
        }

        try {
            log.info("获取评论详情: commentId={}", commentId);
            // 查询评论
            Comment comment = getById(commentId);
            if (comment == null) {
                log.warn("获取评论详情失败: 评论不存在, commentId={}", commentId);
                return null;
            }
            // 转换为DTO
            CommentDTO dto = convertToDTO(comment);
            log.info("获取评论详情成功: commentId={}", commentId);
            return dto;
        } catch (Exception e) {
            log.error("获取评论详情异常: commentId={}, 错误={}", commentId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Long countComments(Integer businessType, String businessId) {
        // 参数校验
        if (businessType == null) {
            log.warn("统计评论数量失败: 业务类型为空");
            return 0L;
        }
        if (StringUtils.isBlank(businessId)) {
            log.warn("统计评论数量失败: 业务ID为空");
            return 0L;
        }

        try {
            log.info("统计评论数量: 业务类型={}, 业务ID={}", businessType, businessId);

            // 构建查询条件
            LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Comment::getBusinessType, businessType)
                    .eq(Comment::getBusinessId, businessId);

            // 执行统计查询
            Long count = count(queryWrapper);
            log.info("统计评论数量成功: 业务类型={}, 业务ID={}, 数量={}", businessType, businessId, count);
            return count;
        } catch (Exception e) {
            log.error("统计评论数量异常: 业务类型={}, 业务ID={}, 错误={}", businessType, businessId, e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * 将实体列表转换为DTO列表，批量处理以提高性能
     *
     * @param comments 评论实体列表
     * @return 评论DTO列表
     */
    private List<CommentDTO> convertToDTOList(List<Comment> comments) {
        // 空值处理
        if (CollUtil.isEmpty(comments)) {
            return Collections.emptyList();
        }

        try {
            // 1. 初始化基本属性
            List<CommentDTO> dtoList = new ArrayList<>(comments.size());
            // 使用Set去重
            Set<String> allUserIds = new HashSet<>();
            List<Long> allFileIds = new ArrayList<>();

            // 2. 收集所有用户ID和文件ID
            for (Comment comment : comments) {
                // 初始化DTO对象
                CommentDTO dto = new CommentDTO();
                BeanUtils.copyProperties(comment, dto);
                dto.setNotifiedUsers(new ArrayList<>());
                dto.setAttachments(new ArrayList<>());
                dtoList.add(dto);

                // 添加创建者ID
                if (StringUtils.isNotBlank(comment.getCreateBy())) {
                    allUserIds.add(comment.getCreateBy());
                }

                // 添加通知用户ID
                if (StringUtils.isNotBlank(comment.getNotifiedUsers())) {
                    try {
                        List<String> notifiedUsers = JSON.parseArray(comment.getNotifiedUsers(), String.class);
                        if (CollUtil.isNotEmpty(notifiedUsers)) {
                            allUserIds.addAll(notifiedUsers);
                        }
                    } catch (Exception e) {
                        log.warn("解析通知用户列表异常: {}", e.getMessage());
                    }
                }

                // 添加附件ID
                if (StringUtils.isNotBlank(comment.getAttachmentIds())) {
                    try {
                        List<Long> fileIds = JSON.parseArray(comment.getAttachmentIds(), Long.class);
                        if (CollUtil.isNotEmpty(fileIds)) {
                            allFileIds.addAll(fileIds);
                        }
                    } catch (Exception e) {
                        log.warn("解析附件ID列表异常: {}", e.getMessage());
                    }
                }
            }

            // 3. 批量查询用户信息
            Map<String, SysUserDto> userNameMaps = new HashMap<>();
            if (CollUtil.isNotEmpty(allUserIds)) {
                try {
                    userNameMaps = userService.getUserNameMaps(new ArrayList<>(allUserIds));
                } catch (Exception e) {
                    log.warn("批量查询用户信息异常: {}", e.getMessage());
                }
            }

            // 4. 批量查询附件信息
            Map<Long, SysFileEntity> fileMap = new HashMap<>();
            if (CollUtil.isNotEmpty(allFileIds)) {
                try {
                    List<SysFileEntity> fileList = fileService.getBySysFileIdList(allFileIds);
                    if (CollUtil.isNotEmpty(fileList)) {
                        fileMap = fileList.stream()
                                .filter(Objects::nonNull)
                                .collect(Collectors.toMap(
                                        SysFileEntity::getId,
                                        Function.identity(),
                                        (a, b) -> a
                                ));
                    }
                } catch (Exception e) {
                    log.warn("批量查询附件信息异常: {}", e.getMessage());
                }
            }

            // 5. 填充DTO信息
            for (int i = 0; i < comments.size(); i++) {
                Comment comment = comments.get(i);
                CommentDTO dto = dtoList.get(i);

                // 处理通知用户列表
                if (StringUtils.isNotBlank(comment.getNotifiedUsers())) {
                    try {
                        List<String> notifiedUsers = JSON.parseArray(comment.getNotifiedUsers(), String.class);
                        if (CollUtil.isNotEmpty(notifiedUsers)) {
                            Map<String, SysUserDto> finalUserNameMaps = userNameMaps;
                            List<String> nameList = notifiedUsers.stream()
                                    .map(userId -> {
                                        SysUserDto userDto = finalUserNameMaps.get(userId);
                                        String userName = userDto != null ? userDto.getRealName() : "";
                                        return userId + (StrUtil.isNotBlank(userName) ? "(" + userName + ")" : "");
                                    })
                                    .filter(StringUtils::isNotBlank)
                                    .toList();
                            dto.setNotifiedUsers(nameList);
                        }
                    } catch (Exception e) {
                        log.warn("处理通知用户列表异常: {}", e.getMessage());
                    }
                }

                // 处理附件列表
                if (StringUtils.isNotBlank(comment.getAttachmentIds())) {
                    try {
                        List<Long> fileIds = JSON.parseArray(comment.getAttachmentIds(), Long.class);
                        if (CollUtil.isNotEmpty(fileIds)) {
                            List<SysFileEntity> fileList = fileIds.stream()
                                    .map(fileMap::get)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList());
                            dto.setAttachments(fileList);
                        }
                    } catch (Exception e) {
                        log.warn("处理附件列表异常: {}", e.getMessage());
                    }
                }

                // 获取创建人姓名
                try {
                    if (StringUtils.isNotBlank(comment.getCreateBy())) {
                        SysUserDto user = userNameMaps.get(comment.getCreateBy());
                        dto.setCreateByName(user != null ? user.getRealName() : comment.getCreateBy());
                    } else {
                        dto.setCreateByName("");
                    }
                } catch (Exception e) {
                    log.warn("获取用户名称失败: {}", e.getMessage());
                    dto.setCreateByName(comment.getCreateBy());
                }
            }

            return dtoList;
        } catch (Exception e) {
            log.error("转换评论列表异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 将实体转换为DTO
     *
     * @param comment 评论实体
     * @return 评论DTO
     */
    private CommentDTO convertToDTO(Comment comment) {
        if (comment == null) {
            return null;
        }

        try {
            return convertToDTOList(Collections.singletonList(comment)).get(0);
        } catch (Exception e) {
            log.error("转换评论异常: commentId={}, 错误={}", comment.getId(), e.getMessage(), e);
            // 出现异常时返回基本信息
            CommentDTO dto = new CommentDTO();
            BeanUtils.copyProperties(comment, dto);
            dto.setNotifiedUsers(Collections.emptyList());
            dto.setAttachments(Collections.emptyList());
            dto.setCreateByName(comment.getCreateBy());
            return dto;
        }
    }
}