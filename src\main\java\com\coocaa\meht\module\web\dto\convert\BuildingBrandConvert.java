package com.coocaa.meht.module.web.dto.convert;

import com.coocaa.meht.module.web.dto.BuildingBrandDto;
import com.coocaa.meht.module.web.entity.BuildingBrandEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.List;

/**
 * 楼宇品牌转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-22
 */
@Mapper
public interface BuildingBrandConvert {
    BuildingBrandConvert INSTANCE = Mappers.getMapper(BuildingBrandConvert.class);

    /**
     * Entity 转 DTO
     */
    BuildingBrandDto toDto(BuildingBrandEntity entity);

    /**
     * 转换成DTO列表
     */
    List<BuildingBrandDto> toList(Collection<BuildingBrandEntity> entities);

    /**
     * DTO 转 Entity
     */
    BuildingBrandEntity toEntity(BuildingBrandDto dto);
}