package com.coocaa.meht.config.cos;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.region.Region;
import org.springframework.util.ObjectUtils;

/**
 * 腾讯云COS基础配置类
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
public class BaseCosConfig {

    /**
     * 创建COS客户端
     * @param secretId 密钥ID
     * @param secretKey 密钥Key
     * @param regionName 区域名称（例如：ap-guangzhou）
     * @return COSClient实例
     */
    protected COSClient createCosClient(String secretId, String secretKey, String regionName) {
        if (ObjectUtils.isEmpty(secretId) || ObjectUtils.isEmpty(secretKey) || ObjectUtils.isEmpty(regionName)) {
            throw new IllegalArgumentException("COS configuration parameters cannot be empty");
        }

        // 1. 初始化用户身份信息
        COSCredentials credentials = new BasicCOSCredentials(secretId, secretKey);

        // 2. 设置bucket的区域
        Region region = new Region(regionName);
        ClientConfig clientConfig = new ClientConfig(region);

        // 3. 生成cos客户端
        return new COSClient(credentials, clientConfig);
    }

    /**
     * 创建COS客户端（带网络配置）
     * @param secretId 密钥ID
     * @param secretKey 密钥Key
     * @param regionName 区域名称
     * @param connectionTimeout 连接超时时间（毫秒）
     * @param socketTimeout socket超时时间（毫秒）
     * @return COSClient实例
     */
    protected COSClient createCosClient(String secretId, String secretKey, String regionName,
                                      int connectionTimeout, int socketTimeout) {
        if (ObjectUtils.isEmpty(secretId) || ObjectUtils.isEmpty(secretKey) || ObjectUtils.isEmpty(regionName)) {
            throw new IllegalArgumentException("COS configuration parameters cannot be empty");
        }

        // 1. 初始化用户身份信息
        COSCredentials credentials = new BasicCOSCredentials(secretId, secretKey);

        // 2. 设置bucket的区域和网络参数
        Region region = new Region(regionName);
        ClientConfig clientConfig = new ClientConfig(region);
        
        // 设置网络参数
        clientConfig.setConnectionTimeout(connectionTimeout);
        clientConfig.setSocketTimeout(socketTimeout);
        
        // 可选：设置请求协议为https
        clientConfig.setHttpProtocol(HttpProtocol.https);

        // 3. 生成cos客户端
        return new COSClient(credentials, clientConfig);
    }

    /**
     * 创建COS客户端（带完整配置）
     * @param secretId 密钥ID
     * @param secretKey 密钥Key
     * @param regionName 区域名称
     * @param connectionTimeout 连接超时时间（毫秒）
     * @param socketTimeout socket超时时间（毫秒）
     * @param maxConnectionsCount 最大连接数
     * @param userAgent User-Agent设置
     * @return COSClient实例
     */
    protected COSClient createCosClient(String secretId, String secretKey, String regionName,
                                      int connectionTimeout, int socketTimeout, 
                                      int maxConnectionsCount, String userAgent) {
        if (ObjectUtils.isEmpty(secretId) || ObjectUtils.isEmpty(secretKey) || ObjectUtils.isEmpty(regionName)) {
            throw new IllegalArgumentException("COS configuration parameters cannot be empty");
        }

        // 1. 初始化用户身份信息
        COSCredentials credentials = new BasicCOSCredentials(secretId, secretKey);

        // 2. 设置bucket的区域和详细配置
        Region region = new Region(regionName);
        ClientConfig clientConfig = new ClientConfig(region);
        
        // 设置网络参数
        clientConfig.setConnectionTimeout(connectionTimeout);
        clientConfig.setSocketTimeout(socketTimeout);
        clientConfig.setMaxConnectionsCount(maxConnectionsCount);
        
        // 设置User-Agent
        if (!ObjectUtils.isEmpty(userAgent)) {
            clientConfig.setUserAgent(userAgent);
        }
        
        // 设置https协议
        clientConfig.setHttpProtocol(HttpProtocol.https);

        // 3. 生成cos客户端
        return new COSClient(credentials, clientConfig);
    }
} 