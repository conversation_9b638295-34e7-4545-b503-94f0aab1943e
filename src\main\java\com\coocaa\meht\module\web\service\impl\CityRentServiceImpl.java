package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.CityRentDao;
import com.coocaa.meht.module.web.entity.CityRentEntity;
import com.coocaa.meht.module.web.service.CityRentService;
import org.apache.groovy.util.Maps;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Created by fengke on 2024/11/11.
 */
@Service
public class CityRentServiceImpl extends ServiceImpl<CityRentDao, CityRentEntity> implements CityRentService {


    @Override
    public CityRentEntity getRent(String adCode) {
        List<CityRentEntity> list = this.listByMap(Maps.of("ad_code", adCode));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}
