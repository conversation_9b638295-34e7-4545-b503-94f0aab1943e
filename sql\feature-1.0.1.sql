ALTER TABLE `building_details`
    ADD COLUMN `building_brand_id` BIGINT (20) UNSIGNED DEFAULT 0 NULL COMMENT 'TOP100品牌ID' AFTER `building_brand`,
    ADD COLUMN `building_brand_name` VARCHAR (200) DEFAULT '' NULL COMMENT 'TOP100品牌名称' AFTER `building_brand_id`;

ALTER TABLE `building_rating`
    ADD COLUMN `customer_id`   VARCHAR(64) DEFAULT '' NULL COMMENT 'CRM客户记录id',
    ADD COLUMN `crm_flow_id`   VARCHAR(64) DEFAULT '' NULL COMMENT 'CRM流程状态ID' AFTER `customer_id`,
    ADD COLUMN `crm_flow_name` VARCHAR(64) DEFAULT '' NULL COMMENT 'CRM流程状态名称' AFTER `crm_flow_id`;
