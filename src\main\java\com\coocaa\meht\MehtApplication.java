package com.coocaa.meht;

import cn.hutool.crypto.SecureUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@MapperScan("com.coocaa.meht.module.*.dao")
@EnableFeignClients(basePackages = {"com.coocaa.meht.rpc", "com.coocaa.ad.common.user.rpc"})
@SpringBootApplication(scanBasePackages = "com.coocaa", exclude = {com.coocaa.ad.common.config.WebAutoConfig.class})
public class MehtApplication {

    public static void main(String[] args) {
        SecureUtil.disableBouncyCastle();
        SpringApplication springApplication = new SpringApplication(MehtApplication.class);
        springApplication.setAllowCircularReferences(true);
        springApplication.run(args);
    }

}