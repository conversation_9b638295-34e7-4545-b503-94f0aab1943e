package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.web.service.IBuildingRatingCorrectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.List;

/**
 * 楼宇评分修正
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-27 11:45
 */
@RestController
@RequestMapping("/buildRating/ai-score")
@Tag(name = "楼宇评分修正相关接口", description = "楼宇评分修正相关接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingRatingCorrectController {
    private final IBuildingRatingCorrectService buildingRatingCorrectService;

    /**
     * 重新生成AI评分过程数据
     */
    @Operation(summary = "重新生成AI评分过程数据")
    @PostMapping("/detail")
    public Result<Boolean> detail(@RequestBody(required = false) List<String> buildingNos) {
        return Result.ok(buildingRatingCorrectService.aiScoreDetail(buildingNos));
    }

    /**
     * 根据已有AI评分项，重新计算并更新AI评分
     */
    @Operation(summary = "根据已有AI评分项，重新计算并更新AI评分")
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody(required = false) List<String> buildingNos) {
        return Result.ok(buildingRatingCorrectService.aiScoreUpdate(buildingNos));
    }

    /**
     * 导出AI评分的详情
     */
    @Operation(summary = "导出AI评分的详情")
    @PostMapping("/export")
    public Result<String> export(@RequestBody(required = false) List<String> cityNames) {
        return Result.ok(buildingRatingCorrectService.aiScoreExport(cityNames));
    }

    /**
     * 导出AI评分的详情（临时）
     */
    @Operation(summary = "导出AI评分的详情")
    @PostMapping("/export-temp")
    public ResponseEntity<InputStreamResource> exportTemp(@RequestBody(required = false) List<String> cityNames) throws FileNotFoundException {
        File file = buildingRatingCorrectService.getAiScoreUploadFile(cityNames);
        // 将 File 转换为 Resource
        InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=example%s.xlsx".formatted(System.currentTimeMillis()))
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .contentLength(file.length())
                .body(resource);
    }

    /**
     * 根据已有的过程详情数据再次计算AI单项分
     */
    @Operation(summary = "再次计算AI单项分")
    @PostMapping("/detail/recalculate")
    public Result<Boolean> recalculate(@RequestParam(value = "batch", required = false) Integer batch,
                                       @RequestBody(required = false) List<String> buildingNos) {
        return Result.ok(buildingRatingCorrectService.aiScoreDetailRecalculate(buildingNos, batch));
    }
}
