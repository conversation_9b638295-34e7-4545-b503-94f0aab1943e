package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.converter.ConverterFactory;
import com.coocaa.meht.module.web.dao.PointPriceSnapshotMapper;
import com.coocaa.meht.module.web.dto.convert.PointConvert;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.dto.point.PointDetailParam;
import com.coocaa.meht.module.web.dto.point.ProjectPointVO;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.PointEntity;
import com.coocaa.meht.module.web.entity.PointPicPriceSnapshotEntity;
import com.coocaa.meht.module.web.entity.PointPriceSnapshotEntity;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.IPointPicPriceSnapshotService;
import com.coocaa.meht.module.web.service.IPointPriceSnapshotService;
import com.coocaa.meht.module.web.service.PointService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 点位价格快照表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
public class PointPriceSnapshotServiceImpl extends ServiceImpl<PointPriceSnapshotMapper, PointPriceSnapshotEntity> implements IPointPriceSnapshotService {

    @Autowired
    private BuildingRatingService buildingRatingService;
    @Autowired
    private PointConvert pointConvert;
    @Autowired
    private IPointPicPriceSnapshotService pointPicPriceSnapshotService;
    @Autowired
    private ConverterFactory converterFactory;
    @Autowired
    private PointService pointService;
    @Override
    public List<PointPriceSnapshotEntity> listByBuildingNos(Collection<String> buildingNos) {
        if(CollectionUtil.isNotEmpty(buildingNos)){
            return lambdaQuery().in(PointPriceSnapshotEntity::getBuildingRatingNo,buildingNos).list();
        }
        return new ArrayList<>();
    }

    @Override
    public ProjectPointVO listBuildingPoint(PointDetailParam param) {
        ProjectPointVO vo = new ProjectPointVO();
        String buildingNo = param.getBuildingNo();
        BuildingRatingEntity entity = buildingRatingService.getByBuildingNo(buildingNo);
        String buildingName = entity.getBuildingName();
        vo.setProjectName(buildingName);
        Integer pointPlanId = param.getPointPlanId();
        List<PointPriceSnapshotEntity> list = listByPointPlanId(pointPlanId);

        if(CollectionUtil.isNotEmpty(list)){
            Set<String> codes = list.stream().map(PointPriceSnapshotEntity::getCode).collect(Collectors.toSet());
            Map<String, String> pointDeviceSize = pointService.lambdaQuery().select(PointEntity::getCode, PointEntity::getDeviceSize)
                    .in(PointEntity::getCode, codes)
                    .list().stream().collect(Collectors.toMap(PointEntity::getCode, e -> e.getDeviceSize()));
            String pointStatus = list.get(0).getPointStatus();
            vo.setPointPlanStatus(pointStatus);
            List<PointDetail> detail = pointConvert.toPointDetailsByPrice(list);
            detail.forEach(e->{
                e.setDeviceSize(pointDeviceSize.get(e.getPointCode()));
            });
            converterFactory.convert(detail);
            vo.setPointDetails(detail);
            setPics(detail);
        }
        converterFactory.convert(Lists.newArrayList(vo));
        return vo;

    }

    private void setPics(List<PointDetail> detail) {
        List<Integer> pointIds = detail.stream().map(PointDetail::getPointId)
                .collect(Collectors.toList());
        List<PointPicPriceSnapshotEntity> entityList = pointPicPriceSnapshotService.listByPointIds(pointIds);
        if(CollectionUtil.isNotEmpty(entityList)){
            Map<Integer, List<PointPicPriceSnapshotEntity>> map =
                    entityList.stream().collect(Collectors.groupingBy(PointPicPriceSnapshotEntity::getPointId));
            detail.forEach(e->{
                List<PointPicPriceSnapshotEntity> pics = map.get(e.getPointId());
                if(CollectionUtil.isNotEmpty(pics)){
                    e.setPointPics(pics.stream().map(PointPicPriceSnapshotEntity::getPic)
                            .collect(Collectors.toList()));
                }

            });
        }
    }

    private List<PointPriceSnapshotEntity> listByPointPlanId(Integer pointPlanId) {
        return lambdaQuery().eq(PointPriceSnapshotEntity::getPointPlanId,pointPlanId)
                .list();
    }
}