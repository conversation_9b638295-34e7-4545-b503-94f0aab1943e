package com.coocaa.meht.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 完善评级可回滚操作标识注解
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-07
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.TYPE})
public @interface RollBack {

    /**
     * 是否可以回滚标识，默认true
     */
    boolean value() default true;

}
