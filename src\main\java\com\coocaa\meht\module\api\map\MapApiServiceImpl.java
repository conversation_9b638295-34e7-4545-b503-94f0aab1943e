package com.coocaa.meht.module.api.map;

import com.coocaa.meht.module.web.dto.CityAddressDto;
import com.coocaa.meht.module.web.dto.MapAddressDto;
import com.coocaa.meht.utils.HttpUtils;
import com.coocaa.meht.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * Created by fengke on 2024/11/7.
 */
@Component
public class MapApiServiceImpl implements MapApiService{

    private static final String URL = "https://api.map.baidu.com/place/v2/suggestion";
    private static final String URL1 = "https://api.map.baidu.com/reverse_geocoding/v3";
    private static final String URL2 = "https://api.map.baidu.com/geoconv/v2/";
//    private static final String AK = "i7bwRkMpZKzmIF7qXaXXjJiderm5TeUe";

    @Value("${mapak}")
    private String mapak;

    @Override
    public Map<String, Object> getJsonMap(String query, String region, String city_limit, String output,String tag) throws UnsupportedEncodingException {
        String newUrl = URL+"?query="+ URLEncoder.encode(query, "UTF-8")+"&region="+region+"&city_limit="+city_limit+"&output="+output+"&tag="+tag+"&ak="+mapak;
        String response = HttpUtils.get(newUrl);
        Map<String, Object> json = JsonUtils.fromMap(response);
        return json;
    }

    @Override
    public Map<String, Object> getAccurate(String latitude,String longitude,String extensions_poi,String poi_types) {
        String location = latitude+","+longitude;
        String newUrl = URL1+"?ak="+mapak+"&output=json&location="+location+"&extensions_poi="+extensions_poi+"&poi_types="+poi_types;
        String response = HttpUtils.get(newUrl);
        Map<String, Object> json = JsonUtils.fromMap(response);
        return json;
    }

    @Override
    public MapAddressDto getConvertAddress(String latitude, String longitude){
        String location = latitude+","+longitude;
        String newUrl = URL2+"?coords="+location+"&model=3&ak="+mapak;
        String response = HttpUtils.get(newUrl);
        Map<String, Object> json = JsonUtils.fromMap(response);
        List<?>  list = (List<?>)json.get("result");
        MapAddressDto person = JsonUtils.fromJson(JsonUtils.toJson(list.get(0)), MapAddressDto.class);
        return person;
    }

    @Override
    public CityAddressDto getCityId(String query, String region) {
        query = URLEncoder.encode(query, Charset.defaultCharset());
        String newUrl = URL+"?query="+query+"&region="+region+"&city_limit=true&output=json"+"&ak="+mapak;
        String response = HttpUtils.get(newUrl);
        Map<String, Object> json = JsonUtils.fromMap(response);
        List<?>  list = (List<?>)json.get("result");
        CityAddressDto person = JsonUtils.fromJson(JsonUtils.toJson(list.get(0)), CityAddressDto.class);
        return person;
    }
}
