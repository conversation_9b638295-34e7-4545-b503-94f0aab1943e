package com.coocaa.meht.module.web.vo.proxy;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/5
 * @description 楼宇助手
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class BrandDataVO {
    //本品点位列表
    private List<BuildingBrandPointVO> selfBrandPoints;
    //竞品点位列表
    private List<BuildingBrandPointVO> competitiveBrandPoints;
    //余量点位列表
    private List<BuildingBrandPointVO> undevelopedBrandPoints;
}
