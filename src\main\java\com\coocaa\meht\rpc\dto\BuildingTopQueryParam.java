package com.coocaa.meht.rpc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20
 */
@Data
public class BuildingTopQueryParam {
    /**
     * 楼宇名称
     */
    @Schema(description = "楼宇名称")
    private String buildingName;

    /**
     * 省份
     */
    @Schema(description = "省份")
    private String province;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String city;

    /**
     * TOP等级
     */
    @Schema(description = "TOP等级")
    private String topLevel;

    /**
     * 楼宇类型
     */
    @Schema(description = "楼宇类型")
    private String buildingType;
}
