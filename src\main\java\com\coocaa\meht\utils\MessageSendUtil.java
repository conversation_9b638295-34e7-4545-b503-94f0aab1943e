package com.coocaa.meht.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.coocaa.meht.kafka.KafkaProducerService;
import com.coocaa.meht.rpc.dto.UserBatchMessageParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 消息发送器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-23
 */
@Slf4j
@Component
public class MessageSendUtil {

    private static final String TOPIC_MSG_SEND = "msg_send";

    @Autowired
    private KafkaProducerService kafkaProducerService;

    public void sendMessage(UserBatchMessageParam message) {
        String msg = JSON.toJSONString(message);
        log.info("发送消息，message:{}", msg);
        kafkaProducerService.sendMessage(TOPIC_MSG_SEND, msg);
    }

}
