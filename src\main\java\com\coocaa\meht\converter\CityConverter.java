package com.coocaa.meht.converter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * 城市相关数据翻译
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-05
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CityConverter extends BaseConverter<Integer> {
    private final CodeNameHelper codeNameHelper;

    @Override
    protected ConvertType getConvertType() {
        return ConvertType.CITY;
    }

    @Override
    protected Map<Integer, String> getNameMapping(Collection<Integer> keys) {
        return codeNameHelper.getCityMapping(keys);
    }

}
