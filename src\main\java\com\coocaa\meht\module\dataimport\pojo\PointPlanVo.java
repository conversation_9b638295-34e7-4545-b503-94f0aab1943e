package com.coocaa.meht.module.dataimport.pojo;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/1/15
 * @description 洗点位数据
 */
@Data
public class PointPlanVo {

    /**
     * 楼宇编码
     * */
    private String buildingRatingNo;
    /**
     * 签约状态字典0037
     * */
    private String status;
    private String createBy;
    private LocalDateTime createTime;
    private String updateBy;
    private LocalDateTime updateTime;
    /**
     * 商机编码与楼宇编码一致
     * */
    private String businessCode;
}
