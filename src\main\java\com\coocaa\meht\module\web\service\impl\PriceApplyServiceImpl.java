package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.alibaba.nacos.shaded.com.google.common.collect.Sets;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.PermissionDTO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.common.constants.TopicConstants;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.common.handler.PermissionHandler;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.kafka.KafkaProducerService;
import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.dto.InnerApproveTemplateParam;
import com.coocaa.meht.module.approve.dto.ScreenApproveRecordDTO;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.module.approve.enums.ApproveFieldTypeEnum;
import com.coocaa.meht.module.approve.facade.ApprovalCenterFacade;
import com.coocaa.meht.module.approve.service.impl.ApprovalQueryServiceImpl;
import com.coocaa.meht.module.building.entity.BuildingScreenEntity;
import com.coocaa.meht.module.building.entity.CompleteRatingEntity;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.sys.dto.SysUserDto;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dao.PriceApplyDao;
import com.coocaa.meht.module.web.dto.BuildingGeneDTO;
import com.coocaa.meht.module.web.dto.PriceApplyApproveDto;
import com.coocaa.meht.module.web.dto.PriceApplyDetailDto;
import com.coocaa.meht.module.web.dto.PriceApplyDeviceDto;
import com.coocaa.meht.module.web.dto.PriceApplyDto;
import com.coocaa.meht.module.web.dto.PriceApplyListCmsDto;
import com.coocaa.meht.module.web.dto.PriceApplyListDto;
import com.coocaa.meht.module.web.dto.convert.PriceApplyConvert;
import com.coocaa.meht.module.web.dto.convert.PriceApplyDeviceConvert;
import com.coocaa.meht.module.web.dto.convert.SysFileConvert;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.dto.point.ProjectPointVO;
import com.coocaa.meht.module.web.dto.req.PointByApplyNumberReq;
import com.coocaa.meht.module.web.dto.req.PriceApplyQueryCmsReq;
import com.coocaa.meht.module.web.dto.req.PriceApplyQueryReq;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BuildingSnapshotEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.PointEntity;
import com.coocaa.meht.module.web.entity.PriceApplyDeviceEntity;
import com.coocaa.meht.module.web.entity.PriceApplyDevicePointEntity;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.coocaa.meht.module.web.entity.ScreenApproveRecordEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.enums.SceneTypeEnum;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingGeneService;
import com.coocaa.meht.module.web.service.BuildingParameterService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BuildingSnapshotService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.MessageRecordService;
import com.coocaa.meht.module.web.service.PointService;
import com.coocaa.meht.module.web.service.PriceApplyDevicePointService;
import com.coocaa.meht.module.web.service.PriceApplyDeviceService;
import com.coocaa.meht.module.web.service.PriceApplyService;
import com.coocaa.meht.module.web.service.ScreenApproveRecordService;
import com.coocaa.meht.module.web.vo.BuildingGeneVO;
import com.coocaa.meht.module.web.vo.BusinessStatusChangeVO;
import com.coocaa.meht.module.web.vo.PriceApplyBasicVO;
import com.coocaa.meht.module.web.vo.PriceApplyDevicePointJoinVO;
import com.coocaa.meht.module.web.vo.common.ConfigVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.FeignSspRpc;
import com.coocaa.meht.utils.AddressUtil;
import com.coocaa.meht.utils.BigDecimalUtils;
import com.coocaa.meht.utils.CodeGenerator;
import com.coocaa.meht.utils.JsonUtils;
import com.coocaa.meht.utils.KafkaProducer;
import com.coocaa.meht.utils.RsaExample;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 价格申请
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PriceApplyServiceImpl extends ServiceImpl<PriceApplyDao, PriceApplyEntity> implements PriceApplyService {

    @Value("${price.approval.message.topic:cheese-venue-price-point}")
    private String approvalMessageTopic;
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final int SCREEN_FLAG_SMALL = 1, SCREEN_FLAG_LARGE = 2, SCREEN_FLAG_BOTH = 3;

    private final StringRedisTemplate stringRedisTemplate;
    private final PriceApplyDeviceService applyDeviceService;
    private final SysUserService sysUserService;
    private final BuildingRatingService buildingRatingService;
    private final SysFileService sysFileService;
    private final PriceApplyDevicePointService priceApplyDevicePointService;
    private final PointService pointService;
    private final KafkaProducer kafkaProducer;
    private final BusinessOpportunityService businessOpportunityService;
    private final KafkaProducerService kafkaProducerService;
    private final FeignSspRpc feignSspRpc;
    private final PriceApplyDeviceService priceApplyDeviceService;
    private final FeignCmsRpc feignCmsRpc;
    private final FeignAuthorityRpc feignAuthorityRpc;
    private final LargeScreenProperties largeScreenProperties;
    private final ScreenApproveRecordService screenApproveRecordService;
    private final MessageRecordService messageRecordService;
    private final BuildingParameterService buildingParameterService;
    private final BuildingDetailsService buildingDetailsService;
    private final SysUserService userService;
    private final BuildingGeneService buildingGeneService;
    private final RsaExample rsaExample;
    private final CompleteRatingService completeRatingService;
    private final ApprovalCenterFacade approvalCenterFacade;
    private final ApprovalQueryServiceImpl approvalQueryService;
    private final BuildingScreenService buildingScreenService;
    private final BuildingSnapshotService buildingSnapshotService;

    private final CodeGenerator codeGenerator;

    private final PermissionHandler permissionHandler;


    @Override
    public PageResult<PriceApplyListDto> listPriceApplyList(PriceApplyQueryReq req) {
        PermissionDTO cmsPermission = permissionHandler.getCmsPermission(req.getCreateBy(), null);
        if (Objects.isNull(cmsPermission.getCities()) || Objects.isNull(cmsPermission.getUserCodes())) {
            // 没有符合权限的条件，直接返回空数据
            return new PageResult<>(Collections.emptyList(), 0);
        }

        Page<PriceApplyEntity> page = new Page<>(req.getPage(), req.getLimit());

        //  列表查询
        Page<PriceApplyEntity> entityPage = listPage(req, cmsPermission, page);

        List<PriceApplyEntity> records = entityPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageResult<>(Collections.emptyList(), page.getTotal());
        }

        List<PriceApplyListDto> priceApplyList = PriceApplyConvert.INSTANCE.toPriceApplyList(records);
        // 获取申请人名
        List<String> createCodeList = priceApplyList.stream().map(PriceApplyListDto::getCreateBy)
                .collect(Collectors.toList());
        List<LoginUser> loginUsers = sysUserService.listUserByEmpCode(createCodeList);
        Map<String, String> userNameMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(loginUsers)) {
            userNameMap = loginUsers.stream()
                    .collect(Collectors.toMap(LoginUser::getUserCode, LoginUser::getUserName, (k1, k2) -> k2));
        }

        // 获取商机
        List<String> businessCodeList = priceApplyList.stream().map(PriceApplyListDto::getBusinessCode)
                .collect(Collectors.toList());
        List<BusinessOpportunityEntity> businessOpportunityEntityList = businessOpportunityService.list(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                .in(BusinessOpportunityEntity::getCode, businessCodeList));
        Map<String, BusinessOpportunityEntity> businessOpportunityMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(businessOpportunityEntityList)) {
            businessOpportunityMap = businessOpportunityEntityList.stream()
                    .collect(Collectors.toMap(BusinessOpportunityEntity::getCode, Function.identity(), (k1, k2) -> k2));
        }

        String userCode = SecurityUser.getUserCode();
        for (PriceApplyListDto priceApplyListDto : priceApplyList) {
            priceApplyListDto.setCreateName(userNameMap.get(priceApplyListDto.getCreateBy()));
            priceApplyListDto.setIsSelfApply(userCode.equals(priceApplyListDto.getCreateBy()));
            Optional.ofNullable(businessOpportunityMap.get(priceApplyListDto.getBusinessCode()))
                    .ifPresent(businessOpportunity -> {
                        priceApplyListDto.setBusinessName(businessOpportunity.getName());
                    });
            // 详细地址解密
            priceApplyListDto.setMapAddress(rsaExample.decryptByPrivate(priceApplyListDto.getMapAddress()));
            // 楼宇类型  原来是放projectName里面的  后续考虑换个字段
            priceApplyListDto.setProjectType(BuildingRatingEntity.BuildingType.getNameByValue(priceApplyListDto.getBuildingType()));
        }

        return new PageResult<>(priceApplyList, page.getTotal());
    }

    private Page<PriceApplyEntity> listPage(PriceApplyQueryReq req, PermissionDTO cmsPermission, Page<PriceApplyEntity> page) {
        // 有权限的城市
        List<String> cities = cmsPermission.getCities();
        // 有权限的用户
        List<String> userCodes = cmsPermission.getUserCodes();

        LambdaQueryWrapper<PriceApplyEntity> queryWrapper = new QueryWrapper<PriceApplyEntity>().lambda()
                .in(CollectionUtil.isNotEmpty(userCodes), PriceApplyEntity::getCreateBy, userCodes)
                .in(CollectionUtil.isNotEmpty(req.getStatus()), PriceApplyEntity::getStatus, req.getStatus())
                .in(CollectionUtil.isNotEmpty(cities), PriceApplyEntity::getMapCity, cities)
                .like(StringUtils.isNotBlank(req.getBuildingName()), PriceApplyEntity::getBuildingName, req.getBuildingName())
                .between(null != req.getStartTime() && null != req.getEndTime(), PriceApplyEntity::getCreateTime, req.getStartTime(), req.getEndTime())
                .and(wrapper -> {
                    wrapper.ne(PriceApplyEntity::getStatus, PriceApplyEntity.Status.DRAFT.getCode())
                            .or()
                            .eq(PriceApplyEntity::getCreateBy, UserThreadLocal.getUser().getWno());
                })
                .orderByDesc(PriceApplyEntity::getUpdateTime);
        return page(page, queryWrapper);
    }

    @Override
    public PriceApplyDetailDto getPriceApplyDetail(Integer id, String version) {
        // 获取基础数据
        PriceApplyBasicVO priceApplyInfo = getPriceApplyInfo(id, version);
        PriceApplyEntity priceApply = priceApplyInfo.getPriceApplyEntity();
        // 判断是否时草稿数据，如果时草稿数据 则返回草稿json中的数据  不走后续逻辑
        if (priceApply != null && priceApply.getStatus().equals(PriceApplyEntity.Status.DRAFT.getCode())) {
            String draft = priceApply.getDraft();
            PriceApplyDetailDto priceApplyDetailDto = JsonUtils.fromJson(draft, PriceApplyDetailDto.class);
            // 处理附件信息
            setFiles(priceApplyDetailDto);
            // 处理项目相关的信息
            priceApplyDetailDto.setProjectName(priceApply.getBuildingName());
            priceApplyDetailDto.setProjectType(BuildingRatingEntity.BuildingType.getNameByValue(priceApply.getBuildingType()));
            priceApplyDetailDto.setScore(priceApply.getProjectLevel());
            // 草稿的多返回一个创建人信息
            priceApplyDetailDto.setApplyCode(priceApply.getApplyCode());
            priceApplyDetailDto.setCreateBy(priceApply.getCreateBy());
            priceApplyDetailDto.setIsSelfApply(priceApply.getCreateBy().equals(UserThreadLocal.getUser().getWno()));
            return priceApplyDetailDto;
        }

        PriceApplyEntity priceApplyEntity = Optional.ofNullable(priceApply)
                .orElseThrow(() -> new ServerException("不存在该申请"));
        BuildingRatingEntity buildingRatingEntity = Optional.ofNullable(buildingRatingService.getByBuildingNo(priceApplyEntity.getBuildingNo()))
                .orElseThrow(() -> new ServerException("不存在楼宇信息"));
        BusinessOpportunityEntity businessOpportunity = Optional.ofNullable(businessOpportunityService.getOne(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                        .eq(BusinessOpportunityEntity::getCode, priceApplyEntity.getBusinessCode())))
                .orElseThrow(() -> new ServerException("不存在商机信息"));

        PriceApplyDetailDto priceApplyDetailDto = PriceApplyConvert.INSTANCE.toPriceApplyDetailDto(priceApplyEntity);
        priceApplyDetailDto.setProjectName(priceApplyEntity.getBuildingName())
                .setProjectType(BuildingRatingEntity.BuildingType.getNameByValue(priceApplyEntity.getBuildingType()))
                .setScore(priceApplyEntity.getProjectLevel())
                .setMapAddress(priceApplyEntity.getMapProvince() + "," + priceApplyEntity.getMapCity() + "," + priceApplyEntity.getMapRegion())
                .setCity(priceApplyEntity.getMapCity())
                .setRatingVersion(priceApplyEntity.getRatingVersion())
                .setMapProvince(priceApplyEntity.getMapProvince())
                .setMapRegion(priceApplyEntity.getMapRegion())
                .setAddress(rsaExample.decryptByPrivate(priceApplyEntity.getMapAddress()))
                .setLatitude(rsaExample.decryptByPrivate(buildingRatingEntity.getMapLatitude()))
                .setLongitude(rsaExample.decryptByPrivate(buildingRatingEntity.getMapLongitude()))
                .setCreateName(Objects.isNull(sysUserService.getUserByEmpCode(priceApplyEntity.getCreateBy())) ? null : sysUserService.getUserByEmpCode(priceApplyEntity.getCreateBy()).getUserName())
                .setBusinessName(businessOpportunity.getName())
                .setBusinessCode(businessOpportunity.getCode());

        // 设置简略地址
        priceApplyDetailDto.setSimplifyAddress(
                AddressUtil.simplify(
                        priceApplyDetailDto.getAddress(),
                        priceApplyDetailDto.getMapProvince(),
                        priceApplyDetailDto.getCity(),
                        priceApplyDetailDto.getMapRegion())
        );

        // 兼容H5
        try {
            priceApplyDetailDto.setIsSelfApply(SecurityUser.getUser().getUserCode()
                    .equals(priceApplyEntity.getCreateBy()));
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }

        if (StringUtils.isNotBlank(priceApplyEntity.getApproveBy())) {
            LoginUser loginUser = sysUserService.getUserByEmpCode(priceApplyEntity.getApproveBy());
            if (loginUser != null) {
                priceApplyDetailDto.setApproveName(loginUser.getUserName());
            }
        }

        LoginUser loginUser = sysUserService.getUserByEmpCode(priceApplyEntity.getCreateBy());
        if (loginUser != null) {
            priceApplyDetailDto.setCreateName(loginUser.getUserName());
        }

        priceApplyDetailDto.setPointCount(0);

        List<PriceApplyDeviceEntity> applyDeviceList = priceApplyInfo.getPriceApplyDeviceList();
        if (CollUtil.isNotEmpty(applyDeviceList)) {
            List<PriceApplyDetailDto.DeviceDetailDto> deviceDetailDtoList = new ArrayList<>();
            Map<Integer, List<PriceApplyDevicePointEntity>> devicePointMap = priceApplyInfo.getPriceApplyDevicePointList().stream()
                    .collect(Collectors.groupingBy(PriceApplyDevicePointEntity::getPriceApplyDeviceId));
            for (PriceApplyDeviceEntity priceApplyDeviceEntity : applyDeviceList) {
                PriceApplyDetailDto.DeviceDetailDto deviceDetailDto = PriceApplyDeviceConvert.INSTANCE.toDeviceDetailDto(priceApplyDeviceEntity);
                if (!devicePointMap.isEmpty()) {
                    List<PriceApplyDetailDto.DevicePointDto> devicePointsDto = devicePointMap.get(priceApplyDeviceEntity.getId())
                            .stream()
                            .map(priceApplyDevicePointEntity -> new PriceApplyDetailDto.DevicePointDto(priceApplyDevicePointEntity.getPointCode(), priceApplyDevicePointEntity.getPointName(), true))
                            .collect(Collectors.toList());
                    deviceDetailDto.setPoints(devicePointsDto);
                }
                deviceDetailDto.setTotalUnitPrice(BigDecimalUtils.add(deviceDetailDto.getIncentivePrice(), deviceDetailDto.getSignPrice()));

                // 设置水位价
                Integer largeScreenFlag = deviceDetailDto.getLargeScreenFlag();
                // 大屏
                if (Objects.equals(largeScreenFlag, SCREEN_FLAG_BOTH) || Objects.equals(largeScreenFlag, SCREEN_FLAG_LARGE)) {
                    deviceDetailDto.setWaterMarkPriceForBig(String.valueOf(priceApply.getBigWaterMarkPrice()));
                }
                // 小屏
                if (Objects.equals(largeScreenFlag, SCREEN_FLAG_BOTH) || largeScreenFlag <= SCREEN_FLAG_SMALL) {
                    deviceDetailDto.setWaterMarkPriceForSmall(String.valueOf(priceApply.getSmallWaterMarkPrice()));
                }

                deviceDetailDtoList.add(deviceDetailDto);
            }
            priceApplyDetailDto.setDevices(deviceDetailDtoList);
            priceApplyDetailDto.setPointCount(deviceDetailDtoList.stream()
                    .filter(device -> CollUtil.isNotEmpty(device.getPoints()))
                    .mapToInt(device -> device.getPoints().size()).sum());
        }

        // 处理附件信息
        setFiles(priceApplyDetailDto);

        // 查询审批记录 并返回
        Result<List<ScreenApproveRecordDTO>> approveResult = approvalQueryService.queryLocalNodes(priceApplyEntity.getApplyCode());
        priceApplyDetailDto.setApproveRecords(null != approveResult ? approveResult.getData() : null);

        // 判断下一个审批人是否是当前登陆人
        priceApplyDetailDto.setIsSelfApprove(approvalQueryService.judgeNextApproveUser(priceApplyDetailDto.getInstanceCode()));

        return priceApplyDetailDto;
    }

    /**
     * 根据id和版本获取基础数据   版本可以不传  保底获取正常表中的基础数据
     */
    private PriceApplyBasicVO getPriceApplyInfo(Integer id, String version) {
        PriceApplyBasicVO priceApplyBasicVO = new PriceApplyBasicVO();
        PriceApplyEntity priceApply = getById(id);
        if (null == priceApply) {
            return priceApplyBasicVO;
        }
        // 详情查询传了version的时候才考虑走快照查询
        if (StringUtils.isNotBlank(version) && !priceApply.getVersion().equals(version)) {
            // 查快照库
            BuildingSnapshotEntity priceApplySnapshot = buildingSnapshotService.lambdaQuery()
                    .eq(BuildingSnapshotEntity::getBuildingRatingNo, priceApply.getApplyCode())
                    .eq(BuildingSnapshotEntity::getRatingVersion, version)
                    .eq(BuildingSnapshotEntity::getType, BuildingSnapshotEntity.Type.PRICE_APPLY.getValue())
                    .one();
            if (null != priceApplySnapshot) {
                if (null != priceApplySnapshot.getMetaSnapshot()) {
                    priceApplyBasicVO.setPriceApplyEntity(JsonUtils.fromJson(priceApplySnapshot.getMetaSnapshot(), PriceApplyEntity.class));
                }
                if (null != priceApplySnapshot.getRatingSnapshot()) {
                    priceApplyBasicVO.setPriceApplyDeviceList(JsonUtils.fromJson(priceApplySnapshot.getRatingSnapshot(), new TypeReference<>() {
                    }));
                }
                if (null != priceApplySnapshot.getDetailsSnapshot()) {
                    priceApplyBasicVO.setPriceApplyDevicePointList(JsonUtils.fromJson(priceApplySnapshot.getDetailsSnapshot(), new TypeReference<>() {
                    }));
                }
                return priceApplyBasicVO;
            }
        }
        // 正常查询
        List<PriceApplyDeviceEntity> applyDeviceList = applyDeviceService.findByPriceApplyId(priceApply.getId());
        if (CollUtil.isNotEmpty(applyDeviceList)) {
            List<Integer> applyDeviceIds = applyDeviceList.stream().map(PriceApplyDeviceEntity::getId)
                    .collect(Collectors.toList());
            List<PriceApplyDevicePointEntity> priceApplyDevicePointEntities = priceApplyDevicePointService
                    .list(Wrappers.<PriceApplyDevicePointEntity>lambdaQuery()
                            .in(PriceApplyDevicePointEntity::getPriceApplyDeviceId, applyDeviceIds));
            priceApplyBasicVO.setPriceApplyDevicePointList(priceApplyDevicePointEntities);
        }

        priceApplyBasicVO.setPriceApplyEntity(priceApply);
        priceApplyBasicVO.setPriceApplyDeviceList(applyDeviceList);
        return priceApplyBasicVO;
    }

    /**
     * 把附件信息设置到详情中
     *
     * @param priceApplyDetailDto 详情
     */
    private void setFiles(PriceApplyDetailDto priceApplyDetailDto) {
        if (CollUtil.isNotEmpty(priceApplyDetailDto.getFileIds())) {
            List<SysFileEntity> sysFileIdList = sysFileService.getBySysFileIdList(priceApplyDetailDto.getFileIds());
            if (CollUtil.isNotEmpty(sysFileIdList)) {
                priceApplyDetailDto.setFiles(SysFileConvert.INSTANCE.toFilesDetailDto(sysFileIdList));
            }
        } else {
            priceApplyDetailDto.setFileIds(Collections.emptyList());
            priceApplyDetailDto.setFiles(Collections.emptyList());
        }
    }


    /**
     * 获取楼宇的城市水位价配置信息
     *
     * @param buildingRatingEntity 楼宇基本信息
     * @return 水位价配置信息
     */
    @Override
    public ConfigVO getCityWaterMarkPrice(BuildingRatingEntity buildingRatingEntity) {
        // 获取城市ID
        CodeNameVO cityInfo = RpcUtils.unBox(feignAuthorityRpc.getByCityName(buildingRatingEntity.getMapCity()));
        if (Objects.isNull(cityInfo)) {
            log.warn("楼宇:{} {}, 城市({})转换失败, 不进行水位价格组装", buildingRatingEntity.getBuildingNo(),
                    buildingRatingEntity.getBuildingName(), buildingRatingEntity.getMapCity());
            return null;
        }

        // 获取城市水印价格
        ConfigVO configVO = RpcUtils.unBox(feignCmsRpc.getConfig(String.valueOf(cityInfo.getId())));
        if (Objects.isNull(configVO)) {
            log.warn("楼宇:{} {}, 城市:{} {},未查询到水位价配置, 不进行水位价格组装", buildingRatingEntity.getBuildingNo(),
                    buildingRatingEntity.getBuildingName(), cityInfo.getId(), buildingRatingEntity.getMapCity());
            return null;
        }

        log.info("楼宇:{} {}, 城市:{} {}, 水位价配置:{}", buildingRatingEntity.getBuildingNo(),
                buildingRatingEntity.getBuildingName(), cityInfo.getId(), buildingRatingEntity.getMapCity(), configVO);
        return configVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean apply(PriceApplyDto applyDto) {
        // 价格申请前基础校验 并查询基础信息
        PriceApplyBasicVO priceApplyBasic = priceApplyBasicCheck(applyDto);

        // 保存价格申请数据
        boolean result = false;

        // 保存楼盘名称
        BuildingRatingEntity buildingRating = buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getBuildingNo,
                        BuildingRatingEntity::getBuildingName,
                        BuildingRatingEntity::getMapCity)
                .eq(BuildingRatingEntity::getBuildingNo, applyDto.getBuildingNo())
                .orderByDesc(BuildingRatingEntity::getId).last("limit 10").one();

        // 楼宇top值
        String topLevel = buildingRatingService.topLevel(applyDto.getBuildingNo());
        String buildingName = Optional.ofNullable(buildingRating).map(BuildingRatingEntity::getBuildingName).orElse("");

        // 是否核心区域
        boolean coreArea = isCoreArea(buildingRating);

        // 保存价格申请数据
        PriceApplyEntity priceApply = buildPriceApplyEntity(priceApplyBasic, applyDto, topLevel, buildingName, coreArea);
        result |= saveOrUpdate(priceApply);

        // 是否超水位价 超过需要二级审核
        boolean beyondWaterMark = false;
        // 保存价格申请设备数据
        if (CollectionUtil.isNotEmpty(applyDto.getDevices())) {
            List<PriceApplyDeviceDto> devices = applyDto.getDevices();
            if (CollectionUtil.isEmpty(devices)) {
                throw new ServerException("该申请无设备，请选择设备");
            }

            int globalLargeScreenFlag = 0;
            List<PriceApplyDevicePointEntity> priceApplyDevicePointAllEntities = Lists.newArrayListWithExpectedSize(devices.size());
            for (PriceApplyDeviceDto device : devices) {
                PriceApplyDeviceEntity priceApplyDeviceEntity = BeanUtil.copyProperties(device, PriceApplyDeviceEntity.class);
                priceApplyDeviceEntity.setApplyId(priceApply.getId());

                // 设置是否大屏 & 是否核心区域
                priceApplyDeviceEntity.setLargeScreenFlag(getLargeScreenFlag(device, applyDto.getBusinessCode()));
                priceApplyDeviceEntity.setCoreAreaFlag(coreArea ? 1 : 0);
                globalLargeScreenFlag |= priceApplyDeviceEntity.getLargeScreenFlag();

                // 判断是否超水位价
                if (null == priceApply.getSmallWaterMarkPrice() || null == priceApply.getBigWaterMarkPrice()) {
                    beyondWaterMark = true;
                } else if (1 == priceApplyDeviceEntity.getLargeScreenFlag()) {
                    // 判断是否超小屏水位价
                    beyondWaterMark |= BigDecimalUtils.lt(priceApply.getSmallWaterMarkPrice(), priceApplyDeviceEntity.getSignPrice());
                } else {
                    // 判断是否超大屏水位价
                    beyondWaterMark |= BigDecimalUtils.lt(priceApply.getBigWaterMarkPrice(), priceApplyDeviceEntity.getSignPrice());
                }

                result = applyDeviceService.save(priceApplyDeviceEntity);
                if (result) {
                    List<PriceApplyDetailDto.DevicePointDto> points = device.getPoints();
                    if (CollectionUtil.isEmpty(points)) {
                        throw new ServerException("该申请设备未绑定点位，请绑定点位");
                    }
                    if (CollectionUtil.isNotEmpty(points)) {
                        List<PriceApplyDevicePointEntity> priceApplyDevicePointEntities = points.stream()
                                .map(point -> new PriceApplyDevicePointEntity(null, priceApply.getId(),
                                        priceApplyDeviceEntity.getId(), point.getName(), point.getCode()))
                                .toList();
                        priceApplyDevicePointAllEntities.addAll(priceApplyDevicePointEntities);
                    }
                }
            }
            //取出被删除的点位
            List<String> list = priceApplyDevicePointAllEntities.stream().map(PriceApplyDevicePointEntity::getPointCode).toList();
            List<String> points = pointService.lambdaQuery()
                    .in(PointEntity::getCode, list)
                    .list().stream().map(PointEntity::getCode).toList();

            if (CollectionUtil.isEmpty(points)) {
                throw new ServerException("所选点位已删除，请重新选择安装位置");
            }

            List<PriceApplyDevicePointEntity> applyDevicePointEntities = priceApplyDevicePointAllEntities.stream()
                    .filter(point -> points.contains(point.getPointCode())).toList();
            result = priceApplyDevicePointService.saveBatch(applyDevicePointEntities);

            // 更新是否大屏标记
            PriceApplyEntity updatePriceApply = new PriceApplyEntity();
            updatePriceApply.setId(priceApply.getId());
            updatePriceApply.setLargeScreenFlag(globalLargeScreenFlag);
            updatePriceApply.setBuildingNo(priceApply.getBuildingNo());
            // 设置大屏复核系数
            setFinalCoefficient(updatePriceApply);
            updateById(updatePriceApply);
            priceApply.setLargeScreenFlag(globalLargeScreenFlag);
        }

        // 达成意向商机状态更改为方案报价
        if (BusinessChangeStatusEnum.REACHING_INTENTION.getCode().equals(priceApplyBasic.getBusinessOpportunityEntity().getStatus())) {
            BusinessStatusChangeVO businessStatusChangeVO = buildBusinessStatusChangeVO(priceApply);
            kafkaProducerService.sendMessageAfterCommit(TopicConstants.BUSINESS_STATUS_CHANGE, JSONObject.toJSONString(businessStatusChangeVO));
        }

        // 调用审批中心发起审批
        ApprovalDTO approval = submitApproval(priceApply, beyondWaterMark);
        approvalCenterFacade.submitApproval(approval);

        return result;
    }

    /**
     * 设置大屏复核系数
     *
     * @param updatePriceApply 价格申请实体
     */
    private void setFinalCoefficient(PriceApplyEntity updatePriceApply) {
        if (SCREEN_FLAG_SMALL == updatePriceApply.getLargeScreenFlag()) {
            // 小屏没用大屏复核系数
            return;
        }

        // 查询大屏信息
        BuildingScreenEntity screenEntity = buildingScreenService.lambdaQuery()
                .eq(BuildingScreenEntity::getBuildingRatingNo, updatePriceApply.getBuildingNo())
                .last("limit 1")
                .one();
        if (Objects.nonNull(screenEntity)) {
            updatePriceApply.setFinalCoefficient(screenEntity.getFinalCoefficient());
        }
    }

    /**
     * 构建审批条件
     *
     * @param priceApply      价格申请实体
     * @param beyondWaterMark 是否超水位价标识
     * @return ApprovalDTO
     */
    private ApprovalDTO submitApproval(PriceApplyEntity priceApply, boolean beyondWaterMark) {
        ApprovalDTO approval = new ApprovalDTO();
        approval.setApprovalType(ApprovalTypeEnum.PRICE_APPROVAL.getCode());
        approval.setBusinessKey(priceApply.getApplyCode());
        approval.setVersion(priceApply.getVersion());
        // 预留按城市条件
        InnerApproveTemplateParam city = new InnerApproveTemplateParam("city", ApproveFieldTypeEnum.CHARACTER.getCode(), priceApply.getMapCity());
        // 按水位价条件 超水位价或者无水位价 走二级审核
        InnerApproveTemplateParam waterBeyond = new InnerApproveTemplateParam("waterBeyond", ApproveFieldTypeEnum.CHARACTER.getCode(), String.valueOf(beyondWaterMark));
        // 预留大小屏条件
        InnerApproveTemplateParam screenType = new InnerApproveTemplateParam("screenType", ApproveFieldTypeEnum.NUMBER.getCode(), String.valueOf(priceApply.getLargeScreenFlag()));
        List<InnerApproveTemplateParam> innerApproveTemplateParams = new ArrayList<>();
        innerApproveTemplateParams.add(city);
        innerApproveTemplateParams.add(waterBeyond);
        innerApproveTemplateParams.add(screenType);
        approval.setForm(innerApproveTemplateParams);
        return approval;
    }

    /**
     * 构建价格申请entity
     */
    private PriceApplyEntity buildPriceApplyEntity(PriceApplyBasicVO priceApplyBasic,
                                                   PriceApplyDto applyDto,
                                                   String topLevel,
                                                   String buildingName,
                                                   boolean coreArea) {
        ConfigVO cityWaterMarkPrice = priceApplyBasic.getCityWaterMarkPrice();
        PriceApplyEntity priceApply = PriceApplyConvert.INSTANCE.toEntity(applyDto);
        // 先判断是否是审批不通过后的重新提交的
        if (StringUtils.isNotBlank(applyDto.getInstanceCode())) {
            dealRejectApply(applyDto, priceApply);
        }

        // 获取草稿
        PriceApplyEntity draft = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(applyDto.getApplyCode()), PriceApplyEntity::getApplyCode, applyDto.getApplyCode())
                .eq(PriceApplyEntity::getBusinessCode, applyDto.getBusinessCode())
                .eq(PriceApplyEntity::getCreateBy, UserThreadLocal.getUser().getWno())
                .eq(PriceApplyEntity::getStatus, PriceApplyEntity.Status.DRAFT.getCode())
                .last("limit 1")
                .one();

        if (null != draft) {
            // 保留草稿的部分信息
            priceApply.setId(draft.getId());
            priceApply.setDraft(draft.getDraft());
            priceApply.setCreateTime(draft.getCreateTime());
            priceApply.setApplyCode(draft.getApplyCode());
            // 清除草稿的设备点位信息 不然申请的后面流程再保存的话会出现重复
            priceApplyDeviceService.lambdaUpdate()
                    .eq(PriceApplyDeviceEntity::getApplyId, draft.getId())
                    .remove();
            // 删除设备点位信息
            priceApplyDevicePointService.lambdaUpdate()
                    .eq(PriceApplyDevicePointEntity::getApplyId, draft.getId())
                    .remove();
        }
        priceApply.setTopLevel(topLevel);
        priceApply.setBuildingName(buildingName);
        priceApply.setTotalAmount(calculateAmount(applyDto.getDevices()));
        priceApply.setApplyCode(StringUtils.isBlank(priceApply.getApplyCode()) ? getApplyCode() : priceApply.getApplyCode());
        if (null != cityWaterMarkPrice) {
            // 设置水位价 把大屏水位价和小屏水位价保存
            String smallScreen = cityWaterMarkPrice.getValue();
            String bigScreen = coreArea ? cityWaterMarkPrice.getExt1() : cityWaterMarkPrice.getExt2();
            // 大屏
            priceApply.setBigWaterMarkPrice(StringUtils.isBlank(bigScreen) ? BigDecimal.ZERO : new BigDecimal(bigScreen));
            // 小屏
            priceApply.setSmallWaterMarkPrice(StringUtils.isBlank(smallScreen) ? BigDecimal.ZERO : new BigDecimal(smallScreen));
        }

        // 获取评级版本并保存
        buildRatingInfo(priceApply, priceApply.getBuildingNo());

        // 保存价格申请版本
        priceApply.setVersion(codeGenerator.generateRatingVersion());
        return priceApply;
    }

    /**
     * 审批不通过后需要保存价格申请快照，并且重提的时候申请单号不变（instanceCode不变）
     */
    private void dealRejectApply(PriceApplyDto applyDto, PriceApplyEntity priceApply) {
        PriceApplyEntity rejectedApply = this.lambdaQuery()
                .eq(PriceApplyEntity::getInstanceCode, applyDto.getInstanceCode())
                .eq(PriceApplyEntity::getStatus, PriceApplyEntity.Status.REJECTED.getCode())
                .last("limit 1")
                .one();
        if (null != rejectedApply) {
            // 重新提交  实例编码和申请编码以及创建时间等都保留
            priceApply.setId(rejectedApply.getId());
            priceApply.setCreateTime(rejectedApply.getCreateTime());
            // applyCode是bizId，如果这个不变，那边审批中心那边的bizId不变   instanceCode会变
            priceApply.setApplyCode(rejectedApply.getApplyCode());

            // 保存上一次的提交记录到snapshot表
            String rejectApplyStr = JSON.toJSONString(rejectedApply, JSONWriter.Feature.WriteMapNullValue);
            // 获取设备点位信息 并存快照
            List<PriceApplyDeviceEntity> devices = priceApplyDeviceService.lambdaQuery()
                    .eq(PriceApplyDeviceEntity::getApplyId, rejectedApply.getId())
                    .list();
            List<PriceApplyDevicePointEntity> devicePoints = priceApplyDevicePointService.lambdaQuery()
                    .eq(PriceApplyDevicePointEntity::getApplyId, rejectedApply.getId())
                    .list();
            String deviceJson = JSON.toJSONString(devices, JSONWriter.Feature.WriteMapNullValue);
            String devicePointJson = JSON.toJSONString(devicePoints, JSONWriter.Feature.WriteMapNullValue);
            BuildingSnapshotEntity buildingSnapshotEntity = new BuildingSnapshotEntity();
            // 存申请单编号
            buildingSnapshotEntity.setBuildingRatingNo(applyDto.getApplyCode());
            buildingSnapshotEntity.setMetaSnapshot(rejectApplyStr);
            buildingSnapshotEntity.setRatingSnapshot(deviceJson);
            buildingSnapshotEntity.setDetailsSnapshot(devicePointJson);
            buildingSnapshotEntity.setRatingVersion(rejectedApply.getVersion());
            buildingSnapshotEntity.setType(BuildingSnapshotEntity.Type.PRICE_APPLY.getValue());
            buildingSnapshotService.save(buildingSnapshotEntity);

            // 清除设备点位信息 不然申请的后面流程再保存的话会出现重复
            priceApplyDeviceService.lambdaUpdate()
                    .eq(PriceApplyDeviceEntity::getApplyId, rejectedApply.getId())
                    .remove();
            // 删除设备点位信息
            priceApplyDevicePointService.lambdaUpdate()
                    .eq(PriceApplyDevicePointEntity::getApplyId, rejectedApply.getId())
                    .remove();
        }
    }

    /**
     * 价格申请保存楼宇相关的信息
     */
    private void buildRatingInfo(PriceApplyEntity priceApply, String buildingNo) {
        if (StringUtils.isBlank(buildingNo)) {
            return;
        }

        BuildingRatingEntity buildingRatingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo)
                .orderByDesc(BuildingRatingEntity::getId)
                .last("limit 1")
                .one();

        if (null != buildingRatingEntity) {
            priceApply.setRatingVersion(buildingRatingEntity.getRatingVersion());
            // 获取评级等级并保存  有复核取复核，无复审取评级等级
            String level = StringUtils.isBlank(buildingRatingEntity.getProjectReviewLevel()) ? buildingRatingEntity.getProjectLevel() : buildingRatingEntity.getProjectReviewLevel();
            priceApply.setProjectLevel(level);
            // 保存当前评级的省市和详细地址以及楼宇类型  后续列表上展示使用
            priceApply.setMapProvince(buildingRatingEntity.getMapProvince());
            priceApply.setMapCity(buildingRatingEntity.getMapCity());
            priceApply.setMapRegion(buildingRatingEntity.getMapRegion());
            priceApply.setMapAddress(buildingRatingEntity.getMapAddress());
            priceApply.setBuildingType(buildingRatingEntity.getBuildingType());
            priceApply.setBuildingName(buildingRatingEntity.getBuildingName());
        }

        // 补充地理等级信息
        BuildingDetailsEntity buildingDetailsEntity = buildingDetailsService.lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, buildingNo)
                .eq(BuildingDetailsEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .orderByDesc(BuildingDetailsEntity::getId)
                .last("limit 1")
                .one();
        if (null != buildingDetailsEntity) {
            priceApply.setLocationName(buildingDetailsEntity.getLocationName());
        }
    }

    /**
     * 构建商机状态变更信息
     */
    private BusinessStatusChangeVO buildBusinessStatusChangeVO(PriceApplyEntity priceApply) {
        CachedUser user = UserThreadLocal.getUser();
        BusinessStatusChangeVO businessStatusChangeVO = new BusinessStatusChangeVO();
        businessStatusChangeVO.setStatus(BusinessChangeStatusEnum.PROPOSAL_QUOTATION.getCode());
        businessStatusChangeVO.setBusinessCode(priceApply.getBusinessCode());
        businessStatusChangeVO.setOperatorId(user.getId());
        businessStatusChangeVO.setOperatorWno(user.getWno());
        businessStatusChangeVO.setOperatorName(user.getName());
        return businessStatusChangeVO;
    }

    /**
     * 价格申请基础数据校验
     */
    private PriceApplyBasicVO priceApplyBasicCheck(PriceApplyDto applyDto) {
        // 校验楼宇基因信息是否填写完整
        List<String> genChecks = checkBuildingGene(applyDto.getBuildingNo());
        if (null == genChecks) {
            throw new ServerException("未填写楼宇基因信息");
        }
        if (CollUtil.isNotEmpty(genChecks)) {
            throw new ServerException("请填写楼宇基因信息：" + String.join(",", genChecks));
        }

        PriceApplyBasicVO priceApplyBasic = new PriceApplyBasicVO();
        // 校验价格分组是否重复 若重复则提示：签约单价相同的点位请放在同一个价格分组中
        List<PriceApplyDeviceDto> deviceList = applyDto.getDevices();
        if (CollectionUtil.isEmpty(deviceList)) {
            throw new ServerException("该申请无设备，请选择设备");
        }
        Set<String> priceKeys = new HashSet<>();
        for (PriceApplyDeviceDto device : deviceList) {
            BigDecimal incentivePrice = Optional.ofNullable(device.getIncentivePrice()).orElse(BigDecimal.ZERO);
            BigDecimal signPrice = Optional.ofNullable(device.getSignPrice()).orElse(BigDecimal.ZERO);
            // 保持统一的小数位数  两位小数四射五入
            incentivePrice = incentivePrice.setScale(2, RoundingMode.HALF_UP);
            signPrice = signPrice.setScale(2, RoundingMode.HALF_UP);

            String key = signPrice + "|" + incentivePrice;
            if (!priceKeys.add(key)) {
                throw new ServerException("签约单价相同的点位请放在同一个价格分组中");
            }
        }
        // 客户验证审批
        BuildingRatingEntity buildingRatingExist = buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getId,
                        BuildingRatingEntity::getSmallScreenRatingFlag,
                        BuildingRatingEntity::getLargeScreenRatingFlag,
                        BuildingRatingEntity::getBuildingName,
                        BuildingRatingEntity::getBuildingNo,
                        BuildingRatingEntity::getMapCity)
                .eq(BuildingRatingEntity::getBuildingNo, applyDto.getBuildingNo())
                .eq(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.AUDITED.value)
                .one();
        if (Objects.isNull(buildingRatingExist)) {
            throw new ServerException("该楼宇申请尚未审核通过，不能申请价格");
        }

        // 校验商机是否还属于用户
        BusinessOpportunityEntity businessOpportunityEntity = businessOpportunityService.lambdaQuery()
                .eq(BusinessOpportunityEntity::getCode, applyDto.getBusinessCode())
                .eq(BusinessOpportunityEntity::getSubmitUser, UserThreadLocal.getUser().getWno())
                .last("limit 1")
                .one();
        if (Objects.isNull(businessOpportunityEntity)) {
            throw new ServerException("该商机已转移，不能申请价格");
        }
        priceApplyBasic.setBusinessOpportunityEntity(businessOpportunityEntity);

        // 校验商机状态
        if (!BusinessChangeStatusEnum.REACHING_INTENTION.getCode().equals(businessOpportunityEntity.getStatus())
                && !BusinessChangeStatusEnum.PROPOSAL_QUOTATION.getCode().equals(businessOpportunityEntity.getStatus())) {
            throw new ServerException(String.format("该商机状态为%s，不能申请价格",
                    BusinessChangeStatusEnum.getByCode(businessOpportunityEntity.getStatus())));
        }

        // 检查是否可以申请
        Set<Integer> notAllowedStatus = Sets.newHashSet(PriceApplyEntity.Status.PENDING.getCode());
        boolean exists = lambdaQuery()
                .eq(PriceApplyEntity::getBusinessCode, applyDto.getBusinessCode())
                .in(PriceApplyEntity::getStatus, notAllowedStatus).exists();
        if (exists) {
            throw new ServerException("该商机已申请过价格，请勿重复申请");
        }

        List<Integer> list = completeRatingService.lambdaQuery()
                .select(CompleteRatingEntity::getBuildingRatingNo, CompleteRatingEntity::getStatus, CompleteRatingEntity::getLargeScreenFlag)
                .eq(CompleteRatingEntity::getBuildingRatingNo, applyDto.getBuildingNo())
                .eq(CompleteRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.getValue())
                .list().stream().map(CompleteRatingEntity::getLargeScreenFlag).toList();
        log.info("待审数据list:{}", list);
        for (PriceApplyDeviceDto device : applyDto.getDevices()) {
            Integer largeScreenFlag = getLargeScreenFlag(device, applyDto.getBusinessCode());
            log.info("是否大屏largeScreenFlag:{}", largeScreenFlag);

            if ((!largeScreenFlag.equals(SCREEN_FLAG_SMALL)
                    && buildingRatingExist.getLargeScreenRatingFlag().equals(0))
                    || (!largeScreenFlag.equals(SCREEN_FLAG_SMALL) && list.contains(2)) || (!largeScreenFlag.equals(SCREEN_FLAG_SMALL) && list.contains(3))) {
                throw new ServerException("包含大屏点位,请在我的楼字-完善评级完成大屏评级(需要完善评级审核通过)!");
            }
            if ((largeScreenFlag.equals(SCREEN_FLAG_SMALL)
                    && buildingRatingExist.getSmallScreenRatingFlag().equals(0))
                    || (largeScreenFlag.equals(SCREEN_FLAG_SMALL) && list.contains(1))) {
                throw new ServerException("缺少评级信息，请在我的楼宇-完善评级完成楼宇评级（需要完善评级审核通过）!");
            }
        }
        // 获取大小屏水位价
        ConfigVO cityWaterMarkPrice = getCityWaterMarkPrice(buildingRatingExist);
        priceApplyBasic.setCityWaterMarkPrice(cityWaterMarkPrice);

        return priceApplyBasic;
    }

    /**
     * 检查是否大屏
     */
    private Integer getLargeScreenFlag(PriceApplyDeviceDto device, String businessCode) {
        int largeScreenFlag = SCREEN_FLAG_SMALL;
        if (Objects.isNull(device) || CollectionUtil.isEmpty(device.getPoints())) {
            return largeScreenFlag;
        }

        Set<String> pointCodes = device.getPoints().stream().map(PriceApplyDetailDto.DevicePointDto::getCode)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(pointCodes)) {
            return largeScreenFlag;
        }

        List<String> largeScreenDictCodes = largeScreenProperties.getLargeDictKey();
        if (CollectionUtil.isEmpty(largeScreenDictCodes)) {
            return largeScreenFlag;
        }

        Map<String, List<String>> pointSizeMapping = pointService.lambdaQuery()
                .select(PointEntity::getCode, PointEntity::getDeviceSize)
                .eq(PointEntity::getBusinessCode, businessCode)
                .in(PointEntity::getCode, pointCodes)
                .list().stream()
                .collect(Collectors.groupingBy(PointEntity::getDeviceSize, Collectors.mapping(PointEntity::getCode, Collectors.toList())));

        // 如果包含大屏尺寸字典编码, 则标记为大屏
        for (String code : largeScreenDictCodes) {
            List<String> largeScreenPointCodes = pointSizeMapping.remove(code);
            if (CollectionUtil.isNotEmpty(largeScreenPointCodes)) {
                largeScreenFlag = SCREEN_FLAG_LARGE;
            }
        }

        // 如果有大屏尺寸, 还有剩余的点位是其它尺寸,则标记为同时有大屏和小屏
        if (Objects.equals(largeScreenFlag, SCREEN_FLAG_LARGE) && CollectionUtil.isNotEmpty(pointSizeMapping)) {
            largeScreenFlag = SCREEN_FLAG_BOTH;
        }

        return largeScreenFlag;
    }


    /**
     * 检查楼宇是否核心区域
     */
    @Override
    public boolean isCoreArea(BuildingRatingEntity buildingRating) {
        if (Objects.isNull(buildingRating) || StringUtils.isBlank(buildingRating.getBuildingNo())) {
            return false;
        }

        Map<Long, String> buildingParameterMap = buildingParameterService.lambdaQuery()
                .select(BuildingParameterEntity::getId, BuildingParameterEntity::getParameterName)
                .eq(BuildingParameterEntity::getParameterCode, "buildingLocation")
                .list().stream()
                .collect(Collectors.toMap(BuildingParameterEntity::getId, BuildingParameterEntity::getParameterName));

        BuildingDetailsEntity buildingDetails = buildingDetailsService.lambdaQuery()
                .select(BuildingDetailsEntity::getBuildingNo, BuildingDetailsEntity::getBuildingLocation)
                .eq(BuildingDetailsEntity::getBuildingNo, buildingRating.getBuildingNo())
                .last("LIMIT 1")
                .one();
        String locationName = Optional.ofNullable(buildingDetails)
                .map(BuildingDetailsEntity::getBuildingLocation)
                .map(buildingParameterMap::get)
                .orElse(null);

        return "城市核心区".equals(locationName);
    }


    @Override
    @Transactional
    public boolean approve(PriceApplyApproveDto approveDto) {
        PriceApplyEntity priceApply = Optional.ofNullable(getById(approveDto.getApplyId()))
                .orElseThrow(() -> new ServerException("价格申请不存在"));

        List<String> priceApplyAuditors = largeScreenProperties.getPriceApplyAuditors();
        // 检查是不是自己审批
        if (!priceApplyAuditors.contains(SecurityUser.getUserCode()) && StringUtils.equalsIgnoreCase(priceApply.getCreateBy(), SecurityUser.getUserCode())) {
            throw new ServerException("不能审批自己的价格申请");
        }

        boolean endFlag = priceApplyApprove(approveDto);

        if (!endFlag) {
            return true;
        }


        // 审批通过，发送点位消息
        Integer status = approveDto.getStatus();
        if (PriceApplyEntity.Status.PASSED.getCode().equals(status)) {
            try {
                sendPointMessage(approveDto.getApplyId());
            } catch (Exception e) {
                log.error("审批通过消息发送失败");
            }
        }

        // 价格申请拒绝商机状态不应回退
        /*if (status != null && status == PriceApplyEntity.Status.REJECTED.getCode()) {
            try {
                PriceApplyEntity priceApplyEntity = this.getOne(Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(PriceApplyEntity::getId, approveDto.getApplyId()));
                //发商机状态更改状态
                BusinessStatusChangeVO businessStatusChangeVO = new BusinessStatusChangeVO();
                businessStatusChangeVO.setStatus(BusinessChangeStatusEnum.REACHING_INTENTION.getCode());
                businessStatusChangeVO.setBusinessCode(priceApplyEntity.getBusinessCode());
                kafkaProducerService.sendMessage("business-status-change", JSONObject.toJSONString(businessStatusChangeVO));
            } catch (Exception e) {
                log.error("审批已驳回");
            }
        }*/

        return lambdaUpdate()
                .set(PriceApplyEntity::getStatus, approveDto.getStatus())
                .set(PriceApplyEntity::getApproveRemark, approveDto.getRemark())
                .set(PriceApplyEntity::getApproveBy, SecurityUser.getUserCode())
                .set(PriceApplyEntity::getApproveTime, LocalDateTime.now())
                .eq(PriceApplyEntity::getId, approveDto.getApplyId())
                .update();
    }

    public boolean priceApplyApprove(PriceApplyApproveDto approveDto) {
        PriceApplyEntity priceApply = this.getById(approveDto.getApplyId());

        ScreenApproveRecordEntity screenApproveRecord = screenApproveRecordService.lambdaQuery()
                .eq(ScreenApproveRecordEntity::getNaturalKey, priceApply.getApplyCode())
                .eq(ScreenApproveRecordEntity::getSceneType, SceneTypeEnum.PRICE_APPLY.getType())
                .eq(ScreenApproveRecordEntity::getDeleteFlag, 0)
                .in(ScreenApproveRecordEntity::getApproveLevel, 1, 2)
                .eq(ScreenApproveRecordEntity::getStatus, PriceApplyEntity.Status.PENDING.getCode())
                .eq(ScreenApproveRecordEntity::getApproveUser, SecurityUser.getUserCode())
                .one();

        if (Objects.isNull(screenApproveRecord)) {
            throw new ServerException("未找到审批节点");
        }

        screenApproveRecord.setApproveTime(LocalDateTime.now());
        screenApproveRecord.setStatus(approveDto.getStatus());
        screenApproveRecord.setRemark(approveDto.getRemark());
        screenApproveRecordService.updateById(screenApproveRecord);

        if (!approveDto.getStatus().equals(PriceApplyEntity.Status.PASSED.getCode())) {
            return true;
        }

        if (screenApproveRecord.getApproveLevel() == 1) {
            List<String> priceApplyAuditors = largeScreenProperties.getPriceApplyAuditors();
            String approveUser = priceApplyAuditors.get(priceApplyAuditors.size() - 1);
            ScreenApproveRecordEntity screenApproveRecordEntity = new ScreenApproveRecordEntity();
            screenApproveRecordEntity.setApproveLevel(2);
            screenApproveRecordEntity.setApproveUser(approveUser);
            screenApproveRecordEntity.setNaturalKey(priceApply.getApplyCode());
            screenApproveRecordEntity.setStatus(PriceApplyEntity.Status.PENDING.getCode());
            screenApproveRecordEntity.setSceneType(SceneTypeEnum.PRICE_APPLY.getType());
            screenApproveRecordService.save(screenApproveRecordEntity);

            this.lambdaUpdate()
                    .set(PriceApplyEntity::getApproveBy, approveUser)
                    .eq(PriceApplyEntity::getId, priceApply.getId())
                    .update();

            BuildingRatingEntity buildingRatingEntity = buildingRatingService.lambdaQuery()
                    .eq(BuildingRatingEntity::getBuildingNo, priceApply.getBuildingNo())
                    .one();

            // 发消息
            Map<String, SysUserDto> nameMaps = sysUserService.getUserNameMaps(Arrays.asList(priceApply.getCreateBy()));
            SysUserDto submitUserDto = nameMaps.get(priceApply.getCreateBy());
            messageRecordService.sendPriceApplyForMsg(approveUser, submitUserDto.getRealName()
                    , buildingRatingEntity.getBuildingName(), buildingRatingEntity.getMapCity(), priceApply.getId()
                            .toString());

            return false;

        }

        return true;
    }

    /**
     * 发送消息
     *
     * @param priceApplyId
     */
    public void sendPointMessage(Integer priceApplyId) {
        PriceApplyEntity priceApplyEntity = this.getById(priceApplyId);
        List<PriceApplyDevicePointEntity> deviceList = priceApplyDevicePointService.lambdaQuery()
                .eq(PriceApplyDevicePointEntity::getApplyId, priceApplyId).list();

        if (CollectionUtils.isEmpty(deviceList)) {
            log.warn("审批通过，但是点位为空。priceApplyId:{}", priceApplyId);
            return;
        }
        List<String> points = deviceList.stream().map(PriceApplyDevicePointEntity::getPointCode)
                .collect(Collectors.toList());
        JSONObject root = new JSONObject();
        root.put("status", "0025-4");

        JSONArray projects = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("code", priceApplyEntity.getBusinessCode());
        item.put("name", priceApplyEntity.getBuildingName());
        item.put("pointCodes", JSONArray.copyOf(points));
        projects.add(item);

        root.put("projects", projects);
        kafkaProducer.sendMessage(approvalMessageTopic, root.toString());

    }

    @Override
    public PageResult<PriceApplyListCmsDto> listPriceApplyCms(PriceApplyQueryCmsReq req) {

        Page<PriceApplyEntity> page = new Page<>(req.getPage(), req.getLimit());
        LambdaQueryWrapper<PriceApplyEntity> queryWrapper = new QueryWrapper<PriceApplyEntity>().lambda()
                .eq(PriceApplyEntity::getStatus, 2)
                .eq(StringUtils.isNotBlank(req.getBuildingNo()), PriceApplyEntity::getBuildingNo, req.getBuildingNo())
                .eq(StringUtils.isNotBlank(req.getBusinessCode()), PriceApplyEntity::getBusinessCode, req.getBusinessCode())
                .like(StringUtils.isNotBlank(req.getSearch()), PriceApplyEntity::getBuildingName, req.getSearch())
                .orderByDesc(PriceApplyEntity::getId);

        Page<PriceApplyEntity> entityPage = this.page(page, queryWrapper);
        List<PriceApplyEntity> records = entityPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageResult<>(Collections.emptyList(), page.getTotal());
        }

        // 查询设备信息
        List<Integer> priceApplyIds = records.stream().map(PriceApplyEntity::getId).collect(Collectors.toList());
        Map<Integer, List<PriceApplyDeviceEntity>> priceApplyIdMap = applyDeviceService.findByPriceApplyIdMap(priceApplyIds);

        // 查询申请单下的点位
        String mapKeyTpl = "%s|%s|%s";
        List<PriceApplyDevicePointEntity> list = priceApplyDevicePointService.list(Wrappers.<PriceApplyDevicePointEntity>lambdaQuery()
                .in(PriceApplyDevicePointEntity::getApplyId, priceApplyIds));
        Map<String, PriceApplyDevicePointEntity> priceApplyDevicePointEntityMap = list.stream()
                .collect(Collectors.toMap(item -> String.format(mapKeyTpl, item.getApplyId(), item.getPriceApplyDeviceId(), item.getPointCode()), Function.identity(), (o, n) -> n));

        // 老数据点位控制
        Set<String> oldPriceApplyDeviceSet = list.stream()
                .map(s -> s.getApplyId().toString() + s.getPriceApplyDeviceId()).collect(Collectors.toSet());

        // 数据转换
        List<PriceApplyListCmsDto> priceApplies = PriceApplyConvert.INSTANCE.toCmsPriceApplyList(records);

        for (PriceApplyListCmsDto priceApply : priceApplies) {
            List<PriceApplyDeviceEntity> devices = priceApplyIdMap.get(priceApply.getApplyId());
            if (CollUtil.isEmpty(devices)) continue;
            priceApply.setQuantity(devices.stream().map(PriceApplyDeviceEntity::getQuantity)
                    .filter(Objects::nonNull).reduce(0, Integer::sum));

            // 查找基础点位信息
            List<CodeNameVO> points = pointService.listPointToContract(priceApply.getBuildingNo(), req.getBusinessCode());

            List<PriceApplyDetailDto.DeviceDetailDto> deviceDetailDtos = new ArrayList<>();
            for (PriceApplyDeviceEntity device : devices) {
                PriceApplyDetailDto.DeviceDetailDto deviceDetailDto = PriceApplyDeviceConvert.INSTANCE.toDeviceDetailDto(device);
                List<PriceApplyDetailDto.DevicePointDto> collect = Collections.emptyList();
                if (CollUtil.isNotEmpty(points)) {
                    collect = points.stream()
                            .map(point -> new PriceApplyDetailDto.DevicePointDto(point.getCode(), point.getName(), false))
                            .peek(point -> point.setChecked(priceApplyDevicePointEntityMap.containsKey(String.format(mapKeyTpl, priceApply.getApplyId(), device.getId(), point.getCode()))))
                            .collect(Collectors.toList());
                }
                deviceDetailDto.setPoints(oldPriceApplyDeviceSet.contains(priceApply.getApplyId()
                        .toString() + device.getId().toString()) ? collect : Collections.emptyList());
                deviceDetailDtos.add(deviceDetailDto);
            }
            priceApply.setDevices(deviceDetailDtos);
        }
        return new PageResult<>(priceApplies, page.getTotal());
    }

    @Override
    public BigDecimal calculateTotalAmount(Collection<String> applyCodes) {
        if (CollectionUtil.isEmpty(applyCodes)) return BigDecimal.ZERO;
        Set<String> codes = applyCodes.stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(codes)) return BigDecimal.ZERO;

        // 查询所有价格申请
        List<PriceApplyEntity> entities = lambdaQuery()
                .select(PriceApplyEntity::getApplyCode, PriceApplyEntity::getContractDuration, PriceApplyEntity::getTotalAmount)
                .in(PriceApplyEntity::getApplyCode, codes)
                .list();
        if (CollectionUtil.isEmpty(entities)) return BigDecimal.ZERO;

        // 计算总金额
        return entities.stream()
                .filter(apply -> Objects.nonNull(apply.getContractDuration()))
                .filter(apply -> Objects.nonNull(apply.getTotalAmount()))
                .map(apply -> apply.getContractDuration().multiply(apply.getTotalAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<CodeNameVO> getPriceApplyDevicePointList(String buildingNo, String businessCode) {

        ResultTemplate<List<PointDetail>> listResultTemplate = feignSspRpc.pointList(buildingNo);
        List<PointEntity> list = pointService.list(Wrappers.<PointEntity>lambdaQuery()
                .eq(PointEntity::getBusinessCode, businessCode));
        Set<String> points = list.stream().map(PointEntity::getCode).collect(Collectors.toSet());
        List<PointDetail> data = listResultTemplate.getData();
        List<PointDetail> pointDetailList = data.stream()
                .filter(pointDetail -> points.contains(pointDetail.getPointCode())).collect(Collectors.toList());

        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(pointDetailList)) {

            return pointDetailList.stream().map(e -> {
                // 1栋 1单元 1层 大堂 1号电梯厅 点位1
                StringJoiner joiner = new StringJoiner("_");
                String v = joiner.add(e.getBuildingName())
                        .add(e.getUnitName())
                        .add(e.getFloorName())
                        .add(e.getWaitingHallTypeName())
                        .add(e.getWaitingHallName())
                        .add(e.getPointCode()).toString();
                return CodeNameVO.builder().name(v).code(e.getPointCode()).build();
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public ProjectPointVO getInstallationInformation(Integer priceApplyDeviceId, String version, String applyCode) {
        PriceApplyDeviceEntity priceApplyDeviceEntity = priceApplyDeviceService.getById(priceApplyDeviceId);
        if (null == priceApplyDeviceEntity) {
            // 从快照拿
            if (StringUtils.isBlank(version) && StringUtils.isBlank(applyCode)) {
                return new ProjectPointVO();
            }
            BuildingSnapshotEntity snapshot = buildingSnapshotService.lambdaQuery()
                    .eq(BuildingSnapshotEntity::getBuildingRatingNo, applyCode)
                    .eq(BuildingSnapshotEntity::getRatingVersion, version)
                    .eq(BuildingSnapshotEntity::getType, BuildingSnapshotEntity.Type.PRICE_APPLY.getValue())
                    .last("limit 1")
                    .one();
            if (null == snapshot || StringUtils.isBlank(snapshot.getDetailsSnapshot()) || StringUtils.isBlank(snapshot.getMetaSnapshot())) {
                return new ProjectPointVO();
            }
            // 解析快照 获取pointCodes
            String detailsSnapshot = snapshot.getDetailsSnapshot();
            List<PriceApplyDevicePointEntity> pointEntities = JSON.parseArray(detailsSnapshot, PriceApplyDevicePointEntity.class);
            PriceApplyEntity priceApply = JSON.parseObject(snapshot.getMetaSnapshot(), PriceApplyEntity.class);
            List<String> points = pointEntities.stream().map(PriceApplyDevicePointEntity::getPointCode).toList();
            if (CollUtil.isEmpty(points)) {
                return new ProjectPointVO();
            }
            ProjectPointVO projectPointVO = pointService.listPriceApplyPoint(points, priceApply.getBuildingNo());
            String topLevel = buildingRatingService.topLevel(priceApply.getBuildingNo());
            projectPointVO.setTopLevel(topLevel);
            return projectPointVO;
        }
        PriceApplyEntity priceApply = this.getById(priceApplyDeviceEntity.getApplyId());
        List<Integer> priceApplyDeviceIds = new ArrayList<>();
        priceApplyDeviceIds.add(priceApplyDeviceId);
        List<String> points = pointService.getWaitingHallPointInformation(priceApplyDeviceIds);
        if (CollUtil.isEmpty(points)) {
            return new ProjectPointVO();
        }
        ProjectPointVO projectPointVO = pointService.listPriceApplyPoint(points, priceApply.getBuildingNo());
        String topLevel = buildingRatingService.topLevel(priceApply.getBuildingNo());
        projectPointVO.setTopLevel(topLevel);
        return projectPointVO;
    }

    @Override
    public ProjectPointVO pointByApplyNumber(PointByApplyNumberReq req) {
        // 按价格申请单
        if (StringUtils.isNotBlank(req.getApplyNumber())) {
            PriceApplyEntity priceApply = this.getOne(Wrappers.<PriceApplyEntity>lambdaQuery()
                    .eq(PriceApplyEntity::getApplyCode, req.getApplyNumber()));
            if (Objects.isNull(priceApply)) return new ProjectPointVO();
            List<String> pointByApplyNumber = applyDeviceService.getPointByApplyNumber(req.getApplyNumber());
            return pointService.listPriceApplyPoint(pointByApplyNumber, priceApply.getBuildingNo());
        }

        // 按楼宇编码
        if (StringUtils.isNotBlank(req.getBuildingNo())) {
            ProjectPointVO projectPointVO = pointService.listPriceApplyPoint(req.getCodes(), req.getBuildingNo());
            BuildingRatingEntity buildingRating = buildingRatingService.lambdaQuery()
                    .select(BuildingRatingEntity::getBuildingName)
                    .eq(BuildingRatingEntity::getBuildingNo, req.getBuildingNo())
                    .one();
            projectPointVO.setProjectName(Objects.nonNull(buildingRating) ? buildingRating.getBuildingName() : "");
            return projectPointVO;
        }

        // 商机编码
        if (StringUtils.isNotBlank(req.getBusinessCode())) {
            BusinessOpportunityEntity businessOpportunity = businessOpportunityService.lambdaQuery()
                    .select(BusinessOpportunityEntity::getBuildingNo)
                    .eq(BusinessOpportunityEntity::getCode, req.getBusinessCode())
                    .last("LIMIT 1").one();
            if (Objects.isNull(businessOpportunity)) return new ProjectPointVO();
            return pointService.listPriceApplyPoint(req.getCodes(), businessOpportunity.getBuildingNo());
        }
        return new ProjectPointVO();
    }

    @Override
    public List<PointDetail> pointByBusinessCode(String code) {

        if (StringUtils.isBlank(code)) {
            return Collections.emptyList();
        }

        BusinessOpportunityEntity businessOpportunity = businessOpportunityService.lambdaQuery()
                .select(BusinessOpportunityEntity::getBuildingNo)
                .eq(BusinessOpportunityEntity::getCode, code)
                .last("LIMIT 1").one();

        if (Objects.isNull(businessOpportunity)) {
            return Collections.emptyList();
        }

        ResultTemplate<List<PointDetail>> listResultTemplate = feignSspRpc.pointList(businessOpportunity.getBuildingNo());
        log.info("ssp返回的点位数据listResultTemplate:{}", listResultTemplate);
        if (CollectionUtil.isEmpty(listResultTemplate.getData())) {
            return Collections.emptyList();
        }

        List<String> pointCodes = pointService.list(Wrappers.<PointEntity>lambdaQuery()
                .eq(PointEntity::getBusinessCode, code)).stream().map(PointEntity::getCode).toList();

        return listResultTemplate.getData().stream()
                .filter(pointDetail -> pointCodes.contains(pointDetail.getPointCode())).toList();
    }

    @Override
    public List<PointDetail> sspPointByBusinessCode(String code) {

        if (StringUtils.isBlank(code)) {
            return Collections.emptyList();
        }

        BusinessOpportunityEntity businessOpportunity = businessOpportunityService.lambdaQuery()
                .select(BusinessOpportunityEntity::getBuildingNo)
                .eq(BusinessOpportunityEntity::getCode, code)
                .last("LIMIT 1").one();

        if (Objects.isNull(businessOpportunity)) {
            return Collections.emptyList();
        }

        ResultTemplate<List<PointDetail>> listResultTemplate = feignSspRpc.pointList(businessOpportunity.getBuildingNo());
        log.info("ssp返回的点位数据listResultTemplate:{}", listResultTemplate);
        if (CollectionUtil.isEmpty(listResultTemplate.getData())) {
            return Collections.emptyList();
        }

        return listResultTemplate.getData();
    }

    /**
     * 计算合同总金额
     */
    private BigDecimal calculateAmount(List<PriceApplyDeviceDto> devices) {
        if (CollectionUtil.isEmpty(devices)) {
            return BigDecimal.ZERO;
        }

        return devices.stream()
                .filter(device -> Objects.nonNull(device.getSignPrice()))
                .filter(device -> Objects.nonNull(device.getQuantity()))
                .map(device -> device.getSignPrice().multiply(new BigDecimal(device.getQuantity())))
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
    }

    @Override
    public boolean checkBuildingStatus(String buildingNo) {
        BuildingRatingEntity buildingRating = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo)
                .eq(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.AUDITED.getValue())
                .one();
        return Objects.nonNull(buildingRating);
    }

    @Override
    public List<String> checkBuildingGene(String buildingNo) {
        BuildingGeneVO geneVO = buildingGeneService.getBuildingGeneByNo(buildingNo);
        if (geneVO == null) {
            return null;
        }

        BuildingGeneDTO geneDTO = BeanUtil.copyProperties(geneVO, BuildingGeneDTO.class);
        List<String> msg = buildingGeneService.validateGene(geneDTO);
        return msg;
    }

    @Override
    public boolean updateDeviceLargeScreenFlag(Integer deviceId, Integer largeScreen, Integer coreArea) {
        if (Objects.isNull(deviceId) || deviceId <= 0) {
            return false;
        }

        if (Objects.isNull(largeScreen) && Objects.isNull(coreArea)) {
            return false;
        }

        // 已存在的价格申请分组
        PriceApplyDeviceEntity priceApplyDevice = priceApplyDeviceService.getById(deviceId);
        if (Objects.isNull(priceApplyDevice)) {
            return false;
        }

        // 更新大屏标记
        boolean results = priceApplyDeviceService.lambdaUpdate()
                .eq(PriceApplyDeviceEntity::getId, deviceId)
                .set(Objects.nonNull(largeScreen), PriceApplyDeviceEntity::getLargeScreenFlag, largeScreen)
                .set(Objects.nonNull(coreArea), PriceApplyDeviceEntity::getCoreAreaFlag, coreArea)
                .update();

        // 查询此价格申请下所有大屏标记
        List<Integer> largeScreenFlags = priceApplyDeviceService.lambdaQuery()
                .select(PriceApplyDeviceEntity::getId, PriceApplyDeviceEntity::getApplyId, PriceApplyDeviceEntity::getLargeScreenFlag)
                .eq(PriceApplyDeviceEntity::getApplyId, priceApplyDevice.getApplyId())
                .list().stream()
                .map(PriceApplyDeviceEntity::getLargeScreenFlag).filter(Objects::nonNull).toList();
        if (CollectionUtil.isEmpty(largeScreenFlags)) {
            return results;
        }

        // 计算价格申请的大屏标记
        int globalLargeScreenFlag = 0;
        for (Integer flag : largeScreenFlags) {
            globalLargeScreenFlag |= flag;
        }

        // 更新是否大屏标记
        PriceApplyEntity updatePriceApply = new PriceApplyEntity();
        updatePriceApply.setId(priceApplyDevice.getApplyId());
        updatePriceApply.setLargeScreenFlag(globalLargeScreenFlag);
        results |= updateById(updatePriceApply);

        return results;
    }


    @Override
    public boolean batchUpdateLargeScreenFlag() {
        List<String> largeScreenDictCodes = largeScreenProperties.getLargeDictKey();
        log.info("配置的大屏点位编码: {}", StringUtils.join(largeScreenDictCodes, ", "));
        if (CollectionUtil.isEmpty(largeScreenDictCodes)) {
            return false;
        }

        // 查询所有大屏点位
        Set<String> largeScreenPointCodes = pointService.lambdaQuery()
                .select(PointEntity::getCode, PointEntity::getDeviceSize)
                .in(PointEntity::getDeviceSize, largeScreenDictCodes)
                .list().stream()
                .map(PointEntity::getCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        log.info("存在的大屏点位: {}", StringUtils.join(largeScreenPointCodes, ", "));
        if (CollectionUtil.isEmpty(largeScreenPointCodes)) {
            return false;
        }

        // 所有大屏价格申请
        List<PriceApplyDevicePointEntity> largeScreenPoints = priceApplyDevicePointService.lambdaQuery()
                .select(PriceApplyDevicePointEntity::getApplyId, PriceApplyDevicePointEntity::getPriceApplyDeviceId, PriceApplyDevicePointEntity::getPointCode)
                .in(PriceApplyDevicePointEntity::getPointCode, largeScreenPointCodes)
                .list();
        Set<Integer> deviceIds = Sets.newHashSetWithExpectedSize(largeScreenPoints.size());
        Set<Integer> applyIds = Sets.newHashSetWithExpectedSize(largeScreenPoints.size());
        for (PriceApplyDevicePointEntity point : largeScreenPoints) {
            deviceIds.add(point.getPriceApplyDeviceId());
            applyIds.add(point.getApplyId());
        }

        log.info("大屏价格申请: {}", StringUtils.join(applyIds, ", "));
        log.info("大屏设备分组: {}", StringUtils.join(deviceIds, ", "));
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            applyDeviceService.lambdaUpdate()
                    .set(PriceApplyDeviceEntity::getLargeScreenFlag, SCREEN_FLAG_LARGE)
                    .in(PriceApplyDeviceEntity::getId, deviceIds)
                    .update();
        }

        if (CollectionUtil.isNotEmpty(applyIds)) {
            this.lambdaUpdate()
                    .set(PriceApplyEntity::getLargeScreenFlag, SCREEN_FLAG_LARGE)
                    .in(PriceApplyEntity::getId, applyIds)
                    .update();
        }

        return true;
    }

    /**
     * 批量更新核心区域标记
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateCoreAreaFlag() {
        log.info("开始更新核心区域标记");
        // 开始前置置核心区域标记为null
        applyDeviceService.lambdaUpdate()
                .gt(PriceApplyDeviceEntity::getId, 0)
                .set(PriceApplyDeviceEntity::getCoreAreaFlag, null)
                .update();

        // 查询核心非核心的评分记录ID
        Map<String, Set<Long>> locationMap = buildingParameterService.lambdaQuery()
                .select(BuildingParameterEntity::getId, BuildingParameterEntity::getParameterName)
                .eq(BuildingParameterEntity::getParameterName, "城市核心区")
                .or()
                .eq(BuildingParameterEntity::getParameterName, "城市非核心区")
                .list().stream()
                .collect(Collectors.groupingBy(BuildingParameterEntity::getParameterName, Collectors.mapping(BuildingParameterEntity::getId, Collectors.toSet())));
        if (MapUtils.isEmpty(locationMap)) {
            log.warn("未配置核心非核心的评分记录ID");
            return false;
        }

        // 查询所有设备数据
        List<PriceApplyDeviceEntity> applyDevices = applyDeviceService.list();
        if (CollectionUtil.isEmpty(applyDevices)) {
            log.warn("未查询到设备数据");
            return false;
        }

        // 分组进行处理
        List<List<PriceApplyDeviceEntity>> partitions = Lists.partition(applyDevices, 1000);
        int ct = 0;
        for (List<PriceApplyDeviceEntity> partition : partitions) {
            log.info("开始处理[{}-{}]/[{}]", ct + 1, ct + partition.size(), applyDevices.size());
            handleDevice(partition, locationMap);
        }

        log.info("结束更新核心区域标记");
        return true;
    }

    /**
     * 分组处理设备数据
     */
    private void handleDevice(List<PriceApplyDeviceEntity> applyDevices, Map<String, Set<Long>> locationMap) {
        log.info("开始处理设备数据");
        // 查询价格申请数据
        Set<Integer> applyIds = applyDevices.stream()
                .map(PriceApplyDeviceEntity::getApplyId)
                .filter(e -> Objects.nonNull(e) && e > 0)
                .collect(Collectors.toSet());
        log.info("共计{}个价格申请ID: {}", applyIds.size(), applyIds);
        if (CollectionUtil.isEmpty(applyIds)) {
            log.warn("未查询到价格申请数据");
            return;
        }
        // 获取价格申请中的楼宇编码
        Map<String, Set<Integer>> buildingNoMap = this.lambdaQuery()
                .select(PriceApplyEntity::getId, PriceApplyEntity::getBuildingNo)
                .in(PriceApplyEntity::getId, applyIds)
                .list().stream()
                .collect(Collectors.groupingBy(PriceApplyEntity::getBuildingNo, Collectors.mapping(PriceApplyEntity::getId, Collectors.toSet())));
        if (CollectionUtil.isEmpty(buildingNoMap)) {
            log.warn("未查询到价格申请中的楼宇编码");
            return;
        }
        // 根据楼宇编码查询楼宇评分过程记录
        List<BuildingDetailsEntity> detailsEntities = buildingDetailsService.lambdaQuery()
                .select(BuildingDetailsEntity::getBuildingNo, BuildingDetailsEntity::getBuildingLocation,
                        BuildingDetailsEntity::getThirdBuildingLocationId)
                .in(BuildingDetailsEntity::getBuildingNo, buildingNoMap.keySet())
                .eq(BuildingDetailsEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .list();

        // 处理核心区域标记
        handleCoreAreaFlag(detailsEntities, locationMap, buildingNoMap);
        log.info("结束处理设备数据");
    }

    /**
     * 获取核心、非核心的记录，并更新数据库
     */
    private void handleCoreAreaFlag(List<BuildingDetailsEntity> detailsEntities, Map<String, Set<Long>> locationMap,
                                    Map<String, Set<Integer>> buildingNoMap) {
        log.info("开始处理核心区域标记");
        // 根据评分记录ID查询评分记录
        Set<Integer> coreAreaIds = Sets.newHashSetWithExpectedSize(detailsEntities.size());
        Set<Integer> nonCoreAreaIds = Sets.newHashSetWithExpectedSize(detailsEntities.size());
        Set<Long> coreParameterIds = locationMap.get("城市核心区");
        Set<Long> nonCoreParameterIds = locationMap.get("城市非核心区");
        Set<String> notMatchBuildingNos = new HashSet<>(16);
        for (BuildingDetailsEntity detailsEntity : detailsEntities) {
            // 先取人为评分
            if (Objects.nonNull(detailsEntity.getBuildingLocation()) && detailsEntity.getBuildingLocation() != 0) {
                if (coreParameterIds.contains(detailsEntity.getBuildingLocation())) {
                    coreAreaIds.addAll(buildingNoMap.getOrDefault(detailsEntity.getBuildingNo(), Set.of()));
                } else if (nonCoreParameterIds.contains(detailsEntity.getBuildingLocation())) {
                    nonCoreAreaIds.addAll(buildingNoMap.getOrDefault(detailsEntity.getBuildingNo(), Set.of()));
                } else {
                    notMatchBuildingNos.add(detailsEntity.getBuildingNo());
                }
            }
            // 没有时取AI评分
            else if (Objects.nonNull(detailsEntity.getThirdBuildingLocationId()) && detailsEntity.getThirdBuildingLocationId() != 0) {
                if (coreParameterIds.contains(detailsEntity.getThirdBuildingLocationId())) {
                    coreAreaIds.addAll(buildingNoMap.getOrDefault(detailsEntity.getBuildingNo(), Set.of()));
                } else if (nonCoreParameterIds.contains(detailsEntity.getThirdBuildingLocationId())) {
                    nonCoreAreaIds.addAll(buildingNoMap.getOrDefault(detailsEntity.getBuildingNo(), Set.of()));
                } else {
                    notMatchBuildingNos.add(detailsEntity.getBuildingNo());
                }
            } else {
                notMatchBuildingNos.add(detailsEntity.getBuildingNo());
            }
        }
        // 更新数据库
        log.warn("未匹配的楼宇编码：{}", notMatchBuildingNos);
        log.info("更新核心、非核心区域标记开始，\n核心区域价格申请ID：{}，\n非核心区域价格申请ID：{}", coreAreaIds, nonCoreAreaIds);
        if (CollectionUtil.isNotEmpty(coreAreaIds)) {
            applyDeviceService.lambdaUpdate()
                    .in(PriceApplyDeviceEntity::getApplyId, coreAreaIds)
                    .set(PriceApplyDeviceEntity::getCoreAreaFlag, 1)
                    .update();
            log.info("更新核心区域标记成功，更新数量：{}", coreAreaIds.size());
        }
        if (CollectionUtil.isNotEmpty(nonCoreAreaIds)) {
            applyDeviceService.lambdaUpdate()
                    .in(PriceApplyDeviceEntity::getApplyId, nonCoreAreaIds)
                    .set(PriceApplyDeviceEntity::getCoreAreaFlag, 0)
                    .update();
            log.info("更新非核心区域标记成功，更新数量：{}", nonCoreAreaIds.size());
        }
        log.info("结束处理核心区域标记");
    }

    /**
     * 根据点位code获取设备激励金数据
     *
     * @param pointCodes 点位code集合
     */
    @Override
    public List<PriceApplyDevicePointJoinVO> getIncentivePrice(Collection<String> pointCodes) {
        return baseMapper.selectIncentivePrice(pointCodes);
    }

    /**
     * 价格申请草稿
     *
     * @param applyDto 申请信息
     * @return 草稿保存是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean priceApplyDraft(PriceApplyDto applyDto) {
        // 获取上次保存的草稿信息    商机+创建人确认每个人每个商机的草稿唯一
        if (StringUtils.isBlank(applyDto.getBusinessCode())) {
            throw new ServerException("请先选择商机");
        }
        if (StringUtils.isBlank(applyDto.getBuildingNo())) {
            throw new ServerException("楼宇编码为空");
        }
        PriceApplyEntity priceApplyDraftOld = this.lambdaQuery()
                .eq(PriceApplyEntity::getBusinessCode, applyDto.getBusinessCode())
                .eq(PriceApplyEntity::getCreateBy, UserThreadLocal.getUser().getWno())
                .eq(StringUtils.isNotBlank(applyDto.getApplyCode()), PriceApplyEntity::getApplyCode, applyDto.getApplyCode())
                .eq(PriceApplyEntity::getStatus, PriceApplyEntity.Status.DRAFT.getCode())
                .last("limit 1")
                .one();

        if (null != priceApplyDraftOld) {
            // 删除上次保存的草稿信息
            removeById(priceApplyDraftOld.getId());
            // 删除上次保存的草稿设备信息
            priceApplyDeviceService.lambdaUpdate()
                    .eq(PriceApplyDeviceEntity::getApplyId, priceApplyDraftOld.getId())
                    .remove();
            // 删除上次保存的草稿设备点位信息
            priceApplyDevicePointService.lambdaUpdate()
                    .eq(PriceApplyDevicePointEntity::getApplyId, priceApplyDraftOld.getId())
                    .remove();
        }

        // 1.保存价格申请信息  更新之前的老数据 保留之前的创建时间
        PriceApplyEntity priceApplyDraft = PriceApplyConvert.INSTANCE.toEntity(applyDto);
        priceApplyDraft.setId(null == priceApplyDraftOld ? null : priceApplyDraftOld.getId());
        priceApplyDraft.setStatus(PriceApplyEntity.Status.DRAFT.getCode());
        priceApplyDraft.setCreateTime(null == priceApplyDraftOld ? LocalDateTime.now() : priceApplyDraftOld.getCreateTime());
        // 草稿也要申请编码
        priceApplyDraft.setApplyCode(null == priceApplyDraftOld ? getApplyCode() : priceApplyDraftOld.getApplyCode());
        // 处理前端回显的问题，把整个提交的json保存在业务主表
        priceApplyDraft.setDraft(JsonUtils.toJson(applyDto));
        // 获取部分楼宇的信息并保存
        buildRatingInfo(priceApplyDraft, priceApplyDraft.getBuildingNo());
        save(priceApplyDraft);

        // 2.保存设备信息
        List<PriceApplyDevicePointEntity> allPointEntities = new ArrayList<>();
        List<PriceApplyDeviceDto> devices = applyDto.getDevices();
        if (CollectionUtil.isNotEmpty(devices)) {
            for (PriceApplyDeviceDto device : devices) {
                PriceApplyDeviceEntity priceApplyDeviceEntity = BeanUtil.copyProperties(device, PriceApplyDeviceEntity.class);
                priceApplyDeviceEntity.setApplyId(priceApplyDraft.getId());
                priceApplyDeviceService.save(priceApplyDeviceEntity);
                // 构建点位信息
                List<PriceApplyDetailDto.DevicePointDto> points = device.getPoints();
                if (CollectionUtil.isNotEmpty(points)) {
                    List<PriceApplyDevicePointEntity> priceApplyDevicePointEntities = points.stream()
                            .map(point -> new PriceApplyDevicePointEntity(null, priceApplyDraft.getId(), priceApplyDeviceEntity.getId(), point.getName(), point.getCode()))
                            .toList();
                    allPointEntities.addAll(priceApplyDevicePointEntities);
                }
            }
        }
        // 3.保存点位信息
        if (CollectionUtil.isNotEmpty(allPointEntities)) {
            priceApplyDevicePointService.saveBatch(allPointEntities);
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean priceApplyDraftDelete(Integer id) {
        // 删除价格申请信息
        removeById(id);
        // 删除设备信息
        priceApplyDeviceService.lambdaUpdate()
                .eq(PriceApplyDeviceEntity::getApplyId, id)
                .remove();
        // 删除设备点位信息
        priceApplyDevicePointService.lambdaUpdate()
                .eq(PriceApplyDevicePointEntity::getApplyId, id)
                .remove();
        return Boolean.TRUE;
    }

    @Override
    public PriceApplyDetailDto getPriceApplyDraft(String businessCode) {
        // 机构编码必传
        if (StringUtils.isBlank(businessCode)) {
            return null;
        }
        // 获取当前登陆人草稿 多条草稿返回最近一条草稿
        PriceApplyEntity priceApply = this.lambdaQuery()
                .eq(PriceApplyEntity::getBusinessCode, businessCode)
                .eq(PriceApplyEntity::getCreateBy, UserThreadLocal.getUser().getWno())
                .eq(PriceApplyEntity::getStatus, PriceApplyEntity.Status.DRAFT.getCode())
                .orderByDesc(PriceApplyEntity::getCreateTime)
                .last("limit 1")
                .one();
        if (null == priceApply) {
            return null;
        }
        // 根据草稿字段构建草稿返回
        String draft = priceApply.getDraft();
        if (StringUtils.isBlank(draft)) {
            return null;
        }
        PriceApplyDetailDto priceApplyDetailDto = JsonUtils.fromJson(draft, PriceApplyDetailDto.class);
        // 处理附件信息
        setFiles(priceApplyDetailDto);
        // 处理项目相关的信息
        priceApplyDetailDto.setProjectName(priceApply.getBuildingName());
        priceApplyDetailDto.setProjectType(BuildingRatingEntity.BuildingType.getNameByValue(priceApply.getBuildingType()));
        priceApplyDetailDto.setScore(priceApply.getProjectLevel());
        return priceApplyDetailDto;
    }

    /**
     * 生成申请编号
     * JGSQ+年月日+流水号（4位，从0001开始）
     */
    private String getApplyCode() {
        String today = DATE_FORMATTER.format(LocalDate.now());
        String cacheKey = "meht:price:apply:code:" + today;
        Long index = stringRedisTemplate.opsForValue().increment(cacheKey);
        stringRedisTemplate.expire(cacheKey, 24, TimeUnit.HOURS);
        return String.format("JGSQ%s%04d", today, index);
    }
}
