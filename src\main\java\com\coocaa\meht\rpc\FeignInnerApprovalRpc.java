package com.coocaa.meht.rpc;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.config.FeignConfig;
import com.coocaa.meht.rpc.dto.ApprovalInitiateParam;
import com.coocaa.meht.rpc.dto.InnerApproveApplyPageQueryParam;
import com.coocaa.meht.rpc.dto.InnerApproveOpinionParam;
import com.coocaa.meht.rpc.dto.InnerApproveTaskPageQueryParam;
import com.coocaa.meht.rpc.dto.RollbackCancelParam;
import com.coocaa.meht.rpc.dto.TaskDealCountRpcParam;
import com.coocaa.meht.rpc.vo.InnerApproveInstanceVO;
import com.coocaa.meht.rpc.vo.InnerApproveNodeVO;
import com.coocaa.meht.rpc.vo.InnerInstanceTaskVO;
import com.coocaa.meht.rpc.vo.InnerTaskOperateVO;
import com.coocaa.meht.rpc.vo.TaskDealCountRpcVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 站内审批系统接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-11
 */
@FeignClient(
        value = "cheese-authority-api",
        path = "/sys/approve/internal",
        configuration = FeignConfig.class)
public interface FeignInnerApprovalRpc {

    /**
     * 发起审批
     */
    @PostMapping("/instance")
    ResultTemplate<String> initiateApproval(@RequestBody ApprovalInitiateParam param);

    /**
     * 查询审批实例节点
     */
    @GetMapping("/instance/node")
    ResultTemplate<List<InnerApproveNodeVO>> queryNodes(@RequestParam String instanceCode);

    /**
     * 用户审批任务列表(分页)
     */
    @PostMapping("/user/task")
    ResultTemplate<PageResponseVO> queryTaskPage(@RequestBody PageRequestVO<InnerApproveTaskPageQueryParam> page);

    /**
     * 用户申请审批列表(分页)
     */
    @PostMapping("/user/instance")
    ResultTemplate<PageResponseVO> queryApplyPage(@RequestBody PageRequestVO<InnerApproveApplyPageQueryParam> page);

    /**
     * 同意审批
     */
    @PostMapping("/task/approve")
    ResultTemplate<InnerTaskOperateVO> agreeApproval(@RequestBody InnerApproveOpinionParam param);

    /**
     * 拒绝审批
     */
    @PostMapping("/task/reject")
    ResultTemplate<InnerTaskOperateVO> rejectApproval(@RequestBody InnerApproveOpinionParam param);

    /**
     * 查询审批实例待完成的任务
     */
    @GetMapping("/instance/task")
    ResultTemplate<InnerInstanceTaskVO> queryTask(@RequestParam String instanceCode);

    /**
     * 查询审批单详情
     */
    @GetMapping("/instance/detail")
    ResultTemplate<InnerApproveInstanceVO> queryDetail(@RequestParam String instanceCode);

    /**
     * 查询用户待办统计
     */
    @PostMapping("/deal-count")
    ResultTemplate<List<TaskDealCountRpcVO>> getTaskCount(@RequestBody TaskDealCountRpcParam param);

    /**
     * 申请人审批单撤回
     */
    @PostMapping("/instance/revoke")
    ResultTemplate<InnerTaskOperateVO> revoke(@RequestBody RollbackCancelParam param);
} 