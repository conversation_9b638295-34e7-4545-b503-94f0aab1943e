package com.coocaa.meht.module.web.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RatingApplyDto {
    /**
     * 楼宇编码
     */
    private String buildingNo;

    /**
     * 完善编码
     */
    private String completeRatingNo;

    /**
     * 楼宇名称
     */
    @NotBlank(message = "楼宇名称不能为空")
    private String buildingName;

    /**
     * 楼宇类型 0 写字楼 1 商住楼 2 综合体 3 产业园区
     */
    @NotNull(message = "楼宇类型不能为空")
    private Integer buildingType;
    /**
     * 地图楼宇编码
     */
    @Schema(description = "地图楼宇编码")
    @NotBlank(message = "地图楼宇编码不能为空")
    private String mapNo;

    @Schema(description = "目标点位数量")
    private Integer targetPointCount;
    /**
     * 省名称
     */
    @NotBlank(message = "省名称不能为空")
    private String mapProvince;
    /**
     * 市名称
     */
//    @NotBlank(message = "城市名称不能为空")
    private String mapCity;
    /**
     * 区名称
     */
    private String mapRegion;
    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    private String mapAddress;
    /**
     * 纬度
     */
    @NotBlank(message = "纬度不能为空")
    private String mapLatitude;
    /**
     * 经度
     */
    @NotBlank(message = "经度不能为空")
    private String mapLongitude;

    @NotNull(message = "行政区域不能为空")
    private String mapAdCode;
    /**
     * 描述
     */
    private String buildingDesc;

    /**
     * 附件地址
     */
    @Schema(description = "外观照片")
    private List<String> buildingExteriorPics;
    @Schema(description = "大堂环境照片")
    private List<String> buildingLobbyPics;
    @Schema(description = "电梯厅环境照片")
    private List<String> buildingHallPics;
    /**
     * 综合得分
     */
    private BigDecimal buildingScore;
    /**
     * 等级评价
     */
    private String projectLevel;
    /**
     写字楼等级
     */
    private Long buildingGrade;
    /**
     地理等级
     */
    private Long buildingLocation;
    /**
     楼层数
     */
    private Long buildingNumber;

    /**
     月租金
     */
    private Long buildingPrice;
    /**
     * 月租金输入
     */
    private String buildingPriceInput;
    /**
     楼龄
     */
    private Long buildingAge;
    /**
     * 楼龄输入
     */
    private String buildingAgeInput;
    /**
     外墙材料
     */
    private Long buildingExterior;
    /**
     楼盘大堂
     */
    private Long buildingLobby;
    /**
     地下车库
     */
    private Long buildingGarage;
    /**
     侯梯厅
     */
    private Long buildingHall;
    /**
     综合体品牌
     */
    private Long buildingBrand;

    /**
     * 第三方品牌ID
     * 来源于数据表：building_brand
     */
    private Long topBrandId;

    /**
     * 第三方品牌名称
     */
    private String topBrandName;

    /**
     点评评分
     */
    private Long buildingRating;
    /**
     入驻率
     */
    private Long buildingSettled;
    /**
     第三方写字楼等级
     */
    private String thirdBuildingGrade;
    /**
     第三方地理等级
     */
    private String thirdBuildingLocation;
    /**
     第三方楼层数
     */
    private String thirdBuildingNumber;
    /**
     第三方月租金
     */
    private String thirdBuildingPrice;
    /**
     第三方楼龄
     */
    private String thirdBuildingAge;
    /**
     第三方外观造型
     */
    private String thirdBuildingExterior;
    /**
     第三方楼盘大堂
     */
    private String thirdBuildingLobby;
    /**
     第三方地下车库
     */
    private String thirdBuildingGarage;
    /**
     第三方侯梯厅
     */
    private String thirdBuildingHall;
    /**
     第三方综合体品牌
     */
    private String thirdBuildingBrand;
    /**
     第三方点评评分
     */
    private String thirdBuildingRating;
    /**
     第三方入驻率
     */
    private String thirdBuildingSettled;

    @Schema(description = "大堂环境图附件地址")
    private List<String> buildingLobbyEnvPics;

    @Schema(description = "梯厅环境图附件地址")
    private List<String> buildingElevatorPics;

    @Schema(description = "闸口图附件地址")
    private List<String> buildingGatePics;

    @Schema(description = "安装示意图附件地址")
    private List<String> buildingInstallationPics;


    /**
     * 操作类型：1-新建数据审核，2-完善评级审核
     */
    @Schema(description = "操作类型：1-新建数据审核，2-完善评级审核", example = "1")
    private Integer operateType = 1;


    // ========== 评级相关字段 ==========

    @Schema(description = "楼层数输入值")
    private String buildingNumberInput;

    @Schema(description = "日租金输入值")
    private BigDecimal dailyPriceInput;

    @Schema(description = "日租金")
    private BigDecimal dailyPrice;

    @Schema(description = "交付日期输入值")
    private LocalDate deliveryDate;

    @Schema(description = "楼宇等级文本值")
    private String gradeName;

    @Schema(description = "地理位置文本值")
    private String locationName;

    @Schema(description = "外立面文本值")
    private String exteriorName;

    @Schema(description = "大堂文本值")
    private String lobbyName;

    @Schema(description = "车库文本值")
    private String garageName;

    @Schema(description = "等候厅文本值")
    private String hallName;

    @Schema(description = "品牌文本值")
    private String brandName;

    @Schema(description = "评分文本值")
    private String ratingName;

    @Schema(description = "入驻率文本值")
    private String settledName;

    @Schema(description = "第三方日租金")
    private String thirdDailyPrice;

    @Schema(description = "第三方交付日期")
    private String thirdDeliveryDate;

    // ========== 大屏相关字段 ==========

    @Schema(description = "挑高")
    private BigDecimal buildingCeilingHeight;

    @Schema(description = "间距")
    private BigDecimal buildingSpacing;

    @Schema(description = "安装规格", example = "[\"0013-3\",\"0013-4\"]")
    private String spec;

    @Schema(description = "楼栋数量")
    private Integer totalUnitCount;

    @Schema(description = "电梯数量")
    private Integer elevatorCount;

    @Schema(description = "入驻企业数")
    private Integer companyCount;

    @Schema(description = "特殊说明")
    private String specialDesc;

    //========== 基因数据 ==========
    @Schema(description = "总栋数")
    private Integer totalBuildingCount;

    @Schema(description = "总等候厅数量")
    private Integer totalWaitingCount;

    @Schema(description = "总车位数量")
    private Integer parkingCount;

    @Schema(description = "禁行行业")
    private List<String> forbiddenIndustry;

    /**
     * 物业费
     */
    @Schema(description = "物业费")
    private BigDecimal propertyFee;

    /**
     * 最低楼层数
     */
    @Schema(description = "最低楼层数")
    private Integer minFloorCount;

    /**
     * 入住率
     */
    @Schema(description = "入住率")
    private BigDecimal occupancyRate;

    /**
     * 最高层数
     */
    @Schema(description = "最高层数")
    private Integer maxFloorCount;

    /**
     * 房价
     */
    @Schema(description = "房价")
    private BigDecimal housePrice;

    /**
     * 覆盖人数
     */
    private Integer coverageCount;

    /**
     * 竞媒信息
     */
    @Schema(description = "竞媒信息")
    private List<String> competitiveMediaInfos;

}
