@startuml
title 价格申请流程 POST /price/apply

start

:调用 priceApplyBasicCheck 方法进行基础校验;
if (校验是否通过?) then (是)
    :获取楼宇信息 buildingRating;
    :获取楼宇 top 值 topLevel;
    :判断是否为核心区域 coreArea;

    :构建 PriceApplyEntity 实体;
    :保存或更新 priceApply 数据;

    if (是否存在设备列表?) then (是)
        :初始化 globalLargeScreenFlag 和点位实体列表;
        note right: 是否是大屏 [1:小屏, 2:大屏, 3:大小屏]

        :获取第一个设备;
        while (还有更多设备?)
            :复制设备属性到 PriceApplyDeviceEntity;
            :设置大屏标记和核心区域标记;
            :更新 globalLargeScreenFlag;

            if (是否存在水位价?) then (否)
                :标记为需要二级审核;
            else
                if (是否超小屏水位价?) then (是)
                    :标记为需要二级审核;
                endif
                if (是否超大屏水位价?) then (是)
                    :标记为需要二级审核;
                endif
            endif

            :保存设备信息;
            if (保存成功?) then (是)
                if (是否存在点位信息?) then (是)
                    :构建点位实体并添加到列表;
                else
                    :抛出异常 "该申请设备未绑定点位，请绑定点位";
                endif
            endif

            :处理下一个设备;
        endwhile

        :检查所有选中的点位是否被删除;
        if (存在已删除的点位?) then (是)
            :抛出异常 "所选点位已删除，请重新选择安装位置";
        endif

        :批量保存点位信息;
        :更新价格申请的大屏标记和复核系数;
    endif

    if (商机状态是达成意向?) then (是)
        :构建商机状态变更对象并发送消息;
    endif

    :构建审批条件并提交审批;
    :调用审批中心发起审批;
else (否)
    :抛出异常;
endif

:返回保存结果 result;

stop

footer
注:
1. 整个流程在@Transactional事务中执行
2. 所有异常都会触发事务回滚
end footer

@enduml