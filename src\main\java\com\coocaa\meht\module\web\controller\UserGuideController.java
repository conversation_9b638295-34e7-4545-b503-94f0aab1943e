package com.coocaa.meht.module.web.controller;


import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.web.dto.UserGuideReadDTO;
import com.coocaa.meht.module.web.service.UserGuideService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户引导控制器
 * @since 2025-06-11
 */
@Slf4j
@RestController
@RequestMapping("/guides")
@RequiredArgsConstructor
@Tag(name = "用户引导", description = "用户引导相关接口")
public class UserGuideController {

    private final UserGuideService userGuideService;

    /**
     * 检查功能引导是否已读
     *
     * @param featureCode 功能编码
     * @param userCode    用户编码
     * @return 是否已读
     */
    @GetMapping("/check")
    @Operation(summary = "检查功能引导是否已读", description = "根据功能编码和用户编码检查功能引导是否已读")
    public Result<Boolean> checkGuide(
            @Parameter(description = "功能编码", required = true) @RequestParam String featureCode,
            @Parameter(description = "用户编码", required = false) @RequestParam(required = false) String userCode) {
        log.info("检查功能引导是否已读: featureCode={}, userCode={}", featureCode, userCode);
        try {
            userCode = StringUtils.isBlank(userCode) ? UserThreadLocal.getUser().getWno() : userCode;
            boolean hasRead = userGuideService.checkUserRead(featureCode, userCode);
            log.info("检查功能引导是否已读结果: featureCode={}, userCode={}, hasRead={}", featureCode, userCode, hasRead);
            return Result.ok(hasRead);
        } catch (Exception e) {
            log.error("检查功能引导是否已读异常: featureCode={}, userCode={}, 错误={}", featureCode, userCode, e.getMessage(), e);
            return Result.error("检查功能引导是否已读失败: " + e.getMessage());
        }
    }

    /**
     * 标记功能引导已读
     *
     * @param dto 请求参数
     * @return 标记结果
     */
    @PostMapping("/read")
    @Operation(summary = "标记功能引导已读", description = "标记用户对某功能的引导已读")
    public Result<Boolean> markRead(@Valid @RequestBody UserGuideReadDTO dto) {
        log.info("标记功能引导已读: featureCode={}, userCode={}", dto.getFeatureCode(), dto.getUserCode());
        try {
            String userCode = StringUtils.isBlank(dto.getUserCode()) ? UserThreadLocal.getUser().getWno() : dto.getUserCode();
            boolean success = userGuideService.markRead(userCode, dto.getFeatureCode());
            log.info("标记功能引导已读结果: featureCode={}, userCode={}, success={}", dto.getFeatureCode(), userCode, success);
            return Result.ok(success);
        } catch (Exception e) {
            log.error("标记功能引导已读异常: featureCode={}, userCode={}, 错误={}", dto.getFeatureCode(), dto.getUserCode(), e.getMessage(), e);
            return Result.error("标记功能引导已读失败: " + e.getMessage());
        }
    }
} 