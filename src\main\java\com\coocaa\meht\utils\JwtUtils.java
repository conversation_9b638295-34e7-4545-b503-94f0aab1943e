package com.coocaa.meht.utils;


import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date 2023-12-20 18:14
 */
/*public class JwtUtils {
    public static final String serverApiSecret = "qkCwk34YGLYlyXaisNgKPq7UXldUIfmpIUbx";
    private static final long EXPIRE = 24 * 60 * 60 * 1000L; //1天
    private static final String cacheTimeKey = "_HoldTime";
    private static final Map<String, String> CACHE_MAP = new ConcurrentHashMap<>();

    *//**
     * 创建
     *
     * @param server
     * @param secret
     * @return
     *//*
    public static String createToken(String server, String secret) {
        String accessToken = get(server);
        if (accessToken != null) return accessToken;
        Date nowDate = new Date();
        accessToken = Jwts.builder().setHeaderParam("typ", "JWT")
                .setIssuer("TTC3.0")
                .setIssuedAt(nowDate)
                .setExpiration(new Date(nowDate.getTime() + EXPIRE))
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
        put(server, accessToken, EXPIRE - 5000);
        return accessToken;
    }

    private static String get(String key) {
        String value = CACHE_MAP.get(key);
        if (value != null) {
            Long ecpire = Converts.toLong(CACHE_MAP.get(key + cacheTimeKey));
            if (ecpire == null || ecpire <= 0) {
                return null;
            }
            if (ecpire < System.currentTimeMillis()) {
                remove(key);
                return null;
            }
            return value;
        }
        return null;
    }

    public static boolean checkCache(String key) {
        Long cacheHoldTime = Converts.toLong(CACHE_MAP.get(key + cacheTimeKey));
        if (cacheHoldTime == null || cacheHoldTime <= 0) {
            return false;
        }
        if (cacheHoldTime < System.currentTimeMillis()) {
            remove(key);
            return false;
        }
        return true;
    }

    private static void put(String key, String value, long holdTime) {
        CACHE_MAP.put(key + cacheTimeKey, String.valueOf(System.currentTimeMillis() + holdTime));
        CACHE_MAP.put(key, value);
    }

    private static void remove(String key) {
        CACHE_MAP.remove(key);
        CACHE_MAP.remove(key + cacheTimeKey);
    }

}*/
