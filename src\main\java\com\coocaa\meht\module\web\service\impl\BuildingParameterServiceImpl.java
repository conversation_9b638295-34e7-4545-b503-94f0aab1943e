package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.config.AiBaseScoreProperties;
import com.coocaa.meht.module.web.dao.BuildingParameterDao;
import com.coocaa.meht.module.web.dto.BuildingParameterDto;
import com.coocaa.meht.module.web.dto.BuildingTypesDto;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.service.BuildingParameterService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service
public class BuildingParameterServiceImpl extends ServiceImpl<BuildingParameterDao, BuildingParameterEntity> implements BuildingParameterService {

    // 规则版本号
    @Value("${parameter.max.dataFlag}")
    private Integer dataFlag;

    @Resource
    private BuildingRatingService buildingRatingService;

    @Resource
    private AiBaseScoreProperties aiBaseScoreProperties;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    // 权重缓存
    private static final String WEIGHT_CACHE_KEY_TEMPLATE = "building-parameter:weight:%d:%d";

    // 评分缓存
    private static final String SCORE_CACHE_KEY_TEMPLATE = "building-parameter:score:%d:%d";

    /**
     * 刷新评级规则缓存
     */
    @Override
    @PostConstruct
    public Boolean refreshCache() {
        log.info("刷新评级规则缓存");

        // 查询所有规则项
        List<BuildingParameterEntity> rules = lambdaQuery()
                .select(BuildingParameterEntity::getId,
                        BuildingParameterEntity::getDataFlag,
                        BuildingParameterEntity::getBuildingType,
                        BuildingParameterEntity::getParameterCode,
                        BuildingParameterEntity::getParameterScore,
                        BuildingParameterEntity::getWeightValue)
                .ne(BuildingParameterEntity::getParentId, 0)
                .list();


        if (CollUtil.isEmpty(rules)) {
            return true;
        }

        HashMap<String, Map<String, String>> mapping = new HashMap<>();
        rules.forEach(rule -> {
            String weightKey = String.format(WEIGHT_CACHE_KEY_TEMPLATE, rule.getDataFlag(), rule.getBuildingType());
            Map<String, String> weightMapping = mapping.computeIfAbsent(weightKey, k -> new HashMap<>());
            weightMapping.put(rule.getParameterCode(), rule.getWeightValue().toString());

            String scoreKey = String.format(SCORE_CACHE_KEY_TEMPLATE, rule.getDataFlag(), rule.getBuildingType());
            Map<String, String> scoreMapping = mapping.computeIfAbsent(scoreKey, k -> new HashMap<>());
            scoreMapping.put(rule.getId().toString(), rule.getParameterScore().toString());
        });

        // 清除缓存
        redisTemplate.delete(mapping.keySet());

        // 设置缓存
        mapping.forEach((key, value) -> redisTemplate.opsForHash().putAll(key, value));

        log.info("刷新评级规则缓存完成");

        return true;
    }

    public BuildingParameterEntity.ScoreAndWeight<BigDecimal> getScoreAndWeight(
            Integer dataFlag, Integer buildingType, Long ruleId, String parameterCode) {
        BigDecimal weight = getWeight(dataFlag, buildingType, parameterCode);

        if (ruleId < 0) {
            // 返回AI基础分
            BigDecimal aiBaseScore = aiBaseScoreProperties.getScoreById(ruleId).score();
            return new BuildingParameterEntity.ScoreAndWeight<>(aiBaseScore, weight);
        }

        if (ruleId == 0) {
            return new BuildingParameterEntity.ScoreAndWeight<>(BigDecimal.ZERO, weight);
        }

        BigDecimal score = getScore(dataFlag, buildingType, ruleId);

        return new BuildingParameterEntity.ScoreAndWeight<>(score, weight);
    }

    private BigDecimal getScore(Integer dataFlag, Integer buildingType, Long ruleId) {
        // 缓存获取
        String ruleKey = String.format(SCORE_CACHE_KEY_TEMPLATE, dataFlag, buildingType);
        String scoreStr = redisTemplate.<String, String>opsForHash().get(ruleKey, ruleId.toString());
        if (StrUtil.isNotBlank(scoreStr)) {
            return new BigDecimal(scoreStr);
        }

        // 数据库查询
        BuildingParameterEntity rule = getById(ruleId);
        if (Objects.isNull(rule)) {
            log.error("未找到对应规则配置，规则id：{}", ruleId);
            throw new ServerException("未找到对应规则配置");
        }

        // 更新缓存
        redisTemplate.opsForHash().put(ruleKey, ruleId.toString(), rule.getParameterScore().toString());

        return rule.getParameterScore();
    }

    private BigDecimal getWeight(Integer dataFlag, Integer buildingType, String parameterCode) {
        // 缓存获取
        String weightKey = String.format(WEIGHT_CACHE_KEY_TEMPLATE, dataFlag, buildingType);
        String weightStr = redisTemplate.<String, String>opsForHash().get(weightKey, parameterCode);
        if (StrUtil.isNotBlank(weightStr)) {
            return new BigDecimal(weightStr);
        }

        // 数据库查询
        BuildingParameterEntity rule = lambdaQuery()
                .select(BuildingParameterEntity::getWeightValue)
                .eq(BuildingParameterEntity::getDataFlag, dataFlag)
                .eq(BuildingParameterEntity::getBuildingType, buildingType)
                .eq(BuildingParameterEntity::getParameterCode, parameterCode)
                .last("limit 1")
                .one();
        if (Objects.isNull(rule) || Objects.isNull(rule.getWeightValue())) {
            log.warn("未找到对应权重配置，dataFlag：{}，buildingType：{}，parameterCode：{}", dataFlag, buildingType, parameterCode);
            throw new ServerException("未找到对应权重配置");
        }

        // 更新缓存
        redisTemplate.opsForHash().put(weightKey, parameterCode, rule.getWeightValue().toString());

        return rule.getWeightValue();
    }

    @Override
    public List<BuildingTypesDto> getBuildingType(String buildingNo) {
        Integer dataVersion = dataFlag;
        if (StrUtil.isNotBlank(buildingNo)) {
            BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(buildingNo);
            if (buildingRating != null && BuildingRatingEntity.Status.REJECTED.value == buildingRating.getStatus()) {
                dataVersion = buildingRating.getDataFlag();
            }
        }
        return getBuildingParameter(dataVersion);
    }

    @Override
    public List<BuildingTypesDto> getParameterBuildingType(Integer dataVersion) {

        return getBuildingParameter(dataVersion);
    }

    private List<BuildingTypesDto> getBuildingParameter(Integer dataVersion) {

        List<BuildingTypesDto> buildingTypesDtoList = this.baseMapper.getBuildingType(dataVersion);
        for (BuildingTypesDto buildingTypesDto : buildingTypesDtoList) {
            List<BuildingParameterDto> buildingParameterDtoList = this.baseMapper.getBuilding(buildingTypesDto.getId(), dataVersion);
            buildingTypesDto.setParameter(buildingParameterDtoList);
            for (BuildingParameterDto buildingParameterDto : buildingParameterDtoList) {
                List<BuildingParameterDto> buildingParameterDtoList1 = this.baseMapper.getBuildingByParentId(buildingParameterDto.getId(), buildingParameterDto.getBuildingType(), dataVersion);
                buildingParameterDto.setChild(buildingParameterDtoList1);
            }
        }
        return buildingTypesDtoList;
    }


    @Override
    public Map<String, BuildingParameterEntity> getByMapIds(List<Long> ids) {
        List<BuildingParameterEntity> list = this.list(Wrappers.<BuildingParameterEntity>lambdaQuery()
                .in(BuildingParameterEntity::getId, ids));
        Map<String, BuildingParameterEntity> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(ele -> result.put(ele.getParameterCode(), ele));
        }
        return result;
    }
}
