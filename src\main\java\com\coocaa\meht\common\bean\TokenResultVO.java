package com.coocaa.meht.common.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TokenResultVO {
    private String token;
    private String refreshToken;

    @Schema(description = "提示信息", type = "String", example = "要改密码啦")
    private String message;
}
