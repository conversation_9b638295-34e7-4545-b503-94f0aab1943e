package com.coocaa.meht.rpc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/31
 */

@Data
@Builder
public class SspPointAddParam {

    @NotNull
    @Schema(description = "等候厅id")
    private Integer waitingHallId;

    @Schema(description = "点位备注")
    private String remark;

    @Schema(description = "点位编码")
    private String pointCode;

    @Schema(description = "点位序号")
    private Integer number;

    @Schema(description = "点位图片列表")
    private List<String> images;

}
