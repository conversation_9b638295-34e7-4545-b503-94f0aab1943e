package com.coocaa.meht.module.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.meht.module.crm.dto.CrmBusinessOpportunityDynamicDto;
import com.coocaa.meht.module.crm.dto.CrmPageResultDto;
import com.coocaa.meht.module.crm.dto.req.CrmFollowUpListReq;
import com.coocaa.meht.module.crm.service.CrmPreDataSyncService;
import com.coocaa.meht.module.sys.entity.SysUserEntity;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.IBuildingStatusChangeLogService;
import com.coocaa.meht.utils.JsonUtils;
import com.google.common.collect.Lists;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @file CrmPreDataSyncServiceImpl
 * @date 2024/12/30 16:42
 * @description This is a java file.
 */

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CrmPreDataSyncServiceImpl extends CrmBaseService implements CrmPreDataSyncService {
    private final BusinessOpportunityService businessOpportunityService;
    private final IBuildingStatusChangeLogService buildingStatusChangeLogService;
    private final SysUserService sysUserService;
    private final Lock lock = new ReentrantLock();

    /**
     * 同步商机动态
     */
    @Override
    public int syncBusinessDynamic(int batchInsertSize, int fetchInterval) {
        if (lock.tryLock()) {
            try {
                LocalDateTime startTime = LocalDateTime.now();
                log.info("\n\n============================================================ 开始同步商机动态：{}\n", startTime);
                int count = syncBusinessDynamicHandle(batchInsertSize, fetchInterval);
                log.info("\n\n============================================================ 结束同步商机动态：{}，共耗时{}秒\n", LocalDateTime.now(),
                        Duration.between(startTime, LocalDateTime.now()).getSeconds());
                log.info("\n\n============================================================ 共计插入{}条商机动态记录成功\n", count);
                threadSleep(3000);
                return count;
            } catch (Exception e) {
                log.error("同步商机动态失败。异常信息:{}", e.getMessage());
                return -1;
            } finally {
                lock.unlock();
            }
        } else {
            return -2;
        }
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-23 13:49
     * @Description：处理商机动态同步
     */
    private int syncBusinessDynamicHandle(int batchInsertSize, int fetchInterval) {
        // 查询商机列表数据 - BusinessId
        Map<String, BusinessOpportunityEntity> businessOpportunityByBusinessIdMap =
                businessOpportunityService.list(new LambdaQueryWrapper<BusinessOpportunityEntity>().ne(BusinessOpportunityEntity::getCrmBusinessId, ""))
                        .stream().collect(Collectors.toMap(BusinessOpportunityEntity::getCrmBusinessId, e -> e, (e1, e2) -> e1));
        log.info("查询到{}条需要同步的商机记录", businessOpportunityByBusinessIdMap.size());
        if (businessOpportunityByBusinessIdMap.isEmpty()) {
            log.info("数据库中没有需要同步的商机记录，同步结束");
            return 0;
        }

        // 查询系统用户的工号信息
        Map<String, SysUserEntity> sysUserByNameMap = getSysUserList().stream()
                .collect(Collectors.toMap(SysUserEntity::getRealName, e -> e, (e1, e2) -> e1));
        log.info("查询到{}条系统用户工号信息", sysUserByNameMap.size());

        // 遍历商机列表，解析商机动态信息
        List<BuildingStatusChangeLogEntity> allBuildingStatusChangeLogEntityList = new ArrayList<>(businessOpportunityByBusinessIdMap.size());
        businessOpportunityByBusinessIdMap.forEach((businessId, businessOpportunityEntity) -> {
            //
            List<Map<String, Object>> dataList = getBusinessDetail(businessId);
            if (fetchInterval != 0) threadSleep(fetchInterval);
            //
            List<BuildingStatusChangeLogEntity> buildingStatusChangeLogEntityList =
                    parseBusinessDynamic(dataList, businessOpportunityEntity, sysUserByNameMap);
            if (!buildingStatusChangeLogEntityList.isEmpty()) {
                allBuildingStatusChangeLogEntityList.addAll(buildingStatusChangeLogEntityList);
            }
        });

        // 插入商机动态记录 - 数据库
        try {
            log.info("共计解析出{}条需要插入的商机动态记录", allBuildingStatusChangeLogEntityList.size());
            // 分批次插入一次数据库
            List<List<BuildingStatusChangeLogEntity>> partitionRecordsList =
                    Lists.partition(allBuildingStatusChangeLogEntityList, batchInsertSize);
            int count = 0, round = 1;
            for (List<BuildingStatusChangeLogEntity> partitionRecords : partitionRecordsList) {
                log.info("\n-------------------------------------------------------->>> 开始第{}轮解析，当前进度：{}/{}", round, round++, partitionRecordsList.size());
                log.info("解析出{}条需要插入的商机动态记录", partitionRecords.size());
                List<BuildingStatusChangeLogEntity> newBuildingStatusChangeLogEntityList = excludeAlreadySavedBusinessLogRecord(partitionRecords);
                log.info("实际需要插入{}条商机动态记录", newBuildingStatusChangeLogEntityList.size());
                if (newBuildingStatusChangeLogEntityList.size() == 0) continue;
                int result = buildingStatusChangeLogService.saveBatch(newBuildingStatusChangeLogEntityList)
                        ? newBuildingStatusChangeLogEntityList.size() : -1;
                if (result > 0) {
                    log.info("插入{}条商机动态记录成功", result);
                } else {
                    log.info("插入商机动态记录失败");
                }
                count += result;
                log.info(">>>>>>>>>>>> 当前累计插入{}记录成功!", count);
            }

            return count;
        } catch (Exception e) {
            log.error("插入商机动态记录失败。异常信息:{}", e.getMessage());
            return -1;
        }
    }

    private static void threadSleep(long timeout) {
        try {
            Thread.sleep(timeout);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询系统用户的工号信息 - 数据库
     */
    private List<SysUserEntity> getSysUserList() {
        LambdaQueryWrapper<SysUserEntity> queryWrapper = new QueryWrapper<SysUserEntity>().lambda()
                .select(SysUserEntity::getId, SysUserEntity::getEmpCode, SysUserEntity::getRealName)
                .eq(SysUserEntity::getStatus, 1);

        return sysUserService.list(queryWrapper);
    }

    /**
     * 获取商机详情 - crm接口
     */
    private List<Map<String, Object>> getBusinessDetail(String businessId) {
        String url = "/crmActivity/getCrmActivityPageList";
        CrmFollowUpListReq req = new CrmFollowUpListReq();
        req.setQueryType(2);
        req.setActivityTypeId(businessId);
        req.setLimit(100L);
        // 调用CRM
        CrmPageResultDto crmPageResultDto = callCrmApi(url, req, CrmPageResultDto.class);
        return crmPageResultDto.getData().getList();
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-23 14:15
     * @Description：解析商机动态记录
     */
    private List<BuildingStatusChangeLogEntity> parseBusinessDynamic(List<Map<String, Object>> dataList,
                                                                     BusinessOpportunityEntity businessOpportunityEntity,
                                                                     Map<String, SysUserEntity> sysUserByNameMap) {
        // 保存数据库记录对象
        List<BuildingStatusChangeLogEntity> buildingStatusChangeLogEntityList = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            CrmBusinessOpportunityDynamicDto crmBusinessOpportunityDynamicDto =
                    JsonUtils.fromJson(JsonUtils.toJson(map), CrmBusinessOpportunityDynamicDto.class);
            if (crmBusinessOpportunityDynamicDto.getContent() == null) continue;
            //
            BuildingStatusChangeLogEntity.BizStatus bizStatus = null;
            if (crmBusinessOpportunityDynamicDto.getContent() instanceof Map) {
                // 初始创建状态
                bizStatus = BuildingStatusChangeLogEntity.BizStatus.WAIT_NEGOTIATION;
            } else if (crmBusinessOpportunityDynamicDto.getContent() instanceof String) {
                // 匹配content
                for (var bs : BuildingStatusChangeLogEntity.BizStatus.values()) {
                    if (((String) crmBusinessOpportunityDynamicDto.getContent()).endsWith(bs.getDesc())) {
                        bizStatus = bs;
                        break;
                    }
                }
            } else continue;

            // 完善entity数据
            BuildingStatusChangeLogEntity entity = new BuildingStatusChangeLogEntity();
            entity.setType(BuildingStatusChangeLogEntity.BizType.BUSINESS.getCode());
            entity.setBizId(businessOpportunityEntity.getId().longValue());
            entity.setBizCode(businessOpportunityEntity.getCode());
            if (Objects.nonNull(bizStatus)) entity.setStatus(bizStatus.getCode());
            entity.setContent(crmBusinessOpportunityDynamicDto.getContent().toString());
            entity.setOperatorName(crmBusinessOpportunityDynamicDto.getCreateUser().getRealname());
            entity.setChangeTime(crmBusinessOpportunityDynamicDto.getCreateTime());
            //同步人员工号 - 根据姓名匹配工号
            SysUserEntity sysUser = sysUserByNameMap.get(entity.getOperatorName());
            if (Objects.nonNull(sysUser)) {
                entity.setOperator(sysUser.getId());
                entity.setOperatorWno(sysUser.getEmpCode());
            }
            //
            buildingStatusChangeLogEntityList.add(entity);
        }

        return buildingStatusChangeLogEntityList;
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-23 19:39
     * @Description：排除已经写入过的商机动态记录
     */
    private List<BuildingStatusChangeLogEntity> excludeAlreadySavedBusinessLogRecord(List<BuildingStatusChangeLogEntity> buildingStatusChangeLogEntityList) {
        if (CollectionUtils.isEmpty(buildingStatusChangeLogEntityList)) {
            return buildingStatusChangeLogEntityList;
        }
        // 找到涉及的business_id
        Set<String> businessIds = buildingStatusChangeLogEntityList.stream().map(BuildingStatusChangeLogEntity::getBizCode).collect(Collectors.toSet());
        log.info("发现{}条businessId", businessIds.size());
        // 获取已写入的商机动态记录
        Set<String> typeCodeAndStatuses = buildingStatusChangeLogService.list(Wrappers.<BuildingStatusChangeLogEntity>lambdaQuery()
                        .in(BuildingStatusChangeLogEntity::getBizCode, businessIds)
                        .eq(BuildingStatusChangeLogEntity::getType, BuildingStatusChangeLogEntity.BizType.BUSINESS.getCode())
                        .eq(BuildingStatusChangeLogEntity::getDeleteFlag, 0)
                        .select(BuildingStatusChangeLogEntity::getType, BuildingStatusChangeLogEntity::getBizCode, BuildingStatusChangeLogEntity::getStatus))
                .stream().map(e -> e.getType() + e.getBizCode() + e.getStatus()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(typeCodeAndStatuses)) {
            return buildingStatusChangeLogEntityList;
        }
        // 排除出现过的商机动态状态
        List<BuildingStatusChangeLogEntity> newBuildingStatusChangeLogEntityList = Lists.newArrayListWithCapacity(buildingStatusChangeLogEntityList.size());
        for (BuildingStatusChangeLogEntity entity : buildingStatusChangeLogEntityList) {
            if (typeCodeAndStatuses.contains(entity.getType() + entity.getBizCode() + entity.getStatus())) continue;
            newBuildingStatusChangeLogEntityList.add(entity);
        }

        return newBuildingStatusChangeLogEntityList;
    }
}
