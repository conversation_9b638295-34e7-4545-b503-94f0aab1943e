package com.coocaa.meht.module.web.service.approval.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coocaa.meht.common.bean.feishu.FlowNode;
import com.coocaa.meht.module.web.entity.PriceApplyDevicePointEntity;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.coocaa.meht.module.web.entity.PriceApprovalDetailEntity;
import com.coocaa.meht.module.web.entity.PriceApprovalEntity;
import com.coocaa.meht.module.web.entity.PriceApprovalEventRecordEntity;
import com.coocaa.meht.module.web.service.PriceApplyDevicePointService;
import com.coocaa.meht.module.web.service.PriceApplyService;
import com.coocaa.meht.module.web.service.approval.IPriceApprovalDetailService;
import com.coocaa.meht.module.web.service.approval.IPriceApprovalEventRecordService;
import com.coocaa.meht.module.web.service.approval.IPriceApprovalService;
import com.coocaa.meht.utils.FeiShuApprovalUtil;
import com.coocaa.meht.utils.KafkaProducer;
import com.lark.oapi.core.request.EventReq;
import com.lark.oapi.event.EventDispatcher;
import com.lark.oapi.service.approval.v4.model.GetInstanceRespBody;
import com.lark.oapi.service.approval.v4.model.InstanceTask;
import com.lark.oapi.service.approval.v4.model.InstanceTimeline;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;


/**
 * 价格审批服务类
 * 处理价格审批流程相关业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PriceApprovalEventService {

    private final FeiShuApprovalUtil feiShuApprovalUtil;
    private final Executor asyncPool;
    private final IPriceApprovalService priceApprovalService;
    private final IPriceApprovalDetailService priceApprovalDetailService;
    private final IPriceApprovalEventRecordService priceApprovalEventRecordService;
    private final PriceApplyService priceApplyService;
    private final PriceApplyDevicePointService priceApplyDevicePointService;
    private final KafkaProducer kafkaProducer;
    @Value("${price.approval.appId:appId}")
    private String appId;
    @Value("${price.approval.appSecret:appSecret}")
    private String appSecret;
    @Value("${price.approval.approvalCode:approvalCode}")
    private String approvalCode;
    @Value("${price.approval.message.topic:cheese-venue-price-point}")
    private String approvalMessageTopic;
    private EventDispatcher eventDispatcher;

    /**
     * 初始化事件分发器
     */
    @PostConstruct
    public void initEventDispatcher() {

        //订阅审批
        //feiShuApprovalUtil.subscribeApprovalEvent(approvalCode);
        //初始化分发器
//        eventDispatcher = EventDispatcher.newBuilder("", "")
//                .onCustomizedEvent("approval_instance", new CustomEventHandler() {
//                    @Override
//                    public void handle(EventReq event) {
//                        asyncPool.execute(() -> handleApprovalInstanceEvent(event));
//                    }
//                }).onCustomizedEvent("approval_task", new CustomEventHandler() {
//                    @Override
//                    public void handle(EventReq event) {
//                        asyncPool.execute(() -> handleApprovalTaskEvent(event));
//                    }
//                }).onCustomizedEvent("approval", new CustomEventHandler() {
//                    @Override
//                    public void handle(EventReq event) {
//                        asyncPool.execute(() -> handleApprovalEvent(event));
//                    }
//                }).build();

        //建立长连接
//        Client cli = new Client.Builder(appId, appSecret).eventHandler(eventDispatcher).build();
//        cli.start();
    }

    /**
     * 处理审批实例事件，如同意，拒绝等
     */
    private void handleApprovalInstanceEvent(EventReq event) {
        try {
            String eventBody = new String(event.getBody());
            log.info("handleApprovalInstanceEvent，event: {}", eventBody);
            PriceApprovalEventRecordEntity recordEntity = assembleContractApprovalEventRecordEntity(eventBody);
            priceApprovalEventRecordService.save(recordEntity);

        } catch (Exception e) {
            log.error("处理审批事件失败", e);
            //入库失败，打印入库失败日志，再次入库？
            throw new RuntimeException("处理审批事件失败", e);
        }
    }

    /**
     * 处理审批实例事件
     */
    private void handleApprovalTaskEvent(EventReq event) {
        try {
            String eventBody = new String(event.getBody());
            log.info("handleApprovalTaskEvent，event: {}", eventBody);
            PriceApprovalEventRecordEntity recordEntity = assembleContractApprovalEventRecordEntity(eventBody);
            priceApprovalEventRecordService.save(recordEntity);

        } catch (Exception e) {
            log.error("处理审批事件失败", e);
            //入库失败，打印入库失败日志，再次入库？
            throw new RuntimeException("处理审批事件失败", e);
        }
    }

    /**
     * 处理审批结果
     */
    private void handleApprovalEvent(EventReq event) {
        try {
            String eventBody = new String(event.getBody());
            log.info("handleApprovalEvent，event: {}", eventBody);
            // 保存流水信息到 venue_contract_approval_event_record
            PriceApprovalEventRecordEntity recordEntity = assembleContractApprovalEventRecordEntity(eventBody);
            priceApprovalEventRecordService.save(recordEntity);
            // 解析事件内容为对象
            JSONObject jsonObject = JSON.parseObject(eventBody);
            JSONObject eventObj = jsonObject.getJSONObject("event");
            String instanceCode = eventObj.getString("instance_code");
            // 组装成ContractApprovalEntity
            PriceApprovalEntity entity = new PriceApprovalEntity();
            entity.setApprovalInstanceCode(instanceCode);
            Integer approvalStatus = getApprovalStatus(eventObj.getString("event"));
            entity.setApprovalStatus(approvalStatus);
            // 根据instanceCode 更新数据库信息
            priceApprovalService.update(entity, new QueryWrapper<PriceApprovalEntity>().eq("approval_instance_code", instanceCode));

            // 根据InstanceCode 获取 conractId
            PriceApprovalEntity approvalEntity = priceApprovalService.getOne(new QueryWrapper<PriceApprovalEntity>().eq("approval_instance_code", instanceCode));
            if (approvalEntity == null) {
                throw new RuntimeException("审批实例不存在");
            }
            Integer priceApplyId = approvalEntity.getPriceApplyId();


            //更新价格申请状态
            PriceApplyEntity updateBean = new PriceApplyEntity();
            updateBean.setId(priceApplyId);
            if (entity.getApprovalStatus() == 1) {
                // 审批通过
                updateBean.setStatus(PriceApplyEntity.Status.PASSED.getCode());
                priceApplyService.updateById(updateBean);

                //发送点位信息消息
                sendPointMessage(priceApplyId);
            } else if (entity.getApprovalStatus() == -1) {
                // 审批不通过
                updateBean.setStatus(PriceApplyEntity.Status.REJECTED.getCode());
                priceApplyService.updateById(updateBean);

            } else {
                log.warn("审核结果不知");
            }

            try {
                List<FlowNode> approvalFlowList = getApprovalFlowList(approvalEntity);
                priceApprovalDetailService.save(PriceApprovalDetailEntity.builder().priceApplyId(priceApplyId)
                        .approvalInstanceUuid(approvalEntity.getApprovalInstanceUuid())
                        .nodeListString(JSON.toJSONString(approvalFlowList)).build());
            } catch (Exception e) {
                log.warn("存入价格审批流异常", e);
            }
        } catch (Exception e) {
            log.error("处理审批事件失败", e);
            throw new RuntimeException("处理审批事件失败", e);
        }
    }

    /**
     * 将审批流存入数据库
     *
     * @param entity
     */
    private List<FlowNode> getApprovalFlowList(PriceApprovalEntity entity) {
        List<FlowNode> approvalFlowList = new ArrayList<>();

        try {
            if (entity == null) {
                return approvalFlowList;
            }

            String instanceCode = entity.getApprovalInstanceCode();
            GetInstanceRespBody approvalInstance = feiShuApprovalUtil.getApprovalInstance(instanceCode);
            InstanceTimeline[] timeline = approvalInstance.getTimeline();
            if (timeline == null || timeline.length == 0) {
                return approvalFlowList;
            }
            InstanceTask[] taskListArray = approvalInstance.getTaskList();
            List<InstanceTimeline> timeLineList = Arrays.asList(timeline);
            List<InstanceTask> taskList = null;
            if (taskListArray == null || taskListArray.length == 0) {
                taskList = new ArrayList<>();
            } else {
                taskList = Arrays.asList(taskListArray);
            }
            // 获取taskList转换成map形式
            Map<String, InstanceTask> taskMap = taskList.stream()
                    .collect(Collectors.toMap(InstanceTask::getId, task -> task));

            //根据openId获取飞书用户信息
            List<String> openIds = timeLineList.stream().map(InstanceTimeline::getOpenId)
                    .filter(userId -> userId != null && !userId.isEmpty()).collect(Collectors.toList());
            Map<String, String> userNames = feiShuApprovalUtil.getUserInfoByUserIds(openIds);


            for (InstanceTimeline node : timeLineList) {
                FlowNode flowNode = new FlowNode();
                String openId = node.getOpenId();

                String type = node.getType();
                String taskId = node.getTaskId();

                InstanceTask instanceTask = null;

                //节点名称
                if (StringUtils.isEmpty(taskId)) {
                    if (StringUtils.equals(type, "START")) {
                        flowNode.setNodeName("提交申请");
                    } else {
                        log.info("taskId为空，type为{}", type);
                        continue;
                    }
                } else {
                    instanceTask = taskMap.get(taskId);
                    //审核名称
                    if (instanceTask != null) {
                        flowNode.setNodeName(instanceTask.getNodeName());
                    }
                }

                // 操作人
                flowNode.setOperator(userNames.get(openId));

                // 操作时间
                String formattedTime = formatTimestamp(node.getCreateTime());
                flowNode.setOperateTime(formattedTime);

                //审核意见
                flowNode.setComment(node.getComment());

                //审批状态
                if (instanceTask != null) {
                    flowNode.setStatus(StringUtils.equals(type, "START") ? "" : convertApprovalStatus(instanceTask.getStatus()));
                }
                approvalFlowList.add(flowNode);
            }
            // 获取taskListArray中最后一个对象
            if (taskList.size() == 0) {
                return approvalFlowList;
            }
            InstanceTask lastTask = taskList.get(taskList.size() - 1);
            // timeLineList转换taskIdSet
            Set<String> taskIdSet = timeLineList.stream().map(InstanceTimeline::getTaskId).collect(Collectors.toSet());
            if (lastTask != null && !taskIdSet.contains(lastTask.getId())) {
                FlowNode flowNode = new FlowNode();
                flowNode.setNodeName(lastTask.getNodeName());
                flowNode.setOperator(userNames.get(lastTask.getOpenId()));
                String formattedTime = formatTimestamp(lastTask.getStartTime());
                flowNode.setOperateTime(formattedTime);
                flowNode.setStatus(convertApprovalStatus(lastTask.getStatus()));
                approvalFlowList.add(flowNode);
            }
        } catch (Exception e) {
            log.error("存入审批流异常");
        }
        return approvalFlowList;


    }

    private String formatTimestamp(String timestamp) {
        // 将时间戳转换为 yyyy/M/d HH:mm:ss 格式
        long time = Long.parseLong(timestamp);
        return new SimpleDateFormat("yyyy/M/d HH:mm:ss").format(new Date(time));
    }

    private String convertApprovalStatus(String type) {
        switch (type) {
            case "PENDING":
                return "进行中";
            case "APPROVED":
                return "已同意";
            case "REJECTED":
                return "已拒绝";
            case "TRANSFERRED":
                return "已转交";
            case "DONE":
                return "已完成";
            default:
                return type;
        }
    }

    /**
     * 发送消息
     *
     * @param priceApplyId
     */
    public void sendPointMessage(Integer priceApplyId) {
        PriceApplyEntity priceApplyEntity = priceApplyService.getById(priceApplyId);
        List<PriceApplyDevicePointEntity> deviceList = priceApplyDevicePointService.lambdaQuery()
                .eq(PriceApplyDevicePointEntity::getApplyId, priceApplyId).list();

        if (CollectionUtils.isEmpty(deviceList)) {
            log.warn("审批通过，但是点位为空。priceApplyId:{}", priceApplyId);
            return;
        }
        List<String> points = deviceList.stream().map(bean -> bean.getPointCode()).collect(Collectors.toList());
        JSONObject root = new JSONObject();
        root.put("status", "0025-4");

        JSONArray projects = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("code", priceApplyEntity.getBusinessCode());
        item.put("name", priceApplyEntity.getBuildingName());
        item.put("pointCodes", JSONArray.copyOf(points));
        projects.add(item);

        root.put("projects", projects);
        kafkaProducer.sendMessage(approvalMessageTopic, root.toString());

    }

    /**
     * 组装消息推送信息
     *
     * @param eventBody
     * @return
     */
    private PriceApprovalEventRecordEntity assembleContractApprovalEventRecordEntity(String eventBody) {
        // 解析eventBody
        JSONObject jsonObject = JSON.parseObject(eventBody);
        JSONObject eventObj = jsonObject.getJSONObject("event");

        PriceApprovalEventRecordEntity entity = new PriceApprovalEventRecordEntity();
        entity.setUuid(jsonObject.getString("uuid"));
        entity.setToken(jsonObject.getString("token"));
        entity.setTs(jsonObject.getString("ts"));
        entity.setType(jsonObject.getString("type"));
        entity.setAppId(eventObj.getString("app_id"));
        entity.setApprovalCode(eventObj.getString("approval_code"));
        entity.setDefinitionName(eventObj.getString("definition_name"));
        entity.setCustomKey(eventObj.getString("custom_key"));
        entity.setDefKey(eventObj.getString("def_key"));
        entity.setGenerateType(eventObj.getString("generate_type"));
        entity.setInstanceCode(eventObj.getString("instance_code"));
        entity.setOpenId(eventObj.getString("open_id"));
        entity.setOperateTime(eventObj.getString("operate_time"));
        entity.setStatus(eventObj.getString("status"));
        entity.setTaskId(eventObj.getString("task_id"));
        entity.setTenantKey(eventObj.getString("tenant_key"));
        entity.setType(eventObj.getString("type"));
        entity.setUserId(eventObj.getString("user_id"));
        entity.setDefinitionCode(eventObj.getString("definition_code"));
        entity.setEndTime(eventObj.getLong("end_time"));
        entity.setEvent(eventObj.getString("event"));
        entity.setInstanceOperateTime(eventObj.getString("instance_operate_time"));
        entity.setStartTime(eventObj.getLong("start_time"));
        return entity;
    }

    /**
     * 获取审批结果
     */
    private Integer getApprovalStatus(String approveResult) {
        // 审批结果
        if (approveResult.equals("approve")) {
            return 1;
        } else if (approveResult.equals("reject")) {
            return -1;
        } else {
            return 0;
        }
    }

    public void fsApprovalManually(Integer priceApplyId, Integer result) {
        //更新价格申请状态
        PriceApplyEntity updateBean = new PriceApplyEntity();
        updateBean.setId(priceApplyId);
        if (result == 1) {
            // 审批通过
            updateBean.setStatus(PriceApplyEntity.Status.PASSED.getCode());
            priceApplyService.updateById(updateBean);

            //发送点位信息消息
            sendPointMessage(priceApplyId);
        } else if (result == -1) {
            // 审批不通过
            updateBean.setStatus(PriceApplyEntity.Status.REJECTED.getCode());
            priceApplyService.updateById(updateBean);

        } else {
            log.warn("审核结果不知");
        }

    }
}
