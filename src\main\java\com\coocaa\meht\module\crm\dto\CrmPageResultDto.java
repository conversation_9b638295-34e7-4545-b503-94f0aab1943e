package com.coocaa.meht.module.crm.dto;

import com.coocaa.meht.module.web.dto.crm.CrmResultDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;


@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CrmPageResultDto extends CrmResultDto {

    private DataDto data;

    @Data
    @Accessors(chain = true)
    public static class DataDto {
        private Integer totalRow;
        private Integer pageSize;
        private Integer pageNumber;

        private List<Map<String,Object>> list;
    }

}
