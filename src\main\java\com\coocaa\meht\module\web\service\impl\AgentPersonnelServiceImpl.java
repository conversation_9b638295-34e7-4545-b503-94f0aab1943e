package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.AgentPersonnelDao;
import com.coocaa.meht.module.web.dto.AgentPersonnelDto;
import com.coocaa.meht.module.web.entity.AgentPersonnelEntity;
import com.coocaa.meht.module.web.service.AgentPersonnelService;
import org.apache.groovy.util.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.naming.CommunicationException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-11-08 15:20
 */
@Service
public class AgentPersonnelServiceImpl extends ServiceImpl<AgentPersonnelDao, AgentPersonnelEntity> implements AgentPersonnelService {

//    @Autowired
//    private  AgentPersonnelConvert agentPersonnelConvert;

    @Autowired
    private AgentPersonnelDao agentPersonnelDao;

    @Override
    public AgentPersonnelEntity getByPhone(String phone) {
        List<AgentPersonnelEntity> list = this.listByMap(Maps.of("emp_mobile", phone));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public AgentPersonnelEntity getByEmpCode(String empCode) {
        AgentPersonnelEntity agentPersonnelByCode = agentPersonnelDao.getAgentPersonnelByCode(empCode);
        return agentPersonnelByCode;
    }

    @Override
    public List<AgentPersonnelEntity> getByEmpCodes(List<String> empCodes) {
        if (CollectionUtils.isEmpty(empCodes)) {
            return new ArrayList<>();
        }
        List<AgentPersonnelEntity> agentPersonnelsByCodes = agentPersonnelDao.getAgentPersonnelsByCodes(empCodes);
        return agentPersonnelsByCodes;
    }

    @Override
    public boolean addAgentPersonnel(AgentPersonnelDto dto) throws CommunicationException {
//        AgentPersonnelEntity agentPersonnelEntity=  agentPersonnelConvert.INSTANCE.addDtoToEntity(dto);
//         Long exist=   this.lambdaQuery().or().eq(AgentPersonnelEntity::getEmpCode,dto.getEmpCode())
//                    .or().eq(AgentPersonnelEntity::getEmpMobile,dto.getEmpMobile()).count();
//         if(exist>0){
//             throw new CommunicationException("代理商已存在，创建失败");
//         }
//        agentPersonnelEntity.setCreateTime(LocalDateTime.now());
//        agentPersonnelEntity.setUpdateTime(LocalDateTime.now());
//
//        return this.save(agentPersonnelEntity);
        return true;
    }

    @Override
    public boolean updateStatus(AgentPersonnelDto dto) {
        return false;
    }
}
