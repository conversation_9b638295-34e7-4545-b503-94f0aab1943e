package com.coocaa.meht.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * mybatis-plus 配置
 */
@Configuration
public class MybatisPlusConfig {

    @Bean
    public MybatisSimpleInterceptor mybatisPlusInterceptor() {
        MybatisSimpleInterceptor mybatisPlusInterceptor = new MybatisSimpleInterceptor();
        // 数据权限
        //mybatisPlusInterceptor.addInnerInterceptor(new DataScopeInnerInterceptor());
        // 分页插件
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        // 乐观锁
        mybatisPlusInterceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        // 防止全表更新与删除
        mybatisPlusInterceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
        return mybatisPlusInterceptor;
    }

    @Bean
    public FieldMetaObjectHandler fieldMetaObjectHandler(){
        return new FieldMetaObjectHandler();
    }

    public static class FieldMetaObjectHandler implements MetaObjectHandler {
        private final static String CREATE_TIME = "createTime";
        private final static String CREATOR = "createBy";
        private final static String UPDATE_TIME = "updateTime";
        private final static String UPDATER = "updateBy";

        @Override
        public void insertFill(MetaObject metaObject) {
            String userCode = UserThreadLocal.getUser().getWno();
            userCode = Objects.nonNull(userCode) ? userCode : "";
            LocalDateTime date = LocalDateTime.now();
            strictInsertFill(metaObject, CREATOR, String.class, userCode);
            strictInsertFill(metaObject, CREATE_TIME, LocalDateTime.class, date);
            strictInsertFill(metaObject, UPDATER, String.class, userCode);
            strictInsertFill(metaObject, UPDATE_TIME, LocalDateTime.class, date);
        }

        @Override
        public void updateFill(MetaObject metaObject) {
            String userCode = UserThreadLocal.getUser().getWno();
            userCode = Objects.nonNull(userCode) ? userCode : "";
            strictUpdateFill(metaObject, UPDATER, String.class, userCode);
            strictUpdateFill(metaObject, UPDATE_TIME, LocalDateTime.class, LocalDateTime.now());
        }
    }

}