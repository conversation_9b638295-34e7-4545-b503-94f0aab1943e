package com.coocaa.meht.rpc;


import com.coocaa.ad.common.config.FeignConfig;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.proxy.CsBrandPointQueryParam;
import com.coocaa.meht.module.web.vo.proxy.CsBrandPointDataWrapperVO;
import com.coocaa.meht.rpc.dto.BuildingTopEntity;
import com.coocaa.meht.rpc.dto.BuildingTopQueryParam;
import com.coocaa.meht.rpc.dto.CoreAreaJudgeParam;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 合同CMS交互接口
 */
@FeignClient(value = "cheese-meht-web-api", configuration = FeignConfig.class)
public interface FeignMehtWebRpc {
    @PostMapping("/api/meht/external/proxy/cs/data/point-analysis/brand-point")
    ResultTemplate<CsBrandPointDataWrapperVO> getCsBrandPoint(@RequestBody CsBrandPointQueryParam param);

    @PostMapping("/api/meht/building-top/get-unique")
    @Operation(summary = "查询唯一top楼宇")
    ResultTemplate<BuildingTopEntity> getUnique(@RequestBody BuildingTopQueryParam queryParam);

    /**
     * 获取负责人下的可用客户数
     */
    @GetMapping("/api/meht/user-tags/{empCode}/available")
    ResultTemplate<Long> getAvailableUserCount(@PathVariable("empCode") String empCode);

    @Operation(summary = "核心区域判断")
    @PostMapping("/api/meht/core-area/judge")
    ResultTemplate<Boolean> judgeCoreArea(@RequestBody CoreAreaJudgeParam param);

}
