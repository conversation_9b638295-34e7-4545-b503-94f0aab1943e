package com.coocaa.meht.module.crm.enums;

/**
 * <AUTHOR>
 * @date 2025/2/17
 * @description 数据权限类型
 */

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据权限类型
 */
@Getter
@AllArgsConstructor
public enum DataAccessTypeEnum {
    SELF("0048-1", "自己"),
    SELF_AND_SUB("0048-2", "自己及下属"),
    CITY_ALL("0048-3", "公司&城市所有"),
    ALL("0048-4", "全部");

    private final String code;
    private final String desc;

    private final static Map<String, DataAccessTypeEnum> BY_CODE_MAP =
            Arrays.stream(DataAccessTypeEnum.values())
                    .collect(Collectors.toMap(DataAccessTypeEnum::getCode, item -> item));


    /**
     * 将代码转成枚举
     */
    public static DataAccessTypeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static DataAccessTypeEnum parse(String code, DataAccessTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }



}
