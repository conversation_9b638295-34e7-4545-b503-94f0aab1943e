package com.coocaa.meht.config;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.dto.CityVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-05-19
 */

@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "audit.config")
@EnableConfigurationProperties
@Slf4j
public class AuditConfigProperties {

    private static final Integer ADMIN_ID = 0;

    /**
     * 评级审核配置 -大屏
     */
    private Map<String, String> largeRating;
    /**
     * 评级审核配置 -小屏
     */
    private Map<String, String> smallRating;

    public List<String> getRatingAuditUsers(boolean large, String city) {
        // 如果large为true，则获取largeRating中city对应的值
        if (large) {
            String largeRatingValue = largeRating.getOrDefault(city, "");
            // 如果largeRating中不存在city对应的值，则返回空列表
            if (StrUtil.isBlank(largeRatingValue)) {
                return Collections.emptyList();
            }
            // 将largeRating中city对应的值按逗号分隔，并返回列表
            return Arrays.stream(largeRatingValue.trim().split(",")).toList();
        }
        // 获取FeignCmsRpc的实例
        FeignCmsRpc cmsRpc = SpringUtil.getBean(FeignCmsRpc.class);
        // 如果FeignCmsRpc为空，则抛出异常
        if (cmsRpc == null) {
            throw new RuntimeException("FeignCmsRpc is null");
        }
        // 调用RpcUtils.unBox方法，获取city对应的CityVO列表
        List<CityVO> cityVOS = RpcUtils.unBox(cmsRpc.getBusinessHead(Set.of(city)));
        // 如果cityVOS为空或者配置了CC0000，则返回空列表
        if (cityVOS.isEmpty() || ADMIN_ID.equals(cityVOS.get(0).getBusinessHead()) || cityVOS.get(0).getBusinessHead() == null) {
            return Collections.emptyList();
        }

        // 获取FeignAuthorityRpc的实例
        FeignAuthorityRpc authorityRpc = SpringUtil.getBean(FeignAuthorityRpc.class);
        // 调用RpcUtils.unBox方法，获取cityVOS中第一个元素的businessHead对应的CodeNameVO列表
        List<CodeNameVO> users = RpcUtils.unBox(authorityRpc.listUserByIds(Collections.singletonList(cityVOS.get(0).getBusinessHead())));
        // 如果users为空或者users中第一个元素的wno为空，则返回空列表
        if (users.isEmpty() || users.get(0).getWno() == null) {
            return Collections.emptyList();
        }
        // 将users中第一个元素的wno按逗号分隔，并返回列表
        return Arrays.stream(users.get(0).getWno().trim().split(",")).toList();
//        // 如果large为true，则返回largeRating中city对应的值按逗号分隔的列表，否则返回smallRating中city对应的值按逗号分隔的列表
//        return large
//                ? Arrays.stream(largeRating.get(city).trim().split(",")).toList()
//                : Arrays.stream(smallRating.get(city).trim().split(",")).toList();
    }

}
