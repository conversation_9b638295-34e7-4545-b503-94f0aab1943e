package com.coocaa.meht.module.approve.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.dto.ApprovalOperationDTO;
import com.coocaa.meht.module.approve.dto.InnerApproveTemplateParam;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.module.approve.enums.ApproveFieldTypeEnum;
import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;
import com.coocaa.meht.module.approve.strategy.ApprovalStrategy;
import com.coocaa.meht.module.building.convert.BuildingDetailsConvert;
import com.coocaa.meht.module.building.convert.BuildingScreenConvert;
import com.coocaa.meht.module.building.entity.BuildingScreenEntity;
import com.coocaa.meht.module.building.entity.CompleteBuildingScreenEntity;
import com.coocaa.meht.module.building.entity.CompleteRatingDetailEntity;
import com.coocaa.meht.module.building.entity.CompleteRatingEntity;
import com.coocaa.meht.module.building.enums.LargeScreenFlagEnum;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.building.service.CompleteBuildingScreenService;
import com.coocaa.meht.module.building.service.CompleteRatingDetailService;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.building.vo.RatingVO;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.module.web.dao.ScreenApproveRecordMapper;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaImgRelationEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BuildingSnapshotEntity;
import com.coocaa.meht.module.web.entity.ScreenApproveRecordEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BuildingSnapshotService;
import com.coocaa.meht.module.web.service.IBuildingMetaImgRelationService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.utils.CodeGenerator;
import com.coocaa.meht.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-18
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CompleteRatingApprovalStrategy implements ApprovalStrategy {

    private final CompleteRatingService completeRatingService;

    private final CompleteBuildingScreenService completeBuildingScreenService;

    private final CompleteRatingDetailService completeRatingDetailService;

    private final BuildingSnapshotService buildingSnapshotService;

    private final BuildingRatingService buildingRatingService;

    private final BuildingDetailsService buildingDetailsService;

    private final BuildingScreenService buildingScreenService;

    private final IBuildingMetaService buildingMetaService;

    private final ScreenApproveRecordMapper screenApproveRecordMapper;

    private final IBuildingMetaImgRelationService imgRelationService;

    private final SysFileService sysFileService;

    private final CodeGenerator codeGenerator;

    private final RedisUtils redisUtils;


    @Override
    public String getApproveType() {
        return ApprovalTypeEnum.COMPLETE_BUILDING_APPROVAL.getCode();
    }

    @Override
    public void validateData(ApprovalDTO data) {

    }

    @Override
    public List<InnerApproveTemplateParam> buildFormData(ApprovalDTO data) {
        CompleteRatingEntity completeRating = completeRatingService.lambdaQuery()
                .eq(CompleteRatingEntity::getCompleteRatingNo, data.getBusinessKey())
                .one();

        CompleteBuildingScreenEntity screenEntity = completeBuildingScreenService.lambdaQuery()
                .eq(CompleteBuildingScreenEntity::getCompleteRatingNo, data.getBusinessKey())
                .one();

        String text = completeBuildingScreenService.isScreenLarge(screenEntity) ? "large" : "small";

        return List.of(
                InnerApproveTemplateParam.builder()
                        .key("city")
                        .type(ApproveFieldTypeEnum.CHARACTER.getCode())
                        .value(completeRating.getMapCity())
                        .build(),
                InnerApproveTemplateParam.builder()
                        .key("screenType")
                        .type(ApproveFieldTypeEnum.CHARACTER.getCode())
                        .value(text)
                        .build()
        );

    }

    @Override
    public void onFinish(String businessKey, ApprovalOperationDTO operationDTO) {
        log.info("处理楼宇审批通过, businessKey: {}, operationDTO: {}", businessKey, operationDTO);

        CompleteRatingEntity completeRating = completeRatingService.lambdaQuery()
                .eq(CompleteRatingEntity::getCompleteRatingNo, businessKey)
                .one();
        completeRating.setStatus(BuildingRatingEntity.Status.AUDITED.getValue());
        completeRatingService.updateById(completeRating);

        //快照处理
        ratingSnapshot(businessKey);

        //修改主数据
        modifyData(businessKey);
    }

    private void modifyData(String businessKey) {
        CompleteRatingEntity completeRating = completeRatingService.lambdaQuery()
                .eq(CompleteRatingEntity::getCompleteRatingNo, businessKey)
                .one();

        BuildingRatingEntity buildingRating = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, completeRating.getBuildingRatingNo())
                .one();

        // 改buildingRating
        buildingRating.setBuildingScore(completeRating.getBuildingScore());
        buildingRating.setTargetPointCount(completeRating.getTargetPointCount());
        if (StringUtils.isNotBlank(completeRating.getProjectReviewLevel())) {
            buildingRating.setProjectReviewLevel(completeRating.getProjectReviewLevel());
        }
        buildingRating.setProjectLevel(completeRating.getProjectLevel());
        buildingRating.setBuildingAiScore(completeRating.getBuildingAiScore());
        buildingRating.setProjectAiLevel(completeRating.getProjectAiLevel());
        buildingRating.setFirstFloorExclusive(completeRating.getFirstFloorExclusive());
        buildingRating.setFirstFloorShare(completeRating.getFirstFloorShare());
        buildingRating.setNegativeFirstFloor(completeRating.getNegativeFirstFloor());
        buildingRating.setNegativeTwoFloor(completeRating.getNegativeSecondFloor());
        buildingRating.setTwoFloorAbove(completeRating.getSecondFloorAbove());
        buildingRating.setThirdFloorBelow(completeRating.getThirdFloorBelow());
        buildingRating.setBuildingExteriorPic(completeRating.getBuildingExteriorPic());
        buildingRating.setBuildingLobbyPic(completeRating.getBuildingLobbyPic());
        buildingRating.setBuildingHallPic(completeRating.getBuildingHallPic());
        buildingRating.setBuildingElevatorPic(completeRating.getBuildingElevatorPic());
        buildingRating.setBuildingGatePic(completeRating.getBuildingGatePic());
        buildingRating.setBuildingInstallationPic(completeRating.getBuildingInstallationPic());
        buildingRating.setUpdateTime(buildingRating.getUpdateTime());
        buildingRating.setRatingVersion(codeGenerator.generateRatingVersion());
        buildingRating.setDataFlag(completeRating.getDataFlag());
        buildingRatingService.updateById(buildingRating);

        //改building_meta
        BuildingMetaEntity buildingMetaEntity = buildingMetaService.lambdaQuery()
                .eq(BuildingMetaEntity::getBuildingRatingNo, buildingRating.getBuildingNo())
                .one();
        buildingMetaEntity.setBuildingScore(completeRating.getBuildingScore());
        buildingMetaEntity.setProjectLevel(StringUtils.isNotBlank(buildingRating.getProjectReviewLevel()) ? buildingRating.getProjectReviewLevel() : buildingRating.getProjectLevel());
        buildingMetaEntity.setProjectLevelAi(completeRating.getProjectAiLevel());
        buildingMetaEntity.setBuildingAiScore(completeRating.getBuildingAiScore());
        buildingMetaService.updateById(buildingMetaEntity);
        //图片处理
        updateMetaPic(completeRating, buildingMetaEntity);

        //改BuildingDetailsEntity
        BuildingDetailsEntity buildingDetailsEntity = buildingDetailsService.lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, buildingRating.getBuildingNo())
                .one();
        CompleteRatingDetailEntity completeRatingDetail = completeRatingDetailService.lambdaQuery()
                .eq(CompleteRatingDetailEntity::getCompleteRatingNo, completeRating.getCompleteRatingNo())
                .one();
        BuildingDetailsEntity buildingDetails = BuildingDetailsConvert.INSTANCE.toRatingDetailEntity(completeRatingDetail);
        if (Objects.nonNull(buildingDetailsEntity)) {
            buildingDetails.setBuildingNo(buildingDetailsEntity.getBuildingNo());
            buildingDetails.setId(buildingDetailsEntity.getId());
            buildingDetailsService.updateById(buildingDetails);
        } else {
            buildingDetails.setId(null);
            buildingDetails.setBuildingNo(completeRating.getBuildingRatingNo());
            buildingDetailsService.save(buildingDetails);
        }

        //修改BuildingScreen
        BuildingScreenEntity buildingScreen = buildingScreenService.lambdaQuery()
                .eq(BuildingScreenEntity::getBuildingRatingNo, buildingRating.getBuildingNo())
                .one();

        CompleteBuildingScreenEntity completeBuildingScreen = completeBuildingScreenService.lambdaQuery()
                .eq(CompleteBuildingScreenEntity::getCompleteRatingNo, businessKey)
                .one();
        BuildingScreenEntity buildingScreenEntity = BuildingScreenConvert.INSTANCE.toBuildingScreenEntity(completeBuildingScreen);
        if (Objects.nonNull(buildingScreen)) {
            buildingScreenEntity.setId(buildingScreen.getId());
            buildingScreenEntity.setBuildingRatingNo(buildingScreen.getBuildingRatingNo());
            buildingScreenService.updateById(buildingScreenEntity);
        } else {
            buildingScreenEntity.setId(null);
            buildingScreenEntity.setBuildingRatingNo(buildingRating.getBuildingNo());
            buildingScreenService.save(buildingScreenEntity);
        }


    }


    private void updateMetaPic(CompleteRatingEntity completeRating, BuildingMetaEntity buildingMetaEntity) {
        //存储图片
        imgRelationService.remove(Wrappers.<BuildingMetaImgRelationEntity>lambdaQuery()
                .eq(BuildingMetaImgRelationEntity::getBuildingMetaNo, buildingMetaEntity.getBuildingMetaNo()));

        //楼盘大堂
        String buildingLobbyPic = completeRating.getBuildingLobbyPic();
        List<BuildingMetaImgRelationEntity> imgList = new ArrayList<>(saveMetaImg(buildingLobbyPic, 2, buildingMetaEntity.getBuildingMetaNo()));
        //侯梯厅
        String buildingHallPic = completeRating.getBuildingHallPic();
        imgList.addAll(saveMetaImg(buildingHallPic, 3, buildingMetaEntity.getBuildingMetaNo()));
        //外墙材料
        String buildingExteriorPic = completeRating.getBuildingExteriorPic();
        imgList.addAll(saveMetaImg(buildingExteriorPic, 1, buildingMetaEntity.getBuildingMetaNo()));


        String buildingLobbyEnvPic = completeRating.getBuildingLobbyEnvPic();
        imgList.addAll(saveMetaImg(buildingLobbyEnvPic, 4, buildingMetaEntity.getBuildingMetaNo()));

        String buildingElevatorPic = completeRating.getBuildingElevatorPic();
        imgList.addAll(saveMetaImg(buildingElevatorPic, 5, buildingMetaEntity.getBuildingMetaNo()));

        String buildingGatePic = completeRating.getBuildingGatePic();
        imgList.addAll(saveMetaImg(buildingGatePic, 6, buildingMetaEntity.getBuildingMetaNo()));

        String buildingInstallationPic = completeRating.getBuildingInstallationPic();
        imgList.addAll(saveMetaImg(buildingInstallationPic, 7, buildingMetaEntity.getBuildingMetaNo()));


        if (ObjectUtil.isNotEmpty(imgList)) {
            imgRelationService.saveBatch(imgList);
        }

    }

    public List<BuildingMetaImgRelationEntity> saveMetaImg(String ids, Integer imgType, String buildingMetaNo) {
        List<BuildingMetaImgRelationEntity> imgRelationEntityList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(ids)) {
            List<String> list = Arrays.asList(ids.split(","));
            List<Long> fileIds = list.stream().map(Long::valueOf).collect(Collectors.toList());
            List<SysFileEntity> bySysFileIdList = sysFileService.getBySysFileIdList(fileIds);
            if (ObjectUtil.isNotEmpty(bySysFileIdList)) {
                bySysFileIdList.forEach(file -> {
                    BuildingMetaImgRelationEntity entity = new BuildingMetaImgRelationEntity();
                    entity.setImgUrl(file.getUrl());
                    entity.setImgType(imgType);
                    entity.setCreateBy(SecurityUser.getUserCode());
                    entity.setBuildingMetaNo(buildingMetaNo);
                    imgRelationEntityList.add(entity);
                });
            }
        }
        return imgRelationEntityList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onRejected(String businessKey, ApprovalOperationDTO operationDTO) {
        CompleteRatingEntity completeRatingEntity = completeRatingService.lambdaQuery()
                .eq(CompleteRatingEntity::getCompleteRatingNo, businessKey)
                .one();
        completeRatingEntity.setStatus(BuildingRatingEntity.Status.FAILED_AUDIT.getValue());
        completeRatingService.updateById(completeRatingEntity);

        //处理快照
        snapshot(businessKey);

        //处理主数据标识
        updateScreenRatingFlag(completeRatingEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onRevoke(String businessKey, ApprovalOperationDTO operationDTO) {
        CompleteRatingEntity completeRatingEntity = completeRatingService.lambdaQuery()
                .eq(CompleteRatingEntity::getCompleteRatingNo, businessKey)
                .one();

        //撤回草稿详情处理
        updateDraft(completeRatingEntity);

        //修改主数据标识
        updateScreenRatingFlag(completeRatingEntity);
    }

    //撤回存草稿
    private void updateDraft(CompleteRatingEntity completeRatingEntity) {

        //处理草稿
        RatingVO ratingVO = completeRatingService.getCompleteInfoByNo(completeRatingEntity);

        //修改状态
        UpdateWrapper<CompleteRatingEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("status", BuildingRatingEntity.Status.DRAFT.getValue());
        updateWrapper.set("draft_text", Objects.nonNull(ratingVO) ? JSON.toJSONString(ratingVO) : null);
        updateWrapper.eq("complete_rating_no", completeRatingEntity.getCompleteRatingNo());
        completeRatingService.update(updateWrapper);

    }

    /**
     * 修改主数据标识
     */
    private void updateScreenRatingFlag(CompleteRatingEntity completeRatingEntity) {
        BuildingRatingEntity buildingRating = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, completeRatingEntity.getBuildingRatingNo())
                .one();
        LocalDateTime updateTime = buildingRating.getUpdateTime();
        Integer smallFlag = buildingRating.getSmallScreenRatingFlag();
        Integer largeFlag = buildingRating.getLargeScreenRatingFlag();
        //处理主数据标识
        if (completeRatingEntity.getLargeScreenFlag().equals(LargeScreenFlagEnum.SCREEN_FLAG_SMALL.getCode())) {
            smallFlag = BooleFlagEnum.NO.getCode();
        } else if (completeRatingEntity.getLargeScreenFlag().equals(LargeScreenFlagEnum.SCREEN_FLAG_LARGE.getCode())) {
            largeFlag = BooleFlagEnum.NO.getCode();
        } else if (completeRatingEntity.getLargeScreenFlag().equals(LargeScreenFlagEnum.SCREEN_FLAG_BOTH.getCode())) {

            largeFlag = BooleFlagEnum.NO.getCode();
            smallFlag = BooleFlagEnum.NO.getCode();
        }

        buildingRatingService.lambdaUpdate()
                .set(BuildingRatingEntity::getSmallScreenRatingFlag, smallFlag)
                .set(BuildingRatingEntity::getLargeScreenRatingFlag, largeFlag)
                .set(BuildingRatingEntity::getUpdateTime, updateTime)
                .eq(BuildingRatingEntity::getBuildingNo, completeRatingEntity.getBuildingRatingNo())
                .update();
    }

    @Override
    public void beforeSubmit(ApprovalDTO dto) {
        ApprovalStrategy.super.beforeSubmit(dto);
    }

    @Override
    public void afterSubmit(ApprovalDTO dto, String instanceCode) {
        ApprovalStrategy.super.afterSubmit(dto, instanceCode);
    }

    @Override
    public void beforeApprove(ApprovalOperationDTO operationDTO) {
        log.info("楼宇完善审批通过前回调, operationDTO: {}", operationDTO);

        CompleteBuildingScreenEntity completeBuildingScreen = completeBuildingScreenService.lambdaQuery()
                .eq(CompleteBuildingScreenEntity::getCompleteRatingNo, operationDTO.getBusinessKey())
                .one();
        if (Objects.isNull(completeBuildingScreen)) {
            log.warn("数据异常，building_screen表数据不存在, buildingNo: {}", operationDTO.getBusinessKey());
            throw new ApprovalBusinessException("数据异常");
        }

        if (!completeBuildingScreenService.isScreenLarge(completeBuildingScreen)) {
            // 小屏忽略校验
            return;
        }

        // 大屏复核系数校验
        if (CollUtil.isEmpty(operationDTO.getExtData()) || Objects.isNull(operationDTO.getExtData().get("finalCoefficient"))) {
            throw new ApprovalBusinessException("未选择大屏复核系数");
        }
    }

    @Override
    public void afterApprove(ApprovalOperationDTO operationDTO) {
        CompleteBuildingScreenEntity completeBuildingScreen = completeBuildingScreenService.lambdaQuery()
                .eq(CompleteBuildingScreenEntity::getCompleteRatingNo, operationDTO.getBusinessKey())
                .one();
        Boolean screenLarge = completeBuildingScreenService.isScreenLarge(completeBuildingScreen);
        if (screenLarge) {
            Object finalCoefficientObj = operationDTO.getExtData().get("finalCoefficient");
            if (finalCoefficientObj != null && StrUtil.isNotBlank(finalCoefficientObj.toString())) {
                try {
                    BigDecimal finalCoefficient = new BigDecimal(finalCoefficientObj.toString());

                    completeBuildingScreen.setFinalCoefficient(finalCoefficient);
                    completeBuildingScreenService.updateById(completeBuildingScreen);

                    ScreenApproveRecordEntity screenApproveRecordEntity = screenApproveRecordMapper.selectByTaskId(operationDTO.getTaskId());
                    if (screenApproveRecordEntity == null) {
                        log.warn("未找到与taskId对应的审批记录, taskId: {}", operationDTO.getTaskId());
                        return;
                    }
                    screenApproveRecordEntity.setFinalCoefficient(finalCoefficient);
                    screenApproveRecordMapper.updateById(screenApproveRecordEntity);
                    log.info("更新楼宇审批记录-系数: {}, taskId: {}", screenApproveRecordEntity.getFinalCoefficient(), operationDTO.getTaskId());
                } catch (NumberFormatException e) {
                    log.warn("finalCoefficient格式无效，无法转换为BigDecimal: [{}], taskId: {}", finalCoefficientObj, operationDTO.getTaskId());
                }
            }

        }
    }


    private void snapshot(String businessKey) {
        CompleteRatingEntity completeRatingEntity = completeRatingService.lambdaQuery()
                .eq(CompleteRatingEntity::getCompleteRatingNo, businessKey)
                .one();

        String completeRatingStr = JSON.toJSONString(completeRatingEntity, JSONWriter.Feature.WriteMapNullValue);

        CompleteRatingDetailEntity completeRatingDetail = completeRatingDetailService.lambdaQuery()
                .eq(CompleteRatingDetailEntity::getCompleteRatingNo, businessKey)
                .one();

        String completeRatingDetailStr = JSON.toJSONString(completeRatingDetail, JSONWriter.Feature.WriteMapNullValue);

        CompleteBuildingScreenEntity completeBuildingScreen = completeBuildingScreenService.lambdaQuery()
                .eq(CompleteBuildingScreenEntity::getCompleteRatingNo, businessKey)
                .one();

        String completeBuildingScreenStr = JSON.toJSONString(completeBuildingScreen, JSONWriter.Feature.WriteMapNullValue);

        BuildingSnapshotEntity buildingSnapshotEntity = new BuildingSnapshotEntity();
        buildingSnapshotEntity.setBuildingRatingNo(businessKey);
        buildingSnapshotEntity.setMetaSnapshot("");
        buildingSnapshotEntity.setRatingSnapshot(completeRatingStr);
        buildingSnapshotEntity.setDetailsSnapshot(completeRatingDetailStr);
        buildingSnapshotEntity.setScreenSnapshot(completeBuildingScreenStr);
        buildingSnapshotEntity.setType(BuildingSnapshotEntity.Type.FAILED_AUDIT.getValue());
        buildingSnapshotEntity.setRatingVersion(completeRatingEntity.getVersion());
        buildingSnapshotService.save(buildingSnapshotEntity);
    }

    private void ratingSnapshot(String businessKey) {
        CompleteRatingEntity completeRatingEntity = completeRatingService.lambdaQuery()
                .eq(CompleteRatingEntity::getCompleteRatingNo, businessKey)
                .one();

        BuildingRatingEntity buildingRating = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, completeRatingEntity.getBuildingRatingNo())
                .one();

        String ratingStr = JSON.toJSONString(buildingRating, JSONWriter.Feature.WriteMapNullValue);

        BuildingDetailsEntity buildingDetails = buildingDetailsService.lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, buildingRating.getBuildingNo())
                .one();

        String ratingDetailStr = JSON.toJSONString(buildingDetails, JSONWriter.Feature.WriteMapNullValue);

        BuildingScreenEntity buildingScreenEntity = buildingScreenService.lambdaQuery()
                .eq(BuildingScreenEntity::getBuildingRatingNo, buildingRating.getBuildingNo())
                .one();

        String buildingScreenStr = "";

        if (Objects.nonNull(buildingScreenEntity)) {
            buildingScreenStr = JSON.toJSONString(buildingScreenEntity, JSONWriter.Feature.WriteMapNullValue);
        }

        BuildingSnapshotEntity buildingSnapshotEntity = new BuildingSnapshotEntity();
        buildingSnapshotEntity.setBuildingRatingNo(completeRatingEntity.getBuildingRatingNo());
        buildingSnapshotEntity.setMetaSnapshot("");
        buildingSnapshotEntity.setRatingSnapshot(ratingStr);
        buildingSnapshotEntity.setDetailsSnapshot(ratingDetailStr);
        buildingSnapshotEntity.setScreenSnapshot(buildingScreenStr);
        buildingSnapshotEntity.setType(BuildingSnapshotEntity.Type.COMPLETE.getValue());
        buildingSnapshotEntity.setRatingVersion(buildingRating.getRatingVersion());
        buildingSnapshotEntity.setCreateBy(completeRatingEntity.getSubmitUser());
        buildingSnapshotService.save(buildingSnapshotEntity);
    }

    @Override
    public void beforeRevoke(ApprovalOperationDTO operationDTO) {
        String key = String.format("rating:complete:%s", operationDTO.getBusinessKey());
        if (redisUtils.get(key) != null) {
            throw new ApprovalBusinessException("评级分数计算中，请稍后");
        }
    }
}
