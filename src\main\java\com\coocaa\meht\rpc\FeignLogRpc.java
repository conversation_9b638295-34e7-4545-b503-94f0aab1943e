package com.coocaa.meht.rpc;

import com.coocaa.ad.common.config.FeignConfig;
import com.coocaa.meht.common.bean.ResultTemplate;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * cheese-log-api远程调用
 */
@FeignClient(value = "cheese-log-api", configuration = FeignConfig.class)
public interface FeignLogRpc {

    /**
     * 获取某一时间段内新增的可售点位数
     *
     * @return
     */
    @GetMapping("/api/log/rpc/log/countPointChangeLog")
    ResultTemplate<Integer> countPointChangeLog(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime);

}
