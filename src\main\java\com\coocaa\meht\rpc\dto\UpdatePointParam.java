package com.coocaa.meht.rpc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/2
 */
@Data
public class UpdatePointParam {
    @NotNull
    private Integer pointId;
    @Schema(description = "点位备注")
    private String remark;
    @Schema(description = "楼栋名称")
    private String buildingName;
    @Schema(description = "单元名称")
    private String unitName;
    @Schema(description = "楼层")
    private String floor;
    @Schema(description = "等候厅id")
    private Integer waitingHallId;
    @Schema(description = "安装公司")
    private String installationCompany;
    @Schema(description = "安装日期")
    private Date installationDate;

    /************H5编辑点位传入的可修改信息****************/
    @Schema(description = "点位图片列表")
    private List<String> images;
    private String businessCode;

}
