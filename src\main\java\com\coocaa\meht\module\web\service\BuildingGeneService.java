/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-16
 */
package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.BuildingGeneDTO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.vo.BuildingGeneVO;
import jakarta.annotation.Nullable;

import java.util.List;

/**
 * 楼宇基因服务接口
 * 定义楼宇基因相关的业务操作方法
 * 提供楼宇基因信息的增删改查等基础服务
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public interface BuildingGeneService extends IService<BuildingGeneEntity> {

    /**
     * 根据楼宇编码查询楼宇基因信息
     *
     * @param buildingRatingNo
     * @return
     */
    @Nullable
    BuildingGeneVO getBuildingGeneByNo(String buildingRatingNo);


    /**
     * 是否有大屏设备
     * @param entity
     * @return
     */
    Boolean isScreen(BuildingGeneEntity entity);

    /**
     * 是否有大屏设备
     * @param buildingNo
     * @return
     */
    Boolean isScreen(String buildingNo);

    /**
     * 校验基因信息是否完整
     *
     * @param dto
     * @return
     */
    List<String> validateGene(BuildingGeneDTO dto) ;

    /**
     * 保存或更新基因信息
     *
     * @param buildingNo
     * @param param
     */
    BuildingGeneEntity saveOrUpdate(String buildingNo, RatingApplyDto param);

    /**
     * 根据ID查询基因详情
     * @param id
     * @return
     */
    BuildingGeneVO detail(Integer id);
}