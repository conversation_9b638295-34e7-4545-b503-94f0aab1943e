package com.coocaa.meht.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024-11-05 14:22
 */
@Component
public class RedisUtils {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 时长为1小时，单位：秒
     */
    public final static long DEFAULT_EXPIRE = 60 * 60L;

    /**
     * 时长为一天，单位：秒
     */
    public final static long DAY_ONE_EXPIRE = DEFAULT_EXPIRE * 24L ;

    /**
     * 设置缓存
     *
     * @param key
     * @param value
     */
    public void set(String key, Object value) {
        set(key, value, DEFAULT_EXPIRE);
    }

    public void set(String key, Object value, long expire) {
        redisTemplate.opsForValue().set(key, JsonUtils.toJson(value), expire, TimeUnit.SECONDS);
    }

    /**
     * 获取缓存
     * @param key
     * @return
     */
    public String get(String key) {
        return get(key, String.class);
    }

    public <T> T get(String key, Class<T> clazz) {
        return JsonUtils.fromJsonNon(redisTemplate.opsForValue().get(key), clazz);
    }

    /**
     * 加 1
     * @param key
     * @return
     */
    public Long increment(String key) {
        return redisTemplate.opsForValue().increment(key);
    }

    /**
     * 减 1
     * @param key
     * @return
     */
    public Long decrement(String key) {
        return redisTemplate.opsForValue().decrement(key);
    }

    /**
     * 判断key是否存在
     * @param key
     * @return
     */
    public boolean hasKey(String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    /**
     * 删除key
     * @param key
     * @return
     */
    public boolean delete(String key) {
        return Boolean.TRUE.equals(redisTemplate.delete(key));
    }

    public boolean delete(Collection<String> keys) {
        Long num = redisTemplate.delete(keys);
        return num != null && num > 0;
    }

    public String hGet(String key, String field) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        return JsonUtils.fromJsonNon(hashOperations.get(key, field), String.class);
    }

    public Map<String, String> hGetAll(String key) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        return hashOperations.entries(key);
    }

    public void hMSet(String key, Map<String, Object> map) {
        hMSet(key, map, DEFAULT_EXPIRE);
    }

    public void hMSet(String key, Map<String, Object> map, long expire) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        Map<String, String> mapStr = new HashMap<>();
        if (map != null && !map.isEmpty()) {
            map.forEach((k, v) -> mapStr.put(k, JsonUtils.toJson(v)));
            hashOperations.putAll(key, mapStr);
            if (expire > 0) {
                expire(key, expire);
            }
        }
    }

    public void hSet(String key, String field, Object value) {
        hSet(key, field, value, DEFAULT_EXPIRE);
    }

    public void hSet(String key, String field, Object value, long expire) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        hashOperations.put(key, field, JsonUtils.toJson(value));
        if (expire > 0) {
            expire(key, expire);
        }
    }

    public void expire(String key, long expire) {
        redisTemplate.expire(key, expire, TimeUnit.SECONDS);
    }

    public void hDel(String key, Object... fields) {
        redisTemplate.opsForHash().delete(key, fields);
    }

}
