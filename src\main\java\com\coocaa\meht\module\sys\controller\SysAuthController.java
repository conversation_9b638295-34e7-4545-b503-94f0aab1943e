package com.coocaa.meht.module.sys.controller;

import cn.hutool.json.JSONUtil;
import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.LoginResultVO;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.UserAuthVO;
import com.coocaa.meht.common.bean.TokenResultVO;
import com.coocaa.meht.module.sys.dto.SysEmailLoginDto;
import com.coocaa.meht.module.sys.dto.SysFsLoginDto;
import com.coocaa.meht.module.sys.dto.SysLoginTokenDto;
import com.coocaa.meht.module.sys.dto.SysPhoneLoginDto;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.utils.Converts;
import com.coocaa.meht.utils.JsonUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * 认证管理
 */
@RestController
@RequestMapping("/sys")
@RequiredArgsConstructor
@Slf4j
public class SysAuthController {
    private final SysUserService sysUserService;


    @Anonymous
    @PostMapping("/captcha")
    @Reqlog(value = "发送验证码", type = Reqlog.LogType.INSERT)
    public Result<?> captcha( @RequestBody Map<String, Object> map) {
        String phone = Converts.toStr(map.get("phone"));
        String type = Converts.toStr(map.get("type"));
        if (StringUtils.isBlank(phone)) {
            return Result.error("手机号不能为空");
        }
        if (StringUtils.isBlank(type)) {
            return Result.error("验证码不能为空");
        }
        UserAuthVO userAuthVO= sysUserService.captcha( phone, type);
        return Result.ok(userAuthVO);
    }

    @Anonymous
    @PostMapping("/email")
    @Reqlog(value = "域登录", type = Reqlog.LogType.LOGIN)
    public Result<SysLoginTokenDto> email(@Valid @RequestBody SysEmailLoginDto dto) {
        dto.setPlatform("app");
        SysLoginTokenDto token = sysUserService.loginByEmail(dto);
        return Result.ok(token);
    }

    @Anonymous
    @PostMapping("/fs")
    @Reqlog(value = "飞书登录", type = Reqlog.LogType.LOGIN)
    public Result<LoginResultVO> fsCode(@Valid @RequestBody SysFsLoginDto dto) {
        log.info("媒资飞书登录用户信息req：{}", JsonUtils.toJson(dto));
        dto.setPlatform("app");
        LoginResultVO token = sysUserService.loginByFsCode(dto);
        log.info("媒资飞书登录用户信息res：{}", JsonUtils.toJson(token));
        return Result.ok(token);
    }

    @Anonymous
    @PostMapping("/phone")
    @Reqlog(value = "手机验证码登录", type = Reqlog.LogType.LOGIN)
    public Result<LoginResultVO> phone( @Valid @RequestBody SysPhoneLoginDto dto) {
        dto.setPlatform("app");
        LoginResultVO token = sysUserService.loginByPhone(dto);
        return Result.ok(token);
    }
    @Anonymous
    @GetMapping("/refreshToken/{refreshToken}")
    public Result<TokenResultVO> refreshToken(@PathVariable(name="refreshToken") String refreshToken ) {
        TokenResultVO token = sysUserService.getTokenByRefreshToken(refreshToken);
        return Result.ok(token);
    }


    @PostMapping("/logout")
    @Reqlog(value = "退出", type = Reqlog.LogType.DELETE)
    public Result<Void> logout() {
        sysUserService.logout(SecurityUser.getUser().getToken());
        return Result.ok();
    }
    @GetMapping("/build-user")
    @Reqlog(value = "测试设置用户信息", type = Reqlog.LogType.LOGIN)
    public Result<Void> buildUserInfo(@RequestHeader(name="token") String token) {
        sysUserService.buildUserInfo(token);
        return Result.ok();
    }
}