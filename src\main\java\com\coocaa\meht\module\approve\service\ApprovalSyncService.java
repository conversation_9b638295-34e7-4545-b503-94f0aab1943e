package com.coocaa.meht.module.approve.service;

/**
 * 审批状态同步服务接口
 * 负责同步审批中心的审批状态到本地
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface ApprovalSyncService {

    /**
     * 同步所有处理中的审批状态
     */
    void syncAllProcessingApprovals();

    /**
     * 同步指定实例的审批状态
     *
     * @param instanceCode 审批实例编码
     * @return 是否同步成功
     */
    boolean syncApprovalStatus(String instanceCode);

    /**
     * 标记同步失败
     *
     * @param instanceCode 审批实例编码
     * @param errorMessage 错误信息
     */
    void markSyncFailed(String instanceCode, String errorMessage);

    /**
     * 重试同步失败的审批
     */
    void retryFailedSync();
} 