package com.coocaa.meht.module.web.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 飞书审批表单控件类型
 */
@Getter
@AllArgsConstructor
public enum FeiShuFormItemTypeEnum implements IEnumType<String> {
    INPUT("input", "单行文本"),
    AMOUNT("amount", "金额"),
    NUMBER("number", "数字"),
    RADIOV2("radioV2", "单选");

    private final String code;
    private final String desc;

    private final static Map<String, FeiShuFormItemTypeEnum> BY_CODE_MAP =
            Arrays.stream(FeiShuFormItemTypeEnum.values())
                    .collect(Collectors.toMap(FeiShuFormItemTypeEnum::getCode, item -> item));


    /**
     * 将代码转成枚举
     */
    public static FeiShuFormItemTypeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static FeiShuFormItemTypeEnum parse(String code, FeiShuFormItemTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(FeiShuFormItemTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
}
