package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.BuildingMetaDetailDto;
import com.coocaa.meht.module.web.dto.BuildingMetaDto;
import com.coocaa.meht.module.web.dto.BuildingMetaUpdateDto;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaImgRelationEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
public interface IBuildingMetaService extends IService<BuildingMetaEntity> {
    BuildingMetaDetailDto findByBuildRatingNo(String buildingRatingNo);
    BuildingMetaDetailDto getDetail(String buildingNo);

    /**
     * 获取楼宇基本信息
     * @param buildingNo 楼宇编号
     * @return
     */
    BuildingMetaDto getBasicInfo(String buildingNo);


    /**
     * 修改楼宇主数据详情
     *
     * @param dto
     * @return
     */
    boolean updateBuildMeta(BuildingMetaUpdateDto dto);

    void saveOrUpdate(BuildingRatingEntity newEntity, boolean isLargeScreen, boolean isSubmit);

    void updateStatus(String buildingNo, Integer status);

    List<BuildingMetaEntity> listByBuildingNos(Collection<String> buildingNos);

    String getBuildingMetaNo();

    BuildingMetaEntity getByMapNo(String mapNo);

    void updateBuildMetaUnConfirmStatus(String mapNo);

    Map<String, List<BuildingMetaImgRelationEntity>> metaPic(String mapNo , List<Integer> imgType);

   /**
     * 填充AI数据
     * @param buildingNo
     * @param buildingAiScore
     * @param projectAiLevel
     */
   void fillAiData(String buildingNo, BigDecimal buildingAiScore, String projectAiLevel);
}
