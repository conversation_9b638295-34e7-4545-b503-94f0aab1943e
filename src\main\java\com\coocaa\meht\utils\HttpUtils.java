package com.coocaa.meht.utils;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class HttpUtils {
    public static final ContentType FORM_URLENCODED = ContentType.create(
            "application/x-www-form-urlencoded", StandardCharsets.UTF_8);
    private static final CloseableHttpAsyncClient httpclient;

    static {
        try {
            PoolingNHttpClientConnectionManager nconnManager = new PoolingNHttpClientConnectionManager(
                    new DefaultConnectingIOReactor(IOReactorConfig.custom()
                            .setIoThreadCount(Runtime.getRuntime().availableProcessors())
                            .setSoKeepAlive(true)
                            .build()));
            nconnManager.setMaxTotal(500);
            nconnManager.setDefaultMaxPerRoute(500);
            nconnManager.closeIdleConnections(10, TimeUnit.MINUTES);
            nconnManager.closeExpiredConnections();
            httpclient = HttpAsyncClients.custom()
                    .setConnectionManager(nconnManager)
                    .setConnectionManagerShared(false)
                    .setDefaultRequestConfig(RequestConfig.custom()
                            .setConnectionRequestTimeout(30000)
                            .setConnectTimeout(15000)
                            .setSocketTimeout(900000).build())
                    .setKeepAliveStrategy((r, c) -> {
                        long k = DefaultConnectionKeepAliveStrategy.INSTANCE.getKeepAliveDuration(r, c);
                        return k == -1 ? 300000 : k;
                    }).build();
            httpclient.start();
            /*
            PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
            connectionManager.setMaxTotal(500);
            connectionManager.setDefaultMaxPerRoute(500);
            connectionManager.setDefaultSocketConfig(SocketConfig.custom()
                    .setSoTimeout(0)
                    .setSoKeepAlive(true).build());
            connectionManager.setValidateAfterInactivity(30000);
            CLIENT = HttpClients.custom().setConnectionManager(connectionManager)
                    .setConnectionManagerShared(false)
                    .setDefaultRequestConfig(RequestConfig.custom()
                            .setConnectionRequestTimeout(30000)
                            .setConnectTimeout(15000)
                            .setSocketTimeout(900000).build())
                    .setRetryHandler(new DefaultHttpRequestRetryHandler(0, false))
                    .setKeepAliveStrategy((r, c) -> {
                        long k = DefaultConnectionKeepAliveStrategy.INSTANCE.getKeepAliveDuration(r, c);
                        return k == -1 ? 300000 : k;
                    })
                    .evictIdleConnections(10, TimeUnit.MINUTES)
                    .evictExpiredConnections().build();
                    */
        } catch (Exception e) {
            throw new RuntimeException("初始化CloseableHttpClient异常:", e);
        }
    }

    /**
     * get请求
     *
     * @param url
     * @return
     */
    public static String get(String url) {
        return get(url, null, null);
    }

    /**
     * get请求
     *
     * @param url
     * @param header
     * @return
     */
    public static String get(String url, Map<String, String> header) {
        return get(url, header, null);
    }

    /**
     * get请求
     *
     * @param url
     * @param header
     * @param param
     * @return
     */
    public static String get(String url, Map<String, String> header, Map<String, Object> param) {
        if (header == null) header = new HashMap<>();
        return execute("GET", joinParam(url, param), header, null, null, null).join().getBody();
    }

    /**
     * post请求（表单提交）
     * application/x-www-form-urlencoded
     *
     * @param url
     * @param header
     * @param formMap
     * @return
     */
    public static String post(String url, Map<String, String> header, Map<String, Object> formMap) {
        return execute("POST", url, header, null, null, formMap).join().getBody();
    }

    /**
     * post请求
     *
     * @param url
     * @param reqBody
     * @return
     */
    public static String post(String url, String reqBody) {
        return post(url, null, reqBody, ContentType.APPLICATION_JSON);
    }

    /**
     * post请求
     *
     * @param url
     * @param header
     * @param reqBody
     * @return
     */
    public static String post(String url, Map<String, String> header, String reqBody) {
        return post(url, header, reqBody, ContentType.APPLICATION_JSON);
    }

    /**
     * post请求
     *
     * @param url
     * @param header
     * @param reqBody
     * @param type
     * @return
     */
    public static String post(String url, Map<String, String> header, String reqBody, ContentType type) {
        return execute("POST", url, header, reqBody, type, null).join().getBody();
    }

    /**
     * @param method
     * @param url
     * @param header
     * @param reqBody
     * @param type
     * @param formParam
     * @return
     */
    public static CompletableFuture<Response> execute(String method, String url, Map<String, String> header,
                                                      String reqBody, ContentType type,
                                                      Map<String, Object> formParam) {
        CompletableFuture<Response> future = new CompletableFuture<>();
        long sta = System.currentTimeMillis();
        Map<String, Object> param = new HashMap<>();
        HttpRequestBase httpRequest = "GET".equals(method) ? new HttpGet(url) : new HttpPost(url);
        if (reqBody == null) {
            if (formParam != null && !formParam.isEmpty()) {
                AtomicBoolean isMultipart = new AtomicBoolean(false);
                formParam.forEach((k, v) -> {
                    if (v instanceof File || v instanceof InputStream || v instanceof byte[]) {
                        if (!isMultipart.get()) isMultipart.set(true);
                        param.put(k, null);
                    } else {
                        param.put(k, v);
                    }
                });
                HttpPost post = new HttpPost(url);
                post.setEntity(isMultipart.get() ? getMultipartEntity(formParam) : getUrlEncodedForm(formParam));
                httpRequest = post;
            }
        } else {
            HttpPost post = new HttpPost(url);
            post.setEntity(new StringEntity(reqBody, type));
            httpRequest = post;
        }
        if (header != null && !header.isEmpty()) {
            for (Map.Entry<String, String> e : header.entrySet()) {
                if (e.getValue() != null) {
                    httpRequest.setHeader(e.getKey(), e.getValue());
                }
            }
        }
        String rid = UUID.randomUUID().toString().replace("-", "");
        log.info("HTTP {} Request[{}] [{}], Header={}, Param={}, Body={}", method, rid, url, header, param, StrUtil.sub(reqBody, 0,

                100));
        httpclient.execute(httpRequest, new FutureCallback<HttpResponse>() {
            @Override
            public void completed(HttpResponse response) {
                Response res = new Response();
                try {
                    res.setCode(response.getStatusLine().getStatusCode());
                    HttpEntity resEntity = response.getEntity();
                    if (resEntity != null) {
                        res.setBody(EntityUtils.toString(resEntity, StandardCharsets.UTF_8));
                        EntityUtils.consume(resEntity);
                    }
                    Header logidHeader = response.getFirstHeader("X-Tt-Logid");
                    long end = System.currentTimeMillis();
                    log.info("HTTP {} Response[{}] [{} {} {}ms], Body={}, Fs-Logid={}", method, rid, url,
                            res.getCode(), (end - sta), StrUtil.sub(res.getBody(), 0, 100), logidHeader);
                    for (Header h : response.getAllHeaders()) {
                        if (h.getName() != null) {
                            res.getHeaders().put(h.getName(), h.getValue());
                        }
                    }
                } catch (Exception e) {
                    log.error("HTTP 解析实体异常:", e);
                } finally {
                    future.complete(res);
                }
            }

            @Override
            public void failed(Exception ex) {
                log.error(String.format("HTTP %s Request[%s] 发生异常:", method, rid), ex);
                future.complete(new Response());
            }

            @Override
            public void cancelled() {
                log.error(String.format("HTTP %s Request[%s] 请求被取消:", method, rid));
                future.complete(new Response());
            }
        });
        return future;
    }

    /**
     * 异步请求
     *
     * @param url
     * @param reqBody
     * @return
     */
    public static CompletableFuture<String> postAsync(String url, String reqBody) {
        return postAsync(url, null, reqBody);
    }

    /**
     * 异步请求
     *
     * @param url
     * @param header
     * @param reqBody
     * @return
     */
    public static CompletableFuture<String> postAsync(String url, Map<String, String> header, String reqBody) {
        return execute("POST", url, header, reqBody, ContentType.APPLICATION_JSON, null)
                .thenApply(Response::getBody);
    }

    /**
     * url拼接
     *
     * @param url
     * @param param
     * @return
     */
    public static String joinParam(String url, Map<String, Object> param) {
        if (param == null || param.isEmpty()) {
            return url;
        }
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> e : param.entrySet()) {
            if (StringUtils.hasText(e.getKey())) {
                if (e.getValue() instanceof Iterable) {
                    Iterable<?> it = (Iterable<?>) e.getValue();
                    for (Object obj : it) {
                        sb.append(e.getKey()).append("=").append(Converts.toStr(obj, "")).append("&");
                    }
                } else {
                    sb.append(e.getKey()).append("=").append(Converts.toStr(e.getValue(), "")).append("&");
                }
            }
        }
        if (sb.length() == 0) {
            return url;
        } else {
            sb.deleteCharAt(sb.length() - 1);
        }
        if (url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
            sb.insert(0, "?");
        } else {
            if (url.contains("?")) {
                if (!url.endsWith("&")) {
                    sb.insert(0, "&");
                }
            } else {
                sb.insert(0, "?");
            }
        }
        return sb.insert(0, url).toString();
    }

    /**
     * @param formParam
     * @return
     */
    private static HttpEntity getMultipartEntity(Map<String, Object> formParam) {
        MultipartEntityBuilder entityBuilder = MultipartEntityBuilder.create();
        entityBuilder.setCharset(StandardCharsets.UTF_8);
        for (Map.Entry<String, Object> entry : formParam.entrySet()) {
            if (StringUtils.hasText(entry.getKey())) {
                if (entry.getValue() instanceof Iterable) {
                    for (Object obj : (Iterable<?>) entry.getValue()) {
                        entityBuilder(entityBuilder, entry.getKey(), obj);
                    }
                } else {
                    entityBuilder(entityBuilder, entry.getKey(), entry.getValue());
                }
            }
        }
        return entityBuilder.build();
    }

    /**
     * @param formParam
     * @return
     */
    private static HttpEntity getUrlEncodedForm(Map<String, Object> formParam) {
        List<BasicNameValuePair> list = new ArrayList<>();
        for (Map.Entry<String, Object> entry : formParam.entrySet()) {
            if (StringUtils.hasText(entry.getKey())) {
                if (entry.getValue() instanceof Iterable) {
                    for (Object obj : (Iterable<?>) entry.getValue()) {
                        list.add(new BasicNameValuePair(entry.getKey(), Converts.toStr(obj)));
                    }
                } else {
                    list.add(new BasicNameValuePair(entry.getKey(), Converts.toStr(entry.getValue())));
                }
            }
        }
        return new UrlEncodedFormEntity(list, StandardCharsets.UTF_8);
    }

    /**
     * @param entityBuilder
     * @param key
     * @param value
     */
    private static void entityBuilder(MultipartEntityBuilder entityBuilder, String key, Object value) {
        if (value instanceof File) {
            entityBuilder.addBinaryBody(key, (File) value);
        } else if (value instanceof byte[]) {
            String[] split = key.split(";");
            if (split.length > 1) {
                entityBuilder.addBinaryBody(split[0], (byte[]) value, ContentType.DEFAULT_BINARY, split[1]);
            }
        } else if (value instanceof InputStream) {
            String[] split = key.split(";");
            if (split.length > 1) {
                entityBuilder.addBinaryBody(split[0], (InputStream) value, ContentType.DEFAULT_BINARY, split[1]);
            }
        } else {
            entityBuilder.addTextBody(key, Converts.toStr(value), FORM_URLENCODED);
        }
    }

    @Data
    public static class Response {
        private int code = -1;
        private String body;
        private Map<String, String> headers = new HashMap<>();
    }

}
