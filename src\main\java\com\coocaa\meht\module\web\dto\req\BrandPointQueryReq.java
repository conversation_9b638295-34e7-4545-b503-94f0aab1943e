package com.coocaa.meht.module.web.dto.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/3
 * @description 查询楼宇
 */
@Data
public class BrandPointQueryReq {
    private String token;
    @Schema(description = "城市")
    private String city;
    @Schema(description = "百度坐标地址（经纬度之间逗号分隔）")
    private String address;
    @Schema(description = "距离n公里数（超过我方系统配置的最大距离，以我方系统配置的最大距离为准，目前系统配置的100公里）")
    private Integer distance;
}
