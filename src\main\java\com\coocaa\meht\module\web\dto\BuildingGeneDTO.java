package com.coocaa.meht.module.web.dto;

import com.coocaa.meht.common.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-04-14
 */
@Data
public class BuildingGeneDTO {

    @Schema(description = "主键ID")
    private Long id;

    /**
     * 规格
     */
    @Schema(description = "规格")
    private String spec;

    @Schema(description = "客户编号")
    @NotBlank(message = "客户编号不能为空", groups = ValidationGroups.Small.class)
    private String buildingRatingNo;


    @Schema(description = "月平均租金")
    private BigDecimal monthlyAvgPrice;

    @DecimalMin(value = "0", message = "房价不能小于0", groups = ValidationGroups.Small.class)
    @Digits(integer = 8, fraction = 2, message = "房价格式不正确", groups = ValidationGroups.Small.class)
    @Schema(description = "房价")
    @NotNull(message = "房价不能为空", groups = ValidationGroups.Small.class)
    private BigDecimal housePrice;

    @Size(max = 200, message = "竞媒信息长度不能超过200", groups = ValidationGroups.Small.class)
    @Schema(description = "竞媒信息")
    @NotNull(message = "竞媒信息不能为空", groups = ValidationGroups.Small.class)
    private String competitiveMediaInfo;

    @Schema(description = "竞媒信息")
    private String competitiveMediaInfoName;

    //    @Min(value = 0, message = "目标点位数量不能小于0")
//    @Max(value = 9999, message = "目标点位数量不能超过9999")
//    @Schema(description = "目标点位数量")
//    @NotNull(message = "目标点位数量不能为空")
    private Integer targetPointCount;

    @Min(value = 0, message = "最高层数不能小于0", groups = ValidationGroups.Small.class)
    @Max(value = 999, message = "最高层数不能超过999", groups = ValidationGroups.Small.class)
    @Schema(description = "最高层数")
    @NotNull(message = "最高层数不能为空", groups = ValidationGroups.Small.class)
    private Integer maxFloorCount;

    //    @Min(value = 0, message = "楼龄不能小于0", groups = ValidationGroups.Small.class)
//    @Max(value = 9999, message = "楼龄信息长度不能超过9999", groups = ValidationGroups.Small.class)
//    @NotNull(message = "楼龄不能为空", groups = ValidationGroups.Small.class)
    @Schema(description = "楼龄")
    private Integer buildingAge;

    @Min(value = 0, message = "总楼栋数量不能小于0", groups = ValidationGroups.Small.class)
    @Max(value = 999, message = "总楼栋数量不能超过999", groups = ValidationGroups.Small.class)
    @NotNull(message = "总楼栋数量不能为空", groups = ValidationGroups.Small.class)
    @Schema(description = "总楼栋数量")
    private Integer totalBuildingCount;


    @Min(value = 0, message = "总单元数量不能小于0", groups = ValidationGroups.Small.class)
    @Max(value = 999, message = "总单元数量不能超过999", groups = ValidationGroups.Small.class)
    @NotNull(message = "总单元数量不能为空", groups = ValidationGroups.Small.class)
    @Schema(description = "总单元数量")
    private Integer totalUnitCount;


    @Schema(description = "总等候厅数量")
//    @Min(value = 0, message = "总等候厅不能小于0")
//    @Max(value = 9999, message = "总等候厅不能超过9999")
//    @NotNull(message = "总等候厅不能为空")
    private Integer totalWaitingCount;

    @Min(value = 0, message = "电梯数量不能小于0", groups = ValidationGroups.Small.class)
    @Max(value = 999, message = "电梯数量不能超过999", groups = ValidationGroups.Small.class)
    @NotNull(message = "电梯数量不能为空", groups = ValidationGroups.Small.class)
    @Schema(description = "电梯数量")
    private Integer elevatorCount;

    @Min(value = 0, message = "车位数量不能小于0", groups = ValidationGroups.Small.class)
    @Max(value = 99999999, message = "车位数量不能超过99999999", groups = ValidationGroups.Small.class)
    @Schema(description = "车位数量")
    @NotNull(message = "车位数量不能为空", groups = ValidationGroups.Small.class)
    private Integer parkingCount;

    @Size(max = 1000, message = "禁忌行业名称长度不能超过1000", groups = ValidationGroups.Small.class)
    @NotNull(message = "禁忌行业名称不能为空", groups = ValidationGroups.Small.class)
    @Schema(description = "禁忌行业名称")
    private String forbiddenIndustry;


    @Schema(description = "禁忌行业")
    private String forbiddenIndustryName;

    //    @Min(value = 0, message = "日人流量不能小于0")
//    @Max(value = *********, message = "日人流量不能超过8位数")
//    @NotNull(message = "日人流量不能为空")
    @Schema(description = "日人流量")
    private Integer flowCount;

    //    @Min(value = 0, message = "访客数不能小于0")
//    @Max(value = *********, message = "访客数不能超过8位数")
//    @NotNull(message = "访客数不能为空")
    @Schema(description = "访客数")
    private Integer visitCount;

    @Min(value = 0, message = "入驻企业数量不能小于0", groups = ValidationGroups.Small.class)
    @Max(value = *********, message = "入驻企业数量不能超过8位数", groups = ValidationGroups.Small.class)
    @NotNull(message = "入驻企业数量不能为空", groups = ValidationGroups.Small.class)
    @Schema(description = "入驻企业数量")
    private Integer companyCount;

    @DecimalMin(value = "0", message = "入住率不能小于0", groups = ValidationGroups.Small.class)
    @DecimalMax(value = "100", message = "入住率不能大于100", groups = ValidationGroups.Small.class)
    @Digits(integer = 3, fraction = 2, message = "入住率格式不正确", groups = ValidationGroups.Small.class)
    @NotNull(message = "入住率不能为空", groups = ValidationGroups.Small.class)
    @Schema(description = "入住率")
    private BigDecimal occupancyRate;


    /**
     * 楼间距，表示楼宇之间的间距（单位：米）
     */
    @NotNull(message = "楼间距不能为空", groups = {ValidationGroups.Large.class})
    @Min(value = 0, message = "楼间距不能小于0", groups = {ValidationGroups.Large.class})
    @Schema(description = "楼间距")
    private BigDecimal buildingSpacing;

    /**
     * 挑高，表示楼宇的层高（单位：米）
     */
    @NotNull(message = "挑高不能为空", groups = {ValidationGroups.Large.class})
    @Min(value = 0, message = "挑高不能小于0", groups = {ValidationGroups.Large.class})
    @Schema(description = "挑高")
    private BigDecimal buildingCeilingHeight;

    /**
     * 提交系数，用于计算楼宇的得分
     */
    private BigDecimal submitCoefficient;

    /**
     * 复核系数，审核后确定的系数
     */
    private BigDecimal finalCoefficient;


    /**
     * 特殊说明，用于记录楼宇的特殊情况或备注信息
     */
    private String specialDesc;

    @DecimalMin(value = "0", message = "物业费不能小于0", groups = ValidationGroups.Small.class)
    @NotNull(message = "物业费不能为空", groups = ValidationGroups.Small.class)
    @Schema(description = "物业费")
    private BigDecimal propertyFee;

    @Schema(description = "最低楼层数")
    @Min(value = 0, message = "最低楼层数不能小于0", groups = ValidationGroups.Small.class)
    @Max(value = 999, message = "最低楼层数不能超过999", groups = ValidationGroups.Small.class)
    @NotNull(message = "最低楼层数不能为空", groups = ValidationGroups.Small.class)
    private Integer minFloorCount;

    @Schema(description = "交付时间")
    @NotNull(message = "交付时间不能为空", groups = ValidationGroups.Small.class)
    private LocalDate deliveryDate;

    @Schema(description = "覆盖人数")
    private Integer coverageCount;

    @Schema(description = "日租金")
    @DecimalMin(value = "0", message = "日租金不能小于0", groups = ValidationGroups.Small.class)
    @Digits(integer = 8, fraction = 2, message = "日租金格式不正确", groups = ValidationGroups.Small.class)
    @NotNull(message = "日租金不能为空", groups = ValidationGroups.Small.class)
    private BigDecimal dailyPrice;


}
