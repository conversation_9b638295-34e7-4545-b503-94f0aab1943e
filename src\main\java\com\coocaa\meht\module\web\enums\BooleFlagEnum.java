package com.coocaa.meht.module.web.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 用于数据库中表达布尔意思的状态或标记
 *
 * <pre>
 *  1: 是，表示肯定的意思，
 *  0: 否, 表示否定的意思
 *
 * 可用于如下标记:
 *   物业公司是否启用 [1:启用 0:禁用]
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Getter
@AllArgsConstructor
public enum BooleFlagEnum implements IEnumType<Integer> {
    /**
     * 是, 肯定的意思
     */
    YES(1, "是"),

    /**
     * 否, 否定的意思
     */
    NO(0, "否");


    private final Integer code;
    private final String desc;


    /**
     * 是否表示肯定
     */
    public static boolean isYes(Integer code) {
        return Objects.nonNull(code) && Objects.equals(YES.getCode(), code);
    }

    /**
     * 是否表示肯定
     */
    public static boolean isYes(BooleFlagEnum flag) {
        return Objects.nonNull(flag) && Objects.equals(YES, flag);
    }

    /**
     * 将代码转成枚举
     */
    public static BooleFlagEnum parse(Integer code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static BooleFlagEnum parse(Integer code, BooleFlagEnum defaultValue) {
        if (Objects.equals(YES.getCode(), code)) {
            return BooleFlagEnum.YES;
        }
        if (Objects.equals(NO.getCode(), code)) {
            return BooleFlagEnum.NO;
        }
        return defaultValue;
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(Integer code) {
        return getDesc(code, null, null);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(Integer code, String yesDesc, String noDesc) {
        if (Objects.equals(YES.getCode(), code)) {
            return StringUtils.isBlank(yesDesc) ? YES.getDesc() : yesDesc;
        }

        if (Objects.equals(NO.getCode(), code)) {
            return StringUtils.isBlank(noDesc) ? NO.getDesc() : noDesc;
        }

        return StringUtils.EMPTY;
    }
}
