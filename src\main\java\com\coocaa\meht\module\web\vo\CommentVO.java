package com.coocaa.meht.module.web.vo;

/**
 * <AUTHOR>
 * @version 1.0
 * @description  
 * @since 2025-04-29
 */
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论视图对象
 */
@Data
public class CommentVO {
    
    /**
     * 评论ID
     */
    private Integer id;
    
    /**
     * 业务类型（1楼宇、2价格申请）
     */
    private Integer businessType;
    
    /**
     * 业务类型描述
     */
    private String businessTypeDesc;
    
    /**
     * 业务ID
     */
    private String businessId;
    
    /**
     * 评论内容
     */
    private String content;
    
    /**
     * 附件ID列表
     */
    private List<String> attachmentIds;
    
    /**
     * 附件信息列表
     */
    private List<AttachmentVO> attachments;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建人姓名
     */
    private String createByName;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 附件视图对象
     */
    @Data
    public static class AttachmentVO {
        
        /**
         * 附件ID
         */
        private String id;
        
        /**
         * 文件名
         */
        private String fileName;
        
        /**
         * 文件大小(KB)
         */
        private Long fileSize;
        
        /**
         * 文件类型
         */
        private String fileType;
        
        /**
         * 文件URL
         */
        private String fileUrl;
    }
} 