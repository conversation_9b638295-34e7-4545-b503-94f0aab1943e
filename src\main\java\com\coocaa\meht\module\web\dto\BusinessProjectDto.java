package com.coocaa.meht.module.web.dto;


import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/9
 * @description 商机楼宇信息
 */
@Data
public class BusinessProjectDto {
    /**
     * 楼宇类型 0 写字楼 1 商住楼 2 综合体 3 产业园区
     */
    private Integer buildingType;

    /**
     * 楼宇编码
     */
    private String buildingNo;

    /**
     * 楼宇名称
     */
    private String buildingName;

    /**
     * 等级评价
     */
    private String projectLevel;

    /**
     * 省名称
     */
    private String mapProvince;

    /**
     * 市名称
     */
    private String mapCity;

    /**
     * 区名称
     */
    private String mapRegion;

    /**
     * 商机编码
     */
    private String businessCode;

    /**
     * 商机名称
     */
    private String businessName;

}
