package com.coocaa.meht.common.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: cheese-meht
 * @ClassName PointPicVo
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-20 17:21
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PointPicVo {

    private Integer pointId;

    private String picUrl;

}
