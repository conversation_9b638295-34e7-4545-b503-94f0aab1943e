package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 价格申请设备明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("price_apply_device")
public class PriceApplyDeviceEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 价格申请ID
     */
    private Integer applyId;

    /**
     * 设备类型
     */
    private String type;

    /**
     * 设备尺寸
     */
    private String size;

    /**
     * 设备数量
     */
    private Integer quantity;

    /**
     * 签约单价(元/台/月)
     */
    private BigDecimal signPrice;

    /**
     * 安装位置
     */
    @TableField(select = false)
    private String location;

    /**
     * 安装位置 JSON
     */
    @TableField(select = false)
    private String locationDetail;

    /**
     * 激励单价(元/台/月)
     */
    private BigDecimal incentivePrice;

    /**
     * 是否是大屏 [1:小屏, 2:大屏, 3:大小屏]
     */
    private Integer largeScreenFlag;

    /**
     * 是否核心区域 [0:非, 1:是]
     */
    private Integer coreAreaFlag;

}
