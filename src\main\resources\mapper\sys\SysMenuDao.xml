<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.sys.dao.SysMenuDao">

    <select id="getListByUser" resultType="com.coocaa.meht.module.sys.entity.SysMenuEntity">
        select t3.*
        from sys_user_role t1
        left join sys_role_menu t2 on t1.role_code = t2.role_code
        left join sys_menu t3 on t2.menu_id = t3.id
        where t1.user_code = #{userCode} and t3.deleted = 0
        order by `sort` asc;
    </select>

</mapper>
