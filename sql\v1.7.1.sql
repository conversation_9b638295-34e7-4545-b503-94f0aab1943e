-- 楼宇基因表
CREATE TABLE `building_gene`
(
    `id`                      int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `building_rating_no`      varchar(20) NOT NULL DEFAULT '' COMMENT '楼宇编号',
    `spec`                    varchar(50) NOT NULL DEFAULT '' COMMENT '规格存json',
    `elevator_count`          int(11) DEFAULT NULL COMMENT '电梯数量',
    `company_count`           int(11) DEFAULT NULL COMMENT '入驻企业数量',
    `building_spacing`        decimal(5, 2)        DEFAULT NULL COMMENT '间距',
    `building_ceiling_height` decimal(5, 2)        DEFAULT NULL COMMENT '挑高',
    `submit_coefficient`      decimal(5, 1)        default null COMMENT '提交系数',
    `final_coefficient`       decimal(5, 1)        default null COMMENT '复核系数',
    `total_unit_count`        int(11) DEFAULT NULL COMMENT '楼栋数',
    `special_desc`            varchar(1000)        DEFAULT '' COMMENT '特殊说明',
    `create_by`               varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`             datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`               varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time`             datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `delete_flag`             tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
    PRIMARY KEY (`id`),
    KEY                       `idx_building_rating_no` (`building_rating_no`)
) ENGINE=InnoDB COMMENT='楼宇基因表';

ALTER TABLE `building_rating`
    ADD COLUMN `building_lobby_env_pic` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '大堂环境图附件地址' AFTER `building_hall_pic`,
    ADD COLUMN `building_elevator_pic` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '梯厅环境图附件地址' AFTER `building_lobby_pic`,
    ADD COLUMN `building_gate_pic` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '闸口图附件地址' AFTER `building_elevator_pic`,
    ADD COLUMN `building_installation_pic` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '安装示意图附件地址' AFTER `building_gate_pic`;


CREATE TABLE `screen_approve_record`
(
    `id`                int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `natural_key`       varchar(20)  NOT NULL DEFAULT '' COMMENT '业务主键',
    `approve_user`      varchar(10)  NOT NULL DEFAULT '' COMMENT '审核人工号',
    `remark`            varchar(200) NOT NULL DEFAULT '' COMMENT '审核意见',
    `status`            tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：见各个业务状态',
    `create_by`         varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `update_by`         varchar(10)  NOT NULL DEFAULT '' COMMENT '修改人',
    `delete_flag`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
    `approve_time`      datetime              DEFAULT NULL COMMENT '审批时间',
    `approve_level`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '审批等级',
    `scene_type`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '业务类型（1楼宇、2价格申请）',
    `final_coefficient` decimal(5, 2)         DEFAULT NULL COMMENT '复核系数',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='审核记录';