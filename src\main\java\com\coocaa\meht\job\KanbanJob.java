package com.coocaa.meht.job;

import com.coocaa.meht.module.web.service.kanban.IKanbanService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;

/**
 * ClassName: KanbanJob
 * Package: com.coocaa.meht.job
 * Description: 媒资数据看板定时任务
 * ReferLink:
 *
 * <AUTHOR>
 * @Create 2025/1/12 17:17
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class KanbanJob {
    private final IKanbanService kanbanService;

    /**
     * @Author：TanJie
     * @Date：2025-01-12 17:19
     * @Description：定时任务 - 同步楼宇总量统计信息
     */
    @XxlJob("syncBuildingTotalStatistics")
    public void syncBuildingTotalStatistics() {
        kanbanService.buildingTotalStatistics();
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-12 17:19
     * @Description：定时任务 - 同步楼宇新增统计信息
     */
    @XxlJob("syncBuildingIncrementStatistics")
    public void syncBuildingIncrementStatistics() {
        kanbanService.buildingIncrementStatistics();
    }

    /**
     * 楼宇项目(商机)转化率统计
     */
    @XxlJob("ratioStatistics")
    public void executeRatioStatistics() {
        XxlJobHelper.log("楼宇项目(商机)转化率统计开始");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("楼宇项目(商机)转化率统计");
        try {
            kanbanService.executeRatioStatistics(null);
            String message = String.format("楼宇项目(商机)转化率统计, 耗时: %dms", stopWatch.getTotalTimeMillis());
            log.info(message);
            XxlJobHelper.log(message);
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            String errorMessage = "楼宇项目(商机)转化率统计失败";
            log.error(errorMessage, e);
            XxlJobHelper.handleFail(errorMessage + ": " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            stopWatch.stop();
        }
    }
}
