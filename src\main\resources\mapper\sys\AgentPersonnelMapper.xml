<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.web.dao.AgentPersonnelDao">
    <select id="getAgentPersonnelByCode" resultType="com.coocaa.meht.module.web.entity.AgentPersonnelEntity">
        SELECT
            id,
            emp_code,
            emp_name,
            emp_mobile,
            agent_code,
            agent_name,
            STATUS,
            create_by,
            create_time,
            update_by,
            update_time
        FROM
            agent_personnel
        WHERE
            emp_code = #{empCode}
            LIMIT 1
    </select>
    <select id="getAgentPersonnelsByCodes" resultType="com.coocaa.meht.module.web.entity.AgentPersonnelEntity">

        SELECT
            id,
            emp_code,
            emp_name,
            emp_mobile,
            agent_code,
            agent_name,
            STATUS,
            create_by,
            create_time,
            update_by,
            update_time
        FROM
            agent_personnel
        WHERE
            emp_code IN
            <foreach collection="empCodes" item="empCode" open="(" separator="," close=")">
                #{empCode}
            </foreach>
    </select>
</mapper>