package com.coocaa.meht.module.web.vo.property;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import jdk.dynalink.linker.LinkerServices;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
public class PropertyCompanyVO {
    /**
     * 主键ID
     */

    private Integer id;

    /**
     * 物业公司类型：1企业、2个人
     */
    @Schema(description = "物业公司类型：1企业、2个人", type = "Int", example = "1")
    private Integer type;

    /**
     * 物业公司名称
     */
    @Schema(description = "物业公司名称", type = "String", example = "xx公司")
    private String name;

    /**
     * 统一社会信用代码：企业类型必填
     */
    @Schema(description = "统一社会信用代码", type = "String", example = "6529****281X")
    private String unifiedSocialCreditCode;

    /**
     * 身份证号
     */
    @Schema(description = "身份证", type = "String", example = "6529****281X")
    private String idCard;

    /**
     * 手机号码:个人必填
     */
    @Schema(description = "手机号码", type = "String", example = "183****4368")
    private String phone;

    /**
     * 物业地址
     */
    @Schema(description = "物业地址", type = "String", example = "xx省xx市")
    private String address;

    /**
     * 状态：1-启用，0-禁用
     */
    @Schema(description = "状态[1:启用, 0:禁用]", type = "Int", example = "1")
    private Integer status;

    /**
     * 物业联系人员
     */
    @Schema(description = "物业联系人员")
    private List<PropertyCompanyPersonVO> personVOS;

}
