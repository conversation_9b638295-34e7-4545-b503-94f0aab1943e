package com.coocaa.meht.rpc.dto;

import com.coocaa.meht.module.web.enums.PointNodeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/11/4
 */
@Data
@Schema(description = "修改单元时要传楼栋名称，" +
        "修改等候厅因为有id就不需要传buildingName和unitName了")
public class PointTreeUpdate {

    @Schema(description = "项目id")
    private Integer projectId;

    @Schema(description = "楼栋名称，修改单元，楼层的时候传")
    private String buildingName;
    private String buildingRatingNo;
    @Schema(description = "单元名称,修改楼层的时候传")
    private String unitName;

    @Schema(description = "修改之后的值")
    private String name;

    @Schema(description = "修改之前的值")
    private String originalValue;

    @Schema(description = "值分别为building/unit/waitingHall 注意楼层不能编辑")
    private PointNodeTypeEnum pointNodeTypeEnum;

    @Schema(description = "等候厅类型只有修改等候厅的时候传")
    private String waitingHallType;

    @Schema(description = "等候厅id，修改等候厅的时候传")
    private Integer waitingHallId;



}
