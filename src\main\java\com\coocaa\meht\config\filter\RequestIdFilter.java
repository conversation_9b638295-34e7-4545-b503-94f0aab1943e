package com.coocaa.meht.config.filter;

import cn.hutool.core.util.StrUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.UUID;

/**
 * 请求ID过滤器
 * 为每个HTTP请求生成唯一的请求ID并放入MDC中
 */
@Component
@Slf4j
public class RequestIdFilter extends OncePerRequestFilter {

    public static final String REQUEST_ID_KEY = "requestId";
    public static final String REQUEST_ID_HEADER = "X-Request-ID";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
            // 尝试从请求头中获取请求ID（支持从上游服务传递过来）
            String requestId = request.getHeader(REQUEST_ID_HEADER);

            // 如果请求头中没有请求ID，则生成一个新的
            if (StrUtil.isBlank(requestId)) {
                requestId = generateRequestId();
//                log.debug("生成新的请求ID: {}", requestId);
                // 将请求ID添加到响应头中
                response.addHeader(REQUEST_ID_HEADER, requestId);
            } else {
//                log.debug("从请求头获取到请求ID: {}", requestId);
            }

            // 将请求ID放入MDC
            MDC.put(REQUEST_ID_KEY, requestId);


            // 继续过滤器链
            filterChain.doFilter(request, response);
        } finally {
            // 清理MDC，防止内存泄漏
            MDC.remove(REQUEST_ID_KEY);
        }
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
} 