package com.coocaa.meht.module.approve.config;

import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 审批规则配置类
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Component
@ConfigurationProperties(prefix = "approval")
@RefreshScope
@Data
public class ApprovalRuleConfig {
    /**
     * 审批类型与规则编码的映射
     */
    private Map<String, RuleInfo> rules = new HashMap<>();

    /**
     * 可查看所有代办/已办列表用户
     */
    private List<String> adminUser;

    /**
     * 获取审批类型对应的规则编码
     */
    public Integer getRuleCode(String type) {
        RuleInfo ruleInfo = rules.get(type);
        return ruleInfo != null ? ruleInfo.getRuleCode() : null;
    }

    /**
     * 根据规则编码获取审批类型
     *
     * @param ruleCode 规则编码
     * @return 审批类型 (map key)
     */
    public String getApprovalTypeByRuleCode(Integer ruleCode) {
        if (ruleCode == null) {
            return null;
        }
        String type = rules.entrySet()
                .stream()
                .filter(entry -> ruleCode.equals(entry.getValue().getRuleCode()))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
        // 楼宇评级跟完善评级共用一套规则  特殊处理
        if (ApprovalTypeEnum.COMPLETE_BUILDING_APPROVAL.getCode().equals(type)) {
            return ApprovalTypeEnum.BUILDING_APPROVAL.getCode();
        }
        return type;
    }

    /**
     * 获取所有规则编码
     *
     * @return 所有规则编码的列表
     */
    public List<Integer> getAllRuleCodes() {
        return rules.values().stream()
                .map(RuleInfo::getRuleCode)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 规则信息
     */
    @Data
    public static class RuleInfo {
        /**
         * 规则编码
         */
        private Integer ruleCode;

        /**
         * 规则名称
         */
        private String ruleName;

    }
} 