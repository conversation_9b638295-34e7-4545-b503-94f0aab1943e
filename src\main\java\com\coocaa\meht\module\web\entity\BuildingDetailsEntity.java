package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.common.annotation.RollBack;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

@RollBack
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("building_details")
public class BuildingDetailsEntity extends BaseEntity {
    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 楼宇编码
     */
    private String buildingNo;
    /**
     * 写字楼等级
     */
    private Long buildingGrade;
    /**
     * 地理等级
     */
    private Long buildingLocation;
    /**
     * 楼层数
     */
    private Long buildingNumber;

    /**
     * 楼层数输入
     */
    private String buildingNumberInput;
    /**
     * 月租金
     */
    private Long buildingPrice;

    /**
     * 月租金输入
     */
    private String buildingPriceInput;
    /**
     * 楼龄
     */
    private Long buildingAge;
    /**
     * 楼龄输入
     */
    private String buildingAgeInput;
    /**
     * 外墙材料
     */
    private Long buildingExterior;
    /**
     * 楼盘大堂
     */
    private Long buildingLobby;
    /**
     * 地下车库
     */
    private Long buildingGarage;
    /**
     * 侯梯厅
     */
    private Long buildingHall;
    /**
     * 综合体品牌
     */
    private Long buildingBrand;

    /**
     * Top100品牌ID
     * 来源于数据表：building_brand
     */
    private Long topBrandId;

    /**
     * TOP100品牌名称
     */
    private String topBrandName;

    /**
     * 点评评分
     */
    private Long buildingRating;
    /**
     * 入驻率
     */
    private Long buildingSettled;
    /**
     * 第三方等级
     */
    private String thirdBuildingGrade;

    /**
     * 第三方等级ID
     */
    private Long thirdBuildingGradeId;

    /**
     * 第三方地理位置
     */
    private String thirdBuildingLocation;

    /**
     * 第三方地理位置ID
     */
    private Long thirdBuildingLocationId;

    /**
     * 第三方楼层数
     */
    private String thirdBuildingNumber;
    /**
     * 第三方楼层数
     */
    private Long thirdBuildingNumberId;


    /**
     * 第三方月租金
     */
    private String thirdBuildingPrice;
    /**
     * 第三方月租金
     */
    private Long thirdBuildingPriceId;

    /**
     * 第三方楼宇类型
     */
    private String thirdBuildingType;

    /**
     * 第三方楼龄
     */
    private String thirdBuildingAge;
    /**
     * 第三方楼龄
     */
    private Long thirdBuildingAgeId;

    /**
     * 第三方外观造型
     */
    private String thirdBuildingExterior;

    /**
     * 第三方外观造型
     */
    private Long thirdBuildingExteriorId;

    /**
     * 第三方楼盘大堂
     */
    private String thirdBuildingLobby;

    /**
     * 第三方楼盘大堂
     */
    private Long thirdBuildingLobbyId;

    /**
     * 第三方地下车库
     */
    private String thirdBuildingGarage;
    /**
     * 第三方楼盘大堂
     */
    private Long thirdBuildingGarageId;

    /**
     * 第三方侯梯厅
     */
    private String thirdBuildingHall;
    /**
     * 第三方楼盘大堂
     */
    private Long thirdBuildingHallId;

    /**
     * 第三方综合体品牌
     */
    private String thirdBuildingBrand;
    /**
     * 第三方综合体品牌
     */
    private Long thirdBuildingBrandId;

    /**
     * 第三方点评评分
     */
    private String thirdBuildingRating;
    /**
     * 第三方点评评分
     */
    private Long thirdBuildingRatingId;

    /**
     * 第三方入驻率
     */
    private String thirdBuildingSettled;
    /**
     * 第三方入驻率
     */
    private Long thirdBuildingSettledId;

    /**
     * 日租金输入值
     */
    private BigDecimal dailyPriceInput;

    /**
     * 第三方日租金
     */
    private String thirdDailyPrice;

    /**
     * 中国房价行情网日租金
     */
    private BigDecimal creDailyPrice;

    /**
     * 交付日期输入值
     */
    private LocalDate deliveryDate;

    /**
     * 第三方交付日期
     */
    private String thirdDeliveryDate;

    /**
     * 楼宇等级文本值
     */
    private String gradeName;

    /**
     * 地理位置文本值
     */
    private String locationName;

    /**
     * 外立面文本值
     */
    private String exteriorName;

    /**
     * 大楼大堂文本值
     */
    private String lobbyName;

    /**
     * 车库文本值
     */
    private String garageName;

    /**
     * 等候厅文本值
     */
    private String hallName;

    /**
     * 品牌文本值
     */
    private String brandName;

    /**
     * 评分文本值
     */
    private String ratingName;

    /**
     * 入驻率文本值
     */
    private String settledName;

    /**
     * 是否删除: 0否,1是
     */
    private Integer deleted;

    public Long valueId(String parameterCode) {
        switch (parameterCode) {
            case "buildingGrade":
                return this.buildingGrade;
            case "buildingLocation":
                return this.buildingLocation;
            case "buildingNumber":
                return this.buildingNumber;
            case "buildingSettled":
                return this.buildingSettled;
            case "buildingPrice":
                return this.buildingPrice;
            case "buildingAge":
                return this.buildingAge;
            case "buildingExterior":
                return this.buildingExterior;
            case "buildingHall":
                return this.buildingHall;
            case "buildingLobby":
                return this.buildingLobby;
            case "buildingGarage":
                return this.buildingGarage;
            case "buildingBrand":
                return this.buildingBrand;
            case "buildingRating":
                return this.buildingRating;
            case "thirdbuildingGradeid":
                return this.thirdBuildingGradeId;
            case "thirdbuildingGarageid":
                return this.thirdBuildingGarageId;
            case "thirdbuildingLocationid":
                return this.thirdBuildingLocationId;
            case "thirdbuildingNumberid":
                return this.thirdBuildingNumberId;
            case "thirdbuildingSettledid":
                return this.thirdBuildingSettledId;
            case "thirdbuildingPriceid":
                return this.thirdBuildingPriceId;
            case "thirdbuildingAgeid":
                return this.thirdBuildingAgeId;
            case "thirdbuildingExteriorid":
                return this.thirdBuildingExteriorId;
            case "thirdbuildingHallid":
                return this.thirdBuildingHallId;
            case "thirdbuildingLobbyid":
                return this.thirdBuildingLobbyId;
            case "thirdbuildingBrandid":
                return this.thirdBuildingBrandId;
            case "thirdbuildingRatingid":
                return this.thirdBuildingRatingId;
            default:
                return null;
        }
    }

    public String thirdValue(String parameterCode) {
        switch (parameterCode) {
            case "buildingGrade":
                return this.thirdBuildingGrade;
            case "buildingLocation":
                return this.thirdBuildingLocation;
            case "buildingNumber":
                return this.thirdBuildingNumber;
            case "buildingSettled":
                return this.thirdBuildingSettled;
            case "buildingPrice":
                return Objects.nonNull(this.creDailyPrice) ? this.creDailyPrice.toString() : this.thirdDailyPrice;
            case "buildingAge":
                return Objects.nonNull(this.deliveryDate) ? this.thirdDeliveryDate : this.thirdBuildingAge;
            case "buildingExterior":
                return this.thirdBuildingExterior;
            case "buildingHall":
                return this.thirdBuildingHall;
            case "buildingLobby":
                return this.thirdBuildingLobby;
            case "buildingGarage":
                return this.thirdBuildingGarage;
            case "buildingBrand":
                return this.thirdBuildingBrand;
            case "buildingRating":
                return this.thirdBuildingRating;
            default:
                return null;
        }
    }

    public String getValue(String code) {
        switch (code) {
            case "buildingNumber":
                return buildingNumberInput;
            case "buildingPrice":
                return buildingPriceInput;
            case "buildingAge":
                return buildingAgeInput;
            case "thirdbuildingGrade":
                return thirdBuildingGrade;
            case "thirdbuildingLocation":
                return thirdBuildingLocation;
            case "thirdbuildingNumber":
                return thirdBuildingNumber;
            case "thirdbuildingPrice":
                return thirdBuildingPrice;
            case "thirdbuildingAge":
                return thirdBuildingAge;
            case "thirdbuildingExterior":
                return thirdBuildingExterior;
            case "thirdbuildingLobby":
                return thirdBuildingLobby;
            case "thirdbuildingGarage":
                return thirdBuildingGarage;
            case "thirdbuildingBrand":
                return thirdBuildingBrand;
            case "thirdbuildingRating":
                return thirdBuildingRating;
            case "buildingGrade":
                return gradeName;
            case "buildingLocation":
                return locationName;
            case "buildingSettled":
                return settledName;
            case "buildingExterior":
                return exteriorName;
            case "buildingHall":
                return hallName;
            case "buildingLobby":
                return lobbyName;
            case "buildingGarage":
                return garageName;
            case "buildingBrand":
                return brandName;
            case "buildingRating":
                return ratingName;
            default:
                return null;
        }
    }

    public void setValueId(String code, Long value) {
        switch (code) {
            case "buildingNumberid":
                buildingNumber = value;
                break;
            case "buildingPriceid":
                buildingPrice = value;
                break;
            case "buildingAgeid":
                buildingAge = value;
                break;
            case "thirdbuildingGradeid":
                thirdBuildingGradeId = value;
                break;
            case "thirdbuildingLocationid":
                thirdBuildingLocationId = value;
                break;
            case "thirdbuildingNumberid":
                thirdBuildingNumberId = value;
                break;
            case "thirdbuildingPriceid":
                thirdBuildingPriceId = value;
                break;
            case "thirdbuildingAgeid":
                thirdBuildingAgeId = value;
                break;
            case "thirdbuildingExteriorid":
                thirdBuildingExteriorId = value;
                break;
            case "thirdbuildingLobbyid":
                thirdBuildingLobbyId = value;
                break;
            case "thirdbuildingGarageid":
                thirdBuildingGarageId = value;
                break;
            case "thirdbuildingBrandid":
                thirdBuildingBrandId = value;
                break;
            case "thirdbuildingRatingid":
                thirdBuildingRatingId = value;
                break;
            case "thirdbuildingHallid":
                thirdBuildingHallId = value;
                break;
            case "thirdbuildingSettledid":
                thirdBuildingSettledId = value;
                break;
            case "buildingGrade":
                buildingGrade = value;
                break;
            case "buildingLocation":
                buildingLocation = value;
                break;
            case "buildingSettled":
                buildingSettled = value;
                break;
            case "buildingExterior":
                buildingExterior = value;
                break;
            case "buildingHall":
                buildingHall = value;
                break;
            case "buildingLobby":
                buildingLobby = value;
                break;
            case "buildingGarage":
                buildingGarage = value;
                break;
            case "buildingBrand":
                buildingBrand = value;
                break;
            case "buildingRating":
                buildingRating = value;
        }
    }

    /**
     * 清空AI数据
     */
    public void emptyAiData(){
        this.setThirdBuildingGrade("")
                .setThirdBuildingGradeId(0L)
                .setThirdBuildingLocation("")
                .setThirdBuildingLocationId(0L)
                .setThirdBuildingNumber("")
                .setThirdBuildingNumberId(0L)
                .setThirdBuildingPrice("")
                .setThirdBuildingPriceId(0L)
                .setThirdBuildingType("")
                .setThirdBuildingAge("")
                .setThirdBuildingAgeId(0L)
                .setThirdBuildingExterior("")
                .setThirdBuildingExteriorId(0L)
                .setThirdBuildingLobby("")
                .setThirdBuildingLobbyId(0L)
                .setThirdBuildingGarage("")
                .setThirdBuildingGarageId(0L)
                .setThirdBuildingHall("")
                .setThirdBuildingHallId(0L)
                .setThirdBuildingBrand("")
                .setThirdBuildingBrandId(0L)
                .setThirdBuildingRating("")
                .setThirdBuildingRatingId(0L)
                .setThirdBuildingSettled("")
                .setThirdBuildingSettledId(0L)
                .setThirdDailyPrice("")
                .setThirdDeliveryDate("")
                .setCreDailyPrice(null);
    }

    public static BuildingDetailsEntity getEmptyEntity() {
        return new BuildingDetailsEntity()
                .setBuildingGrade(0L)
                .setBuildingLocation(0L)
                .setBuildingNumber(0L)
                .setBuildingNumberInput("")
                .setBuildingPrice(0L)
                .setBuildingPriceInput("")
                .setBuildingAge(0L)
                .setBuildingAgeInput("")
                .setBuildingExterior(0L)
                .setBuildingLobby(0L)
                .setBuildingGarage(0L)
                .setBuildingHall(0L)
                .setBuildingBrand(0L)
                .setTopBrandId(0L)
                .setTopBrandName("")
                .setBuildingRating(0L)
                .setBuildingSettled(0L)
                .setThirdBuildingGrade("")
                .setThirdBuildingGradeId(0L)
                .setThirdBuildingLocation("")
                .setThirdBuildingLocationId(0L)
                .setThirdBuildingNumber("")
                .setThirdBuildingNumberId(0L)
                .setThirdBuildingPrice("")
                .setThirdBuildingPriceId(0L)
                .setThirdBuildingType("")
                .setThirdBuildingAge("")
                .setThirdBuildingAgeId(0L)
                .setThirdBuildingExterior("")
                .setThirdBuildingExteriorId(0L)
                .setThirdBuildingLobby("")
                .setThirdBuildingLobbyId(0L)
                .setThirdBuildingGarage("")
                .setThirdBuildingGarageId(0L)
                .setThirdBuildingHall("")
                .setThirdBuildingHallId(0L)
                .setThirdBuildingBrand("")
                .setThirdBuildingBrandId(0L)
                .setThirdBuildingRating("")
                .setThirdBuildingRatingId(0L)
                .setThirdBuildingSettled("")
                .setThirdBuildingSettledId(0L)
                .setDailyPriceInput(BigDecimal.ZERO)
                .setThirdDailyPrice("")
                .setThirdDeliveryDate("")
                .setGradeName("")
                .setLocationName("")
                .setExteriorName("")
                .setLobbyName("")
                .setGarageName("")
                .setHallName("")
                .setBrandName("")
                .setRatingName("")
                .setSettledName("");
    }

}
