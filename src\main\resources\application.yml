server:
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 300
      min-spare: 4
  port: 6004
  servlet:
    context-path: /
    session:
      cookie:
        http-only: true

spring:
  # 环境 dev|beta|master
  profiles:
    active: dev
  session:
    store-type: none
  application:
    name: meht
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: ALWAYS
    serialization:
      write-dates-as-timestamps: false
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      enabled: true
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  global-config:
    db-config:
      id-type: AUTO
      insert-strategy: not_null
      update-strategy: not_null
      where-strategy: not_null
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
