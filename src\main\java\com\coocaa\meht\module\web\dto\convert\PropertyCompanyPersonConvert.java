package com.coocaa.meht.module.web.dto.convert;


import com.coocaa.meht.module.web.dto.property.PropertyCompanyPersonParam;
import com.coocaa.meht.module.web.entity.PropertyCompanyPersonEntity;
import com.coocaa.meht.module.web.vo.property.PropertyCompanyPersonVO;
import org.mapstruct.Mapper;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface PropertyCompanyPersonConvert {
    PropertyCompanyPersonConvert INSTANCE = Mappers.getMapper(PropertyCompanyPersonConvert.class);

    /**
     * DTO转Entity
     */
    PropertyCompanyPersonEntity toEntity(PropertyCompanyPersonParam param);

    /**
     * Entity转VO
     */
    PropertyCompanyPersonVO toVO(PropertyCompanyPersonEntity entity);

    /**
     * Entity数组转VO数组
     */

    List<PropertyCompanyPersonVO> toVOs(List<PropertyCompanyPersonEntity> entities);

}
