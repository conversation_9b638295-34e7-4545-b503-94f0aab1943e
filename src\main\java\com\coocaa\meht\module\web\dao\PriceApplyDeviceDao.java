package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.web.entity.PriceApplyDeviceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 价格申请设备明细表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Mapper
public interface PriceApplyDeviceDao extends BaseMapper<PriceApplyDeviceEntity> {
    List<String> getPointByApplyNumber(@Param("applyNumber")String applyNumber);
}
