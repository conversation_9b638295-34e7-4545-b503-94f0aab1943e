package com.coocaa.meht.utils;

import com.lark.oapi.Client;
import com.lark.oapi.core.response.RawResponse;
import com.lark.oapi.service.approval.v4.model.*;
import com.lark.oapi.service.contact.v3.model.BatchUserReq;
import com.lark.oapi.service.contact.v3.model.BatchUserResp;
import com.lark.oapi.service.contact.v3.model.BatchUserRespBody;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 飞书审批工具类
 */
@Slf4j
@Component
public class FeiShuApprovalUtil {

    @Value("${price.approval.appId:appId}")
    private String appId;

    @Value("${price.approval.appSecret:appSecret}")
    private String appSecret;

    private Client client;

    @PostConstruct
    public void initializeClient() {
        this.client = Client.newBuilder(appId, appSecret).build();
    }


    /**
     * 创建审批实例
     *
     * @param approvalCode 审批编码
     * @param userId       飞书用户ID
     * @param openId       飞书用户openid  非必传，如果有则以openid为准
     * @param uuid         申请唯一标识，回调时需要对应
     * @param formData     表单数据  示例："[{\"id\":\"widget17337344132670001\",\"type\":\"input\",\"value\":\"这里展示超链接\"}]"
     */
    public String createApprovalInstance(String approvalCode, String userId, String openId, String formData, String uuid) throws Exception {
        // 创建请求对象
        CreateInstanceReq req = CreateInstanceReq.newBuilder().instanceCreate(InstanceCreate.newBuilder().approvalCode(approvalCode).userId(userId).openId(openId).uuid(uuid).form(formData).build()).build();

        CreateInstanceResp resp = client.approval().instance().create(req);
        // 处理服务端错误
        if (!resp.success()) {
            log.warn("创建审批失败，approvalCode ：{}，userId ：{}，openId ：{}，formData ：{}", approvalCode, userId, openId, formData);
            log.error("创建审批失败,失败原因：{}", resp.getMsg());
            throw new IllegalStateException("创建审批实例失败，uuid" + uuid + "" + resp.getMsg());
        }
        return resp.getData().getInstanceCode();
    }


    /**
     * 获取审批定义
     *
     * @param approvalCode 审批定义编码
     */
    public List<ApprovalNodeInfo> getApprovalDefinition(String approvalCode) {
        List<ApprovalNodeInfo> result = new ArrayList<>();
        try {
            // 创建请求对象
            GetApprovalReq req = new GetApprovalReq();
            req.setApprovalCode(approvalCode);
            // 发起请求
            GetApprovalResp resp = client.approval().approval().get(req);
            // 处理服务端错误
            if (!resp.success()) {
                log.warn("获取审批定义失败，approvalCode ：{}", approvalCode);
                return result;
            }

            GetApprovalRespBody data = resp.getData();
            if (data == null) {
                return result;
            }
            ApprovalNodeInfo[] nodeList = data.getNodeList();
            if (nodeList == null) {
                return result;
            }
            // 将最后一个元素添加到列表开头, 如[2,3,4,1]变成[1,2,3,4]
            ApprovalNodeInfo lastNode = nodeList[nodeList.length - 1];
            result.add(0, lastNode);
            // 将剩余元素依次添加到列表末尾
            for (int i = 0; i < nodeList.length - 1; i++) {
                result.add(nodeList[i]);
            }
            return result;

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 订阅审批
     *
     * @param approvalCode 审批定义Code
     */
    public void subscribeApprovalEvent(String approvalCode) {
        // 订阅审批事件
        try {
            SubscribeApprovalReq req = SubscribeApprovalReq.newBuilder().approvalCode(approvalCode).build();
            SubscribeApprovalResp resp = client.approval().approval().subscribe(req);
            RawResponse rawResponse = resp.getRawResponse();
            if (rawResponse.getStatusCode() != 200) {
                log.info("审批订阅失败，审批编码：{},订阅结果 code：{}，订阅消息 msg：{}", approvalCode, resp.getCode(), resp.getMsg());
            } else {
                log.info("审批订阅成功，审批编码：{},订阅结果 code：{}，订阅消息 msg：{}", approvalCode, resp.getCode(), resp.getMsg());
            }
        } catch (Exception e) {
            log.error("订阅审批事件异常，approvalCode：{}", approvalCode, e);
            throw new RuntimeException("订阅审批事件失败", e);
        }
    }

    /**
     * 取消审批订阅
     *
     * @param approvalCode 审批定义Code
     */
    public void unsubscribeApprovalEvent(String approvalCode) {
        try {
            UnsubscribeApprovalReq req = UnsubscribeApprovalReq.newBuilder().approvalCode(approvalCode).build();
            UnsubscribeApprovalResp resp = client.approval().approval().unsubscribe(req);
            RawResponse rawResponse = resp.getRawResponse();
            if (rawResponse.getStatusCode() != 200) {
                log.info("取消审批订阅失败，审批编码：{},订阅结果 code：{}，订阅消息 msg：{}", approvalCode, resp.getCode(), resp.getMsg());
            } else {
                log.info("取消审批订阅成功，审批编码：{},订阅结果 code：{}，订阅消息 msg：{}", approvalCode, resp.getCode(), resp.getMsg());
            }
        } catch (Exception e) {
            log.error("取消订阅审批事件异常，approvalCode：{}", approvalCode, e);
            throw new RuntimeException("取消订阅审批事件失败", e);
        }

    }

    /**
     * 获取审批实例
     *
     * @param instanceCode 审批实例Code
     */
    public GetInstanceRespBody getApprovalInstance(String instanceCode) {
        try {
            GetInstanceReq req = GetInstanceReq.newBuilder().instanceId(instanceCode).build();
            GetInstanceResp resp = client.approval().instance().get(req);
            if (!resp.success()) {
                log.warn("获取审批实例失败，instanceCode ：{}", instanceCode);
                throw new IllegalStateException("获取审批实例失败，instanceCode: " + instanceCode);
            }
            log.info("获取审批实例成功，instanceCode ：{}", instanceCode);
            return resp.getData();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取用户相关信息
     *
     * @param userIds 用户ID集合
     */
    public Map<String, String> getUserInfoByUserIds(List<String> userIds) {
        Map<String, String> userMap = new HashMap<>();
        try {
            if (CollectionUtils.isEmpty(userIds)) {
                return userMap;
            }
            BatchUserReq req = BatchUserReq.newBuilder().userIds(userIds.toArray(new String[0])).build();
            BatchUserResp resp = client.contact().user().batch(req);
            log.info("getUserInfoByUserIds");
            BatchUserRespBody data = resp.getData();
            User[] items = data.getItems();
            if (items == null) {
                return userMap;
            }
            for (User user : items) {
                userMap.put(user.getOpenId(), user.getName());
            }
            return userMap;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
