package com.coocaa.meht.module.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.sys.dao.StsTokenDao;
import com.coocaa.meht.module.sys.entity.StsTokenEntity;
import com.coocaa.meht.module.sys.service.StsTokenService;
import org.apache.groovy.util.Maps;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024-11-06 18:06
 */
@Service
public class StsTokenServiceImpl extends ServiceImpl<StsTokenDao, StsTokenEntity> implements StsTokenService {

    @Override
    public List<StsTokenEntity> getListByUser(String empCode, String platform) {
        return this.listByMap(Maps.of("user_code", empCode, "platform", platform));
    }

    @Override
    public void delete(String empCode, String platform) {
        Map<String, Object> map = new HashMap<>();
        map.put("user_code", empCode);
        if (platform != null) {
            map.put("platform", platform);
        }
        this.removeByMap(map);
    }

}
