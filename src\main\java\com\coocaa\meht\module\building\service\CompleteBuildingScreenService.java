package com.coocaa.meht.module.building.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.building.entity.CompleteBuildingScreenEntity;
import com.coocaa.meht.module.web.dto.RatingApplyDto;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17
 */
public interface CompleteBuildingScreenService extends IService<CompleteBuildingScreenEntity> {

    void saveOrUpdate(String completeRatingNo, RatingApplyDto applyDto,String submitCoefficient);

    Boolean isScreenLarge(CompleteBuildingScreenEntity entity);

}
