package com.coocaa.meht.common;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user")
public class UserEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 工号
     */
    private String wno;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 登陆名
     */
    private String userName;

    /**
     * 密码;使用MD5加密
     */
    private String password;

    /**
     * 状态 [0:禁用, 1:启用]
     */
    private Boolean status;

    /**
     * 最后修改密码时间
     */
    private Date changePasswordTime;

    /**
     * 用户被锁 [0:未锁, 1:已锁]
     */
    private Boolean locked;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 用户类型：1-内部用户；2-外部代理商
     */
    private Integer type;


}
