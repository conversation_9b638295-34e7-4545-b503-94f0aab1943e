package com.coocaa.meht.common.handler;

import com.coocaa.meht.utils.AesUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04
 */
@Slf4j
public class EncryptHandler extends BaseTypeHandler<String> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, encrypt(parameter));
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return decrypt(rs.getString(columnName));
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return decrypt(rs.getString(columnIndex));
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return decrypt(cs.getString(columnIndex));
    }

    private String encrypt(String value) {
        try {
            return StringUtils.isEmpty(value) ? null : AesUtils.encryptHex(value);
        } catch (Exception ex) {
            log.warn("加密存储数据({})错误", value, ex);
            return value;
        }
    }

    private String decrypt(String value) {
        try {
            return StringUtils.isEmpty(value) ? null : AesUtils.decryptStr(value);
        } catch (Exception ex) {
            log.warn("解密存储数据({})错误", value, ex);
            return value;
        }
    }
}
