package com.coocaa.meht.module.web.vo.kanban;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 指标
 */
@Data
@Accessors(chain = true)
public class StatusChangeVO {
    /**
     * ID
     */
    private Integer id;

    /**
     * 数据类型
     */
    private String type;

    /**
     * 数据类型子类型
     */
    private Integer subType;

    /**
     * 业务ID (楼宇,客户,商机,...)
     */
    private Integer bizId;


    /**
     * 业务ID (楼宇,客户,商机,...)
     */
    private Set<Integer> bizIds;

    /**
     * 业务状态(字典xx)
     */
    private String status;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 状态变更时间
     */
    private LocalDateTime changeTimeEnd;

    /**
     * 状态变更开始时间
     */
    private LocalDateTime changeTimeStart;
}
