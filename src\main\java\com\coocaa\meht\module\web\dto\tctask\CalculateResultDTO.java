package com.coocaa.meht.module.web.dto.tctask;

import java.math.BigDecimal;

import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class CalculateResultDTO extends BuildingDetailsEntity {

    private BigDecimal buildingScore;
    private BigDecimal buildingAiScore;

    private String projectAiLevel;
    private String projectLevel;

    private BigDecimal firstFloorExclusiveScore;
    private BigDecimal firstFloorShareScore;
    private BigDecimal negativeFirstFloorScore;
    private BigDecimal negativeTwoFloorScore;
    private BigDecimal twoFloorAboveScore;
    private BigDecimal thirdFloorBelowScore;

}
