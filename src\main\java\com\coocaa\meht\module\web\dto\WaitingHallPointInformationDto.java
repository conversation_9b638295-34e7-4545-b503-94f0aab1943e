package com.coocaa.meht.module.web.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/17
 * @description 等候厅点位
 */
@Data
public class WaitingHallPointInformationDto {
    @Schema(description = "点位id", type = "String", example = "FZ000402")
    private Integer pointId;
    @Schema(description = "等候厅id", type = "String", example = "FZ000402")
    private Integer waitingHallId;

    @Schema(description = "点位编码", type = "String", example = "2栋_1单元_1层_大堂_1号电梯厅_点位2")
    private String pointCode;

    @Schema(description = "描述", type = "String", example = "FZ000402")
    private String remark ;

    @Schema(description = "点位名称", type = "String", example = "FZ000402")
    private String pointName;

    @Schema(description = "设备尺寸", type = "String", example = "FZ000402")
    private String deviceSize;

    /**
     * 图片
     */
    private List<String> picture;
}
