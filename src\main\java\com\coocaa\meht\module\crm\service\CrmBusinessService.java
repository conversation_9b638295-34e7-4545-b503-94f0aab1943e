package com.coocaa.meht.module.crm.service;

import com.coocaa.meht.common.KeyValue;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.crm.dto.CmsBusinessDto;
import com.coocaa.meht.module.crm.dto.CrmBusinessDto;
import com.coocaa.meht.module.crm.dto.CrmBusinessFlowDto;
import com.coocaa.meht.module.crm.dto.req.CmsBusinessReq;
import com.coocaa.meht.module.crm.dto.req.CrmBusinessReq;
import com.coocaa.meht.module.crm.vo.BusinessVO;
import com.coocaa.meht.module.web.dto.req.BusinessReq;

import java.util.List;

/**
 * 商机信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-25
 */
public interface CrmBusinessService {

    /**
     * 查询商机列表
     *
     * @param req 查询参数
     * @return 商机列表
     */
    PageResult<BusinessVO> list(CrmBusinessReq req);

    /**
     * 查询crm商机列表
     *
     * @param req 查询参数
     * @return 商机列表
     */
    PageResult<CrmBusinessDto> crmList(CrmBusinessReq req);

    /**
     * 查询商机详情
     *
     * @param businessCode 商机code
     * @return 商机详情
     */
    Result<BusinessVO> getDetail(String businessCode);



    /**
     * 根据商机ID查询流程列表
     *
     * @param businessId 商机ID
     * @return 流程列表
     */
    Result<List<CrmBusinessFlowDto>> getFlows(String businessId);

    /**
     * 查询商机查询场景列表
     *
     * @return 场景列表
     */
    Result<List<KeyValue<String, String>>> listQueryScenes();

    /**
     * cms 系统通过手机号查询客户信息
     * @param cmsBusinessReq
     * @return
     */
    PageResult<CmsBusinessDto> listCmsBusinessByPhone(CmsBusinessReq cmsBusinessReq);


    /**
     * 添加商机
     */
    Result<Boolean> addBusiness(BusinessReq req);
    /**
     * 完善商机
     */
    Result<Boolean> updateBusiness(BusinessReq req);


    /**
     * 客户商机状态校验
     */
    Result<Boolean> checkBusinessStatus(String buildingNo);

}
