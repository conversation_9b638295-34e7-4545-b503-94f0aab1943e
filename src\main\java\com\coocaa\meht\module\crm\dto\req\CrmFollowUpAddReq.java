package com.coocaa.meht.module.crm.dto.req;


import com.coocaa.meht.common.KeyValue;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class CrmFollowUpAddReq {

    /**
     * 跟进方式: 1.面访 2.电话
     */
    @Schema(description = "跟进方式: 1.面访 2.电话", type = "String", example = "1")
    @NotBlank(message = "跟进方式不能为空")
    private String category;

    /**
     * 沟通目的
     */
    @NotBlank(message = "沟通目的不能为空")
    @Schema(description = "沟通目的", type = "String", example = "1")
    private String content;

    private String batchId;

    private Integer activityType = 2;

    /**
     * 客户id
     */
    @Schema(description = "客户id", type = "String", example = "1")
    @NotBlank(message = "客户id不能为空")
    private String activityTypeId;

    /**
     * 客户名称(楼宇名称)
     */
    @Schema(description = "客户名称(楼宇名称)", type = "String", example = "1")
    @NotBlank(message = "楼宇名称不能为空")
    private String titleName;

    /**
     * 商机编码
     */
    @NotBlank(message = "商机编码不能为空")
    @Schema(description = "商机编码", type = "String", example = "BCxxxxxxxx")
    private String businessCode;

    /**
     * 客户跟进记录id
     */
    @Schema(description = "客户跟进记录id", type = "String", example = "1")
    private Integer id;


    @Schema(description = "客户跟进记录图片")
    private List<String> customerFollowRecordPicList;

    @Schema(description = "客户跟进记录电话")
    @NotBlank(message = "电话不能为空")
    private String phone;

    @Schema(description = "拜访角色")
    @NotBlank(message = "拜访角色不能为空")
    private String role;


    private List<KeyValue<String, String>> paramList = new ArrayList<>();

    private List<FieldDto> field;


    @Data
    @Accessors(chain = true)
    public static class FieldDto {

        private String fieldId;

        private String fieldName;

        private Integer fieldType;

        private String name;

        private Integer type;

        private String value;

    }
}
