package com.coocaa.meht.config;

import com.coocaa.meht.common.bean.BigScreenCalculateRule;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description  大屏配置
 * @since 2025-04-16
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "large-screen")
@EnableConfigurationProperties
public class LargeScreenProperties {

    // 大屏设备key
    private List<String> largeDictKey;

    // 大屏点位系数
    private List<String> coefficient;

    // 审核人
    private List<String> auditUsers;

    // 价格申请审核人
    private List<String> priceApplyAuditors;

    // 大屏点位系数计算规则
    private List<BigScreenCalculateRule> calculateRule;


}
