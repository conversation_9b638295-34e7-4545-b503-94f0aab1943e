package com.coocaa.meht.utils;

import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.Signature;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024-11-20 13:59
 */
public class RsaExample {
    private static final String ALGORITHM = "RSA";

    private final int keySize;
    private final PrivateKey privateKey;
    private final PublicKey publicKey;

    public RsaExample(int keySize) {
        this.keySize = keySize;
        KeyPair keyPair = generateKey(keySize);
        this.publicKey = keyPair.getPublic();
        this.privateKey = keyPair.getPrivate();
    }

    public RsaExample(String publicKey, String privateKey) {
        try {
            byte[] pubKey = Base64.getDecoder().decode(publicKey);
            byte[] priKey = Base64.getDecoder().decode(privateKey);
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
            this.publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(pubKey));
            this.privateKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(priKey));
            this.keySize = ((RSAPrivateKey) this.privateKey).getModulus().bitLength();
        } catch (Exception e) {
            throw new RuntimeException(String.format("初始化rsa密钥异常：[%s] [%s]", publicKey, privateKey), e);
        }
    }

    public static KeyPair generateKey(int keySize) {
        KeyPairGenerator keygen;
        try {
            keygen = KeyPairGenerator.getInstance(ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("RSA初始化密钥出现异常");
        }
        SecureRandom secrand = new SecureRandom();
        secrand.setSeed("Alian".getBytes());
        keygen.initialize(keySize, secrand);
        return keygen.genKeyPair();
    }

    public static Map<String, String> generateKeyStr(int keySize) {
        KeyPair keyPair = generateKey(keySize);
        Base64.Encoder encoder = Base64.getEncoder();
        Map<String, String> keyPairMap = new HashMap<>();
        keyPairMap.put("publicKey", encoder.encodeToString(keyPair.getPublic().getEncoded()));
        keyPairMap.put("privateKey", encoder.encodeToString(keyPair.getPrivate().getEncoded()));
        return keyPairMap;
    }

    /**
     * 公钥加密
     *
     * @param data
     * @return
     */
    public String encryptByPublic(String data) {
        if (StringUtils.isBlank(data)) {
            return data;
        }
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, this.publicKey);
            return Base64.getEncoder().encodeToString(cipher.doFinal(data.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            throw new RuntimeException("RSA公钥加密异常:", e);
        }
    }

    /**
     * 私钥加密
     *
     * @param data
     * @return
     */
    public String encryptByPrivate(String data) {
        if (StringUtils.isBlank(data)) {
            return data;
        }
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, this.privateKey);
            return Base64.getEncoder().encodeToString(cipher.doFinal(data.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            throw new RuntimeException("RSA私钥加密异常:", e);
        }
    }

    /**
     * 公钥解密
     *
     * @param data
     * @return
     */
    public String decryptByPublic(String data) {
        if (StringUtils.isBlank(data)) {
            return data;
        }
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, this.publicKey);
            return new String(cipher.doFinal(Base64.getDecoder().decode(data)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("RSA公钥解密异常:", e);
        }
    }

    /**
     * 私钥解密
     *
     * @param data
     * @return
     */
    public String decryptByPrivate(String data) {
        if (StringUtils.isBlank(data)) {
            return data;
        }
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, this.privateKey);
            return new String(cipher.doFinal(Base64.getDecoder().decode(data)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("RSA私钥解密异常:", e);
        }
    }

    /**
     * RSA2签名（私钥签名）
     *
     * @param data
     * @return
     */
    public String sign(String data) {
        try {
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(this.privateKey);
            signature.update(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(signature.sign());
        } catch (Exception e) {
            throw new RuntimeException("RSA签名异常:", e);
        }
    }

    /**
     * SA2校验签名（公钥验签）
     *
     * @param data
     * @param sign
     * @return
     */
    public boolean verify(String data, String sign) {
        try {
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initVerify(this.publicKey);
            signature.update(data.getBytes(StandardCharsets.UTF_8));
            return signature.verify(sign.getBytes());
        } catch (Exception e) {
            throw new RuntimeException("RSA公钥验证签名异常:", e);
        }
    }

    public String getPrivateKey() {
        return Base64.getEncoder().encodeToString(this.privateKey.getEncoded());
    }

    public String getPublicKey() {
        return Base64.getEncoder().encodeToString(this.publicKey.getEncoded());
    }

    public int getKeySize() {
        return keySize;
    }

}
