package com.coocaa.meht.utils;

import cn.hutool.core.util.StrUtil;

/**
 * 楼宇地址处理工具
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-19
 */
public class AddressUtil {

    /**
     * 简化地址，删除多余的省，市，区
     *
     * @param address
     * @param provence
     * @param city
     * @param region
     * @return
     */
    public static String simplify(String address, String provence, String city, String region) {
        try {
            return doSimplify(address, provence, city, region);
        } catch (Exception ignored) {
        }
        return address;
    }

    private static String doSimplify(String address, String provence, String city, String region) {
        if (StrUtil.isBlank(address)) {
            return address;
        }

        int provenceIndex = address.indexOf(provence);
        provenceIndex = provenceIndex < 0 ? provenceIndex : provenceIndex + provence.length() - 1;
        int cityIndex = address.indexOf(city);
        cityIndex = cityIndex < 0 ? cityIndex : cityIndex + city.length() - 1;
        int regionIndex = address.indexOf(region);
        regionIndex = regionIndex < 0 ? regionIndex : regionIndex + region.length() - 1;

        // 取最大的index
        int index = Math.max(provenceIndex, Math.max(cityIndex, regionIndex));
        if (index < 0) {
            return address;
        }

        String subAddress = address.substring(index + 1);
        return removePrefix(subAddress, "-");
    }

    private static String removePrefix(String address, String prefix) {
        if (StrUtil.isBlank(address)) {
            return address;
        }

        if (address.startsWith(prefix)) {
            String substring = address.substring(prefix.length());
            return removePrefix(substring, prefix);
        }

        return address;
    }

}
