package com.coocaa.meht.module.api.ldap;

import com.coocaa.magazine.utils.LdapUtil;
import com.novell.ldap.LDAPEntry;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@AllArgsConstructor
public class LdapApiServiceImpl implements LdapApiService {
    private static final String LDAP_MAIL = "mail";
    private static final String LDAP_NAME = "name";
    private static final String LDAP_MOBILE = "mobile";

    private final LdapUtil ldapUtil = new LdapUtil();

    /**
     * 域登录获取用户信息
     *
     * @param username
     * @param password
     * @return
     */
    @Override
    public LdapUserDto getUser(String username, String password) {
        LDAPEntry data = ldapUtil.validAndGetUser(username, password);
        if (Objects.isNull(data)) {
            return null;
        }
        LdapUserDto ldapUserDto = new LdapUserDto();
        String email;
        if (data.getAttribute(LDAP_MAIL) != null) {
            email = data.getAttribute(LDAP_MAIL).getStringValue();
        } else {
            email = data.getAttribute("userPrincipalName").getStringValue();
        }
        ldapUserDto.setEmail(email);
        if (data.getAttribute(LDAP_NAME) != null) {
            ldapUserDto.setName(data.getAttribute(LDAP_NAME).getStringValue());
        }
        if (data.getAttribute(LDAP_MOBILE) != null) {
            ldapUserDto.setMobile(data.getAttribute(LDAP_MOBILE).getStringValue());
        }
        return ldapUserDto;
    }

    /**
     * 域登录是否有效
     *
     * @param username
     * @param password
     * @return
     */
    @Override
    public boolean isValidLdap(String username, String password) {
        return ldapUtil.validateUser(username, password);
    }

}
