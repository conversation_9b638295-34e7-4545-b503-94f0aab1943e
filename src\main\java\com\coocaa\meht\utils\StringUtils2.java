package com.coocaa.meht.utils;

/**
 * <AUTHOR>
 * @since 2024-10-22
 */

public class StringUtils2 {
    private StringUtils2() {

    }

    public static String file4Code(int code) {
        return fileCode(code, 4);
    }

    public static String file3Code(int code) {
        return fileCode(code, 3);
    }

    public static String file2Code(int code) {
        return fileCode(code, 2);
    }

    /**
     * 2位以下补0
     */
    public static String fillPointCode(int number) {
        if (number >= 0 && number < 10) {
            return "0" + number;
        }
        return String.valueOf(number);

    }

    public static String fileCode(int code, int length) {
        return String.format("%0" + length + "d", code);
    }

    public static String getEnableMsg(Boolean enable) {
        return enable ? "启用" : "禁用";
    }

    public static String file6Code(int number) {
        return fileCode(number, 6);
    }
    public static String file5Code(int number) {
        return fileCode(number, 5);
    }

}
