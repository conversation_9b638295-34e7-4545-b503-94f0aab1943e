package com.coocaa.meht.aop;

import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.module.sys.entity.SysRequestLogEntity;
import com.coocaa.meht.module.sys.service.SysRequestLogService;
import com.coocaa.meht.utils.ExceptionUtils;
import com.coocaa.meht.utils.IpUtil;
import com.coocaa.meht.utils.JsonUtils;
import com.coocaa.meht.utils.ServletUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023-12-07 20:28
 */
@Aspect
@Component
public class WebLogAspect {
    private static final Logger log = LoggerFactory.getLogger(WebLogAspect.class);

    @Resource
    private SysRequestLogService sysRequestLogService;

    @Pointcut("@annotation(com.coocaa.meht.aop.Reqlog)")
    public void commonPointCut() {
    }

    @Around(value = "commonPointCut() && @annotation(reqlogAnnotation)")
    public Object saveLog(ProceedingJoinPoint pjp, Reqlog reqlogAnnotation) throws Throwable {
        if (reqlogAnnotation == null) {
            return pjp.proceed();
        }
        HttpServletRequest request = ServletUtils.getRequest();
        if (request == null) {
            return pjp.proceed();
        }
        LoginUser user = SecurityUser.getUserAnonymity();
        SysRequestLogEntity requestLog = new SysRequestLogEntity();
        requestLog.setTypesOf(reqlogAnnotation.type().getKey());
        requestLog.setTitle(reqlogAnnotation.value());
        requestLog.setUserCode(user == null ? "" : user.getUserCode());
        requestLog.setRequestUrl(request.getRequestURI());
        requestLog.setIp(IpUtil.getIpAddr(request));
        UserAgent ua = UserAgentUtil.parse(request.getHeader("User-Agent"));
        requestLog.setOs(ua.getOs().isUnknown() ? "" : ua.getOs().getName());
        requestLog.setBrowser(ua.getBrowser().isUnknown() ? "" : ua.getBrowser().getName());
        requestLog.setPlatform(requestLog.getRequestUrl().startsWith("/app") ? "app"
                : requestLog.getRequestUrl().startsWith("/server-api") ? "server-api"
                : requestLog.getRequestUrl().startsWith("/fs") ? "fs"
                : "web");
        requestLog.setCreateTime(LocalDateTime.now());
        requestLog.setRequestParam(montage(ServletUtils.getParamToStr(request),
                getBody(ServletUtils.getBody(request), reqlogAnnotation)));
        long start = System.currentTimeMillis();
        try {
            Object obj = pjp.proceed();
            requestLog.setTime((int) (System.currentTimeMillis() - start));
            requestLog.setStatus(getResult(obj));
            if (reqlogAnnotation.type() == Reqlog.LogType.LOGIN) {
                user = SecurityUser.getUser();
                requestLog.setUserCode(user.getUserCode());
            }
            sysRequestLogService.saveAsync(requestLog);
            outInfo(requestLog, request);
            return obj;
        } catch (Throwable e) {
            requestLog.setTime((int) (System.currentTimeMillis() - start));
            requestLog.setStatus(1);
            requestLog.setErrorLog(ExceptionUtils.getMessage(e));
            sysRequestLogService.saveAsync(requestLog);
            outInfo(requestLog, request);
            throw e;
        }
    }

    private String getBody(String body, Reqlog reqlog) {
        if (StringUtils.isNotBlank(body) && reqlog.type() == Reqlog.LogType.LOGIN) {
            try {
                Map<String, Object> paramMap = JsonUtils.fromMap(body);
                paramMap.remove("password");
                return JsonUtils.toJson(paramMap);
            } catch (Exception ignored) {
            }
        }
        return body;
    }

    private String montage(String param, String body) {
        return "{\"param\": \"" + param + "\", \"body\": " + body + "}";
    }

    private void outInfo(SysRequestLogEntity logOp, HttpServletRequest request) {
        log.info("     \nURL           : {} {} {} ms\n" +
                        "Param         : {}\n",
                request.getMethod(), logOp.getRequestUrl(), logOp.getTime(),
                logOp.getRequestParam()
        );
    }

    private int getResult(Object obj) {
        if (obj instanceof Result) {
            Result<?> r = (Result<?>) obj;
            return r.getCode() == 200 ? 0 : 1;
        }
        return 0;
    }

}
