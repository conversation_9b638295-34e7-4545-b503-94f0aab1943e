package com.coocaa.meht.module.web.service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description  
 * @since 2025-04-29
 */
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.module.web.dto.CommentDTO;
import com.coocaa.meht.module.web.dto.CommentCreateDTO;
import com.coocaa.meht.module.web.dto.CommentQueryDto;

/**
 * 评论服务接口
 */
public interface CommentService {
    
    /**
     * 创建评论
     *
     * @param request 评论创建请求
     * @param userId 当前用户ID
     * @return 评论ID
     */
    Integer createComment(CommentCreateDTO request, String userId);
    
    /**
     * 删除评论
     *
     * @param commentId 评论ID
     * @param userId 当前用户ID
     * @return 是否成功
     */
    Boolean deleteComment(Integer commentId, String userId);
    
    /**
     * 分页查询评论
     *
     * @param request 查询请求
     * @return 评论分页列表
     */
    Page<CommentDTO> pageComments(CommentQueryDto request);
    
    /**
     * 获取评论详情
     *
     * @param commentId 评论ID
     * @return 评论详情
     */
    CommentDTO getCommentDetail(Integer commentId);
    
    /**
     * 获取评论总数
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 评论总数
     */
    Long countComments(Integer businessType, String businessId);
} 