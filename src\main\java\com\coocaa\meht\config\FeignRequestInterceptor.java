package com.coocaa.meht.config;

import cn.hutool.core.util.StrUtil;
import com.coocaa.ad.common.core.context.TokenThreadLocal;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.config.filter.RequestIdFilter;
import com.coocaa.meht.utils.TokenUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

/**
 * Feign请求拦截器，用于传递请求头信息
 */
@Slf4j
@Deprecated
public class FeignRequestInterceptor implements RequestInterceptor {

    private static final String TOKEN_HEADER = "Token";
    private static final String INNER = "inner";

    @Override
    public void apply(RequestTemplate template) {
        // 传递token
        template.header("token", TokenThreadLocal.getToken());

        // 传递请求ID
        String requestId = MDC.get(RequestIdFilter.REQUEST_ID_KEY);
        if (StrUtil.isNotBlank(requestId)) {
            template.header(RequestIdFilter.REQUEST_ID_HEADER, requestId);
//            log.debug("传递请求ID到下游服务: {}, 请求ID: {}", template.url(), requestId);
        } else {
            // 如果没有请求ID（例如定时任务发起的请求），则生成一个新的
            String newRequestId = generateRequestId();
            template.header(RequestIdFilter.REQUEST_ID_HEADER, newRequestId);
//            log.debug("生成新的请求ID到下游服务: {}, 请求ID: {}", template.url(), newRequestId);
        }

        // 定时任务可能没有token
        template.header(INNER, INNER);
        log.debug("Feign请求已添加头信息: {}", template.url());
    }

    /**
     * 生成唯一请求ID
     */
    private String generateRequestId() {
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }
} 