package com.coocaa.meht.module.sys.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @Date 2024-11-08 15:24
 */
@Data
public class SysPhoneLoginDto {
    @NotBlank(message = "手机号不能为空")
    private String phone;
    @NotBlank(message = "验证码不能为空")
    private String captcha;
    @NotBlank(message = "hash不能为空")
    private String hash;

    private String platform;
}
