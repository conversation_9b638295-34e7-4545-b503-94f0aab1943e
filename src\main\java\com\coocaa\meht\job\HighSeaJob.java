package com.coocaa.meht.job;

import cn.hutool.core.collection.CollUtil;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 公海业务定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18
 */
@Deprecated
@Slf4j
@Component
@RefreshScope
public class HighSeaJob {

    @Autowired
    private BuildingRatingService buildingRatingService;

    @Autowired
    private IBuildingMetaService buildingMetaService;

    @Autowired
    private BusinessOpportunityService businessOpportunityService;

    @Autowired
    private FeignCmsRpc feignCmsRpc;

    /**
     * 入公海天数阈值
     */
    @Value("${high-sea.enter.threshold:20}")
    public Integer threshold;

    /**
     * 客户数据入公海定时任务
     */
    public void enterSea() {
        log.info("入公海定时任务开始");
        // 入公海时间阈值，入公海计算起始时间早于该时间的数据，需要进行进入公海判断
        LocalDateTime latestTime = LocalDateTime.now().minusDays(threshold);
        log.info("入公海时间阈值:{}", latestTime);

        List<BuildingRatingEntity> buildingRatings = buildingRatingService.lambdaQuery()
                .select(
                        BuildingRatingEntity::getId,
                        BuildingRatingEntity::getBuildingNo,
                        BuildingRatingEntity::getBuildingName,
                        BuildingRatingEntity::getSubmitUser)
                .eq(BuildingRatingEntity::getHighSeaFlag, BuildingRatingEntity.HighSeaFlagEnum.NO.getCode())
                .in(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.getValue(),
                        BuildingRatingEntity.Status.AUDITED.getValue(), BuildingRatingEntity.Status.REJECTED.getValue())
                .lt(BuildingRatingEntity::getEnterSeaCalculateTime, latestTime)
                .list();
        if (CollUtil.isEmpty(buildingRatings)) {
            log.info("楼宇数据为空");
            return;
        }

        Map<String, String> businessBuildingMapping = businessOpportunityService.lambdaQuery()
                .select(BusinessOpportunityEntity::getCode, BusinessOpportunityEntity::getBuildingNo)
                .in(BusinessOpportunityEntity::getBuildingNo, buildingRatings.stream().map(BuildingRatingEntity::getBuildingNo).toList())
                .list()
                .stream()
                .collect(Collectors.toMap(BusinessOpportunityEntity::getCode, BusinessOpportunityEntity::getBuildingNo));
        if (CollUtil.isEmpty(businessBuildingMapping)) {
            log.info("无有效数据");
            return;
        }

        List<String> invalidCodes = RpcUtils.unBox(feignCmsRpc.isContractSigned(businessBuildingMapping.keySet()));
        log.info("有签约合同的商机编号，{}", invalidCodes);

        // 不能掉公海的楼宇编号
        Set<String> excludeBuildingNos = invalidCodes.stream().map(businessBuildingMapping::get).collect(Collectors.toSet());

        // 过滤掉不能掉公海的数据
        List<String> buildingRatingNos = businessBuildingMapping.values().stream()
                .filter(buildingNo -> !excludeBuildingNos.contains(buildingNo))
                .distinct()
                .toList();

        if (CollUtil.isNotEmpty(buildingRatingNos)) {
            // 数据入公海，置空提交人
            buildingRatingService.lambdaUpdate()
                    .set(BuildingRatingEntity::getHighSeaFlag, BuildingRatingEntity.HighSeaFlagEnum.YES.getCode())
                    .set(BuildingRatingEntity::getSubmitUser, "")
                    .in(BuildingRatingEntity::getBuildingNo, buildingRatingNos)
                    .update();

            buildingMetaService.lambdaUpdate()
                    .set(BuildingMetaEntity::getManager, "")
                    .in(BuildingMetaEntity::getBuildingRatingNo, buildingRatingNos)
                    .update();

            businessOpportunityService.lambdaUpdate()
                    .set(BusinessOpportunityEntity::getSubmitUser, "")
                    .set(BusinessOpportunityEntity::getOwner, "")
                    .in(BusinessOpportunityEntity::getBuildingNo, buildingRatingNos)
                    .update();

            log.info("数据入公海执行成功,客户数量:{}", buildingRatingNos.size());
        }

        log.info("入公海定时任务结束");
    }

}
