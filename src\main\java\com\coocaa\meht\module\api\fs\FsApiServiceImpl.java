package com.coocaa.meht.module.api.fs;

import cn.hutool.core.util.StrUtil;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.utils.Converts;
import com.coocaa.meht.utils.HttpUtils;
import com.coocaa.meht.utils.JsonUtils;
import com.coocaa.meht.utils.RedisUtils;
import com.coocaa.meht.utils.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Component
public class FsApiServiceImpl implements FsApiService {

    private static final String TOKEN_KEY = "meht:fs:tenant_token";

    private static final ScheduledExecutorService scheduled = Executors.newSingleThreadScheduledExecutor();

    @Value("${fsAppId}")
    private String fsAppId;
    @Value("${fsAppSecret}")
    private String fsAppSecret;
    @Value("${fsDoMain}")
    private String fsDoMain;
    @Resource
    private RedisUtils redisUtils;
    @Resource(name = "asyncPool")
    private Executor executor;

    private static boolean isSuccess(Map<String, Object> request) {
        return request != null && "0".equals(Converts.toStr(request.get("code")));
    }

    /**
     * 获取TOKEN
     *
     * @return
     */
    @Override
    public String getTenantAccessToken() {
        String token = redisUtils.get(TOKEN_KEY);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        header.put("Connection", "keep-alive");
        Map<String, String> map = new HashMap<>();
        map.put("app_id", fsAppId);
        map.put("app_secret", fsAppSecret);
        String response = HttpUtils.post(fsDoMain + "/open-apis/auth/v3/tenant_access_token/internal", header,
                JsonUtils.toJson(map));
        log.info("getTenantAccessToken，请求参数" + JsonUtils.toJson(map));
        Map<String, Object> json = JsonUtils.fromMap(response);
        String tenantAccessToken = Converts.toStr(json.get("tenant_access_token"));
        if (StringUtils.isNotBlank(tenantAccessToken)) {
            Long expire = Converts.toLong(json.get("expire"));
            redisUtils.set(TOKEN_KEY, tenantAccessToken, expire - 5);
            return tenantAccessToken;
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, String> getUserToken(String code) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        header.put("Connection", "keep-alive");
        header.put("Authorization", "Bearer " + this.getTenantAccessToken());
        Map<String, Object> map = new HashMap<>();
        map.put("grant_type", "authorization_code");
        map.put("code", code);
        String response = HttpUtils.post(fsDoMain + "/open-apis/authen/v1/oidc/access_token", header, JsonUtils.toJson(map));
        log.info("getUserToken: " + response);
        Map<String, Object> json = JsonUtils.fromMap(response);
        if (!isSuccess(json)) {
            return null;
        }
        Map<String, Object> data = (Map<String, Object>) json.get("data");
        Map<String, String> result = new HashMap<>();
        result.put("accessToken", Converts.toStr(data.get("access_token")));
        result.put("refreshToken", Converts.toStr(data.get("refresh_token")));
        result.put("tokenType", Converts.toStr(data.get("token_type")));
        result.put("expiresIn", Converts.toStr(data.get("expires_in")));
        result.put("refreshExpiresIn", Converts.toStr(data.get("refresh_expires_in")));
        result.put("scope", Converts.toStr(data.get("scope")));
        return result;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getUserInfo(String userToken) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        header.put("Connection", "keep-alive");
        header.put("Authorization", "Bearer " + userToken);
        String response = HttpUtils.get(fsDoMain + "/open-apis/authen/v1/user_info", header);
        Map<String, Object> json = JsonUtils.fromMap(response);
        if (isSuccess(json)) {
            Map<String, Object> data = (Map<String, Object>) json.get("data");
            Map<String, Object> result = new HashMap<>();
            result.put("name", data.get("name"));
            result.put("enName", data.get("en_name"));
            result.put("avatarUrl", data.get("avatar_url"));
            result.put("avatarThumb", data.get("avatar_thumb"));
            result.put("avatarMiddle", data.get("avatar_middle"));
            result.put("avatarBig", data.get("avatar_big"));
            result.put("openId", data.get("open_id"));
            result.put("unionId", data.get("union_id"));
            result.put("email", data.get("email"));
            result.put("enterpriseEmail", data.get("enterprise_email"));
            result.put("userId", data.get("user_id"));
            result.put("mobile", data.get("mobile"));
            result.put("tenantKey", data.get("tenant_key"));
            result.put("employeeNo", data.get("employee_no"));
            return result;
        }
        return null;
    }

    /**
     * 获取用户信息
     *
     * @param userId
     * @return
     */
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getUserInfoByUserId(String userId) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        header.put("Connection", "keep-alive");
        header.put("Authorization", "Bearer " + getTenantAccessToken());
        String response = HttpUtils.get(fsDoMain + "/open-apis/contact/v3/users/" + userId + "?user_id_type=user_id", header);
        Map<String, Object> json = JsonUtils.fromMap(response);
        if (isSuccess(json)) {
            Map<String, Object> data = (Map<String, Object>) json.get("data");
            Map<String, Object> user = (Map<String, Object>) data.get("user");
            Map<String, Object> avatar = (Map<String, Object>) user.get("avatar");
            String avatarUrl = Converts.toStr(avatar.get("avatar_240"));
            Map<String, Object> result = new HashMap<>();
            result.put("unionId", user.get("union_id"));
            result.put("userId", user.get("user_id"));
            result.put("openId", user.get("open_id"));
            result.put("name", user.get("name"));
            result.put("nickname", user.get("nickname"));
            result.put("email", user.get("email"));
            result.put("mobile", user.get("mobile"));
            result.put("gender", user.get("gender"));
            result.put("avatar", avatarUrl);
            return result;
        }
        return null;
    }

    /**
     * 根据邮箱或手机号批量获取UserId
     *
     * @param emails
     * @param mobiles
     * @return
     */
    @Override
    public Map<String, String> getUserIds(List<String> emails, List<String> mobiles) {
        int size = 50;
        Map<String, String> mapVo = new HashMap<>();
        List<List<String>> partitionEmails = Collections.emptyList();
        List<List<String>> partitionMobiles = Collections.emptyList();
        if (emails != null) {
            partitionEmails = IntStream.range(0, (emails.size() + size - 1) / size)
                    .mapToObj(i -> emails.subList(i * size, Math.min((i + 1) * size, emails.size())))
                    .collect(Collectors.toList());
        }
        if (mobiles != null) {
            partitionMobiles = IntStream.range(0, (mobiles.size() + size - 1) / size)
                    .mapToObj(i -> mobiles.subList(i * size, Math.min((i + 1) * size, mobiles.size())))
                    .collect(Collectors.toList());
        }
        int leg = Math.max(partitionEmails.size(), partitionMobiles.size());
        for (int i = 0; i < leg; i++) {
            try {
                Collection<String> e = null;
                Collection<String> m = null;
                if (i < partitionEmails.size()) {
                    e = partitionEmails.get(i);
                }
                if (i < partitionMobiles.size()) {
                    m = partitionMobiles.get(i);
                }
                mapVo.putAll(getUserIdsMax50(e, m));
            } catch (Exception ex) {
                log.error("获取飞书userI异常:", ex);
            }
        }
        return mapVo;
    }

    private Map<String, String> getUserIdsMax50(Collection<String> emails, Collection<String> mobiles) {
        Map<String, Object> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(emails)) {
            map.put("emails", emails);
        }
        if (!CollectionUtils.isEmpty(mobiles)) {
            map.put("mobiles", mobiles);
        }
        if (!map.isEmpty()) {
            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", "application/json; charset=utf-8");
            header.put("Connection", "keep-alive");
            header.put("Authorization", "Bearer " + this.getTenantAccessToken());
            String response = HttpUtils.post(fsDoMain + "/open-apis/contact/v3/users/batch_get_id?user_id_type=user_id",
                    header, JsonUtils.toJson(map));
            Map<String, Object> json = JsonUtils.fromMap(response);
            if (isSuccess(json)) {
                Map<?, ?> data = (Map<?, ?>) json.get("data");
                if (!CollectionUtils.isEmpty(data)) {
                    List<?> userList = (List<?>) data.get("user_list");
                    if (!CollectionUtils.isEmpty(userList)) {
                        Map<String, String> mapVo = new HashMap<>();
                        userList.forEach(ele -> {
                            Map<?, ?> u = (Map<?, ?>) ele;
                            String userId = Converts.toStr(u.get("user_id"));
                            if (StringUtils.isNotBlank(userId)) {
                                String email = Converts.toStr(u.get("email"));
                                if (StringUtils.isNotBlank(email)) {
                                    mapVo.put(email, userId);
                                }
                                String mobile = Converts.toStr(u.get("mobile"));
                                if (StringUtils.isNotBlank(mobile)) {
                                    mapVo.put(mobile, userId);
                                }
                            }
                        });
                        return mapVo;
                    }
                }
            }
        }
        return Collections.emptyMap();
    }

    /**
     * 发送模板信息
     *
     * @param fsUserId
     * @param variable
     * @param uuid
     * @return
     */
    @Override
    public CompletableFuture<String> sendTemplateMsg(String fsUserId, String templateId,
                                                     Map<String, Object> variable, String uuid) {
        String content = JsonUtils.toJson(Maps.of("type", "template", "data",
                Maps.of("template_id", templateId,
                        "template_variable", variable)));
        return this.sendMsg(fsUserId, "interactive", content, uuid, null)
                .thenApply(resp -> resp == null ? null : resp.getMessage_id());
    }

    public CompletableFuture<String> sendPriceApplyTemplateMsg(String fsUserId, String templateId,
                                                               Map<String, Object> variable, String uuid) {
        String content = JsonUtils.toJson(Maps.of("type", "template", "data",
                Maps.of("template_id", templateId,
                        "template_variable", variable)));
        return this.sendPriceApplyMsg(fsUserId, "interactive", content, uuid, null)
                .thenApply(resp -> resp == null ? null : resp.getMessage_id());

                                                               }

    /**
     * 发送文本消息
     *
     * @param fsUserId
     * @param content
     * @return
     */
    @Override
    public CompletableFuture<String> sendTextMsgAsync(String fsUserId, String content) {
        return this.sendMsg(fsUserId, "text", content, null, null)
                .thenApply(resp -> resp == null ? null : resp.getMessage_id());
    }

    /**
     * 发送消息（异步）
     *
     * @param fsUserId
     * @param msgType
     * @param content
     * @param uuid
     * @param resetNumber
     * @return
     */
    @Override
    public CompletableFuture<FsMessageResp> sendMsg(String fsUserId, String msgType, String content,
                                                    String uuid, Integer resetNumber) {
        if (StringUtils.isBlank(fsUserId)) {
            throw new ServerException("用户ID不能为空");
        }
        if (StringUtils.isBlank(content)) {
            throw new ServerException("消息内容不能为空");
        }
        String cutUUID = StringUtils.isNotBlank(uuid) ? uuid : UUID.randomUUID().toString();
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("receive_id", fsUserId);
        bodyMap.put("msg_type", msgType);
        bodyMap.put("content", content);
        bodyMap.put("uuid", cutUUID);
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + this.getTenantAccessToken());
        header.put("Content-Type", "application/json; charset=utf-8");
        return HttpUtils.execute("POST",
                fsDoMain + "/open-apis/im/v1/messages?receive_id_type=user_id",
                header, JsonUtils.toJson(bodyMap),
                ContentType.APPLICATION_JSON, null).thenApplyAsync(body -> {
            if (body.getCode() == 200) {
                FsResult<FsMessageResp> result = JsonUtils.fromJson(body.getBody(),
                        FsResult.class, FsMessageResp.class);
                if (result.getCode() == 0) {
                    return result.getData();
                }
            }
            if (body.getHeaders() != null) {
                Long ratelimit = Converts.toLong(body.getHeaders().get("x-ogw-ratelimit-reset"));
                if (ratelimit != null && ratelimit > 0) {
                    if (resetNumber == null || (resetNumber >= 0 && resetNumber < 3)) {
                        int i = (resetNumber == null ? 0 : resetNumber) + 1;
                        scheduled.schedule(() -> {
                            FsApiService bean = SpringBeanUtils.getBean(FsApiService.class);
                            bean.sendMsg(fsUserId, msgType, content, cutUUID, i);
                        }, ratelimit, TimeUnit.SECONDS);
                    }
                }
            }
            log.error("飞书发送消息失败: {}", StrUtil.sub(body.getBody(), 0, 100));
            return null;
        }, executor);
    }

    /**
     * 发送消息（异步）
     *
     * @param fsUserId
     * @param msgType
     * @param content
     * @param uuid
     * @param resetNumber
     * @return
     */
    @Override
    public CompletableFuture<FsMessageResp> sendPriceApplyMsg(String fsUserId, String msgType, String content,
                                                    String uuid, Integer resetNumber) {
        if (StringUtils.isBlank(fsUserId)) {
            throw new ServerException("用户ID不能为空");
        }
        if (StringUtils.isBlank(content)) {
            throw new ServerException("消息内容不能为空");
        }
        String cutUUID = StringUtils.isNotBlank(uuid) ? uuid : UUID.randomUUID().toString();
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("receive_id", fsUserId);
        bodyMap.put("msg_type", msgType);
        bodyMap.put("content", content);
        bodyMap.put("uuid", cutUUID);
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + this.getTenantAccessToken());
        header.put("Content-Type", "application/json; charset=utf-8");
        return HttpUtils.execute("POST",
                fsDoMain + "/open-apis/im/v1/messages?receive_id_type=open_id",
                header, JsonUtils.toJson(bodyMap),
                ContentType.APPLICATION_JSON, null).thenApplyAsync(body -> {
            if (body.getCode() == 200) {
                FsResult<FsMessageResp> result = JsonUtils.fromJson(body.getBody(),
                        FsResult.class, FsMessageResp.class);
                if (result.getCode() == 0) {
                    return result.getData();
                }
            }
            if (body.getHeaders() != null) {
                Long ratelimit = Converts.toLong(body.getHeaders().get("x-ogw-ratelimit-reset"));
                if (ratelimit != null && ratelimit > 0) {
                    if (resetNumber == null || (resetNumber >= 0 && resetNumber < 3)) {
                        int i = (resetNumber == null ? 0 : resetNumber) + 1;
                        scheduled.schedule(() -> {
                            FsApiService bean = SpringBeanUtils.getBean(FsApiService.class);
                            bean.sendMsg(fsUserId, msgType, content, cutUUID, i);
                        }, ratelimit, TimeUnit.SECONDS);
                    }
                }
            }
            log.error("飞书发送消息失败: {}", StrUtil.sub(body.getBody(), 0, 100));
            return null;
        }, executor);
    }

}
