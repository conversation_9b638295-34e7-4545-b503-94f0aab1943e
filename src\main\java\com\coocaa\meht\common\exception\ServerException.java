package com.coocaa.meht.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自定义异常
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServerException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private int code;
    private String msg;

    public ServerException() {
        super();
        this.code = ErrorCode.ERROR.getCode();
        this.msg = ErrorCode.ERROR.getMsg();
    }

    public ServerException(ErrorCode errorCode) {
        super(errorCode.getMsg());
        this.code = errorCode.getCode();
        this.msg = errorCode.getMsg();
    }

    public ServerException(String msg) {
        super(msg);
        this.code = ErrorCode.ERROR.getCode();
        this.msg = msg;
    }

    public ServerException(int code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public ServerException(String msg, Throwable e) {
        super(msg, e);
        this.code = 999;
        this.msg = msg;
    }

    public ServerException(Throwable e) {
        super(e);
        this.code = ErrorCode.ERROR.getCode();
        this.msg = ErrorCode.ERROR.getMsg();
    }

}