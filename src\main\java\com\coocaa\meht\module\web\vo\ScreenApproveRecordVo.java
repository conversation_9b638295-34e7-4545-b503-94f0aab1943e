package com.coocaa.meht.module.web.vo;

import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Generated;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-04-16
 */
@Data
public class ScreenApproveRecordVo extends BaseEntity {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 楼宇单号
     */
    private String buildingRatingNo;

    /**
     * 审批人
     */
    private String approveUser;
    /**
     * 审批人名称
     */
    private String approveUserName;

    /**
     * 审批意见
     */
    private String remark;

    /**
     * 审批状态
     */
    private Integer status;

    /**
     * 是否删除标记：0-未删除，1-已删除
     */
    private Boolean deleteFlag;

    /**
     * 审批时间
     */
    private LocalDateTime approveTime;

    /**
     * 审批级别
     */
    private Integer approveLevel;

    /**
     * 审批场景类型 BUILDING(1, "楼宇"),
     * PRICE_APPLY(2, "价格申请");
     */
    private Integer sceneType;

    /**
     * 复核系数
     */
    private BigDecimal finalCoefficient;
    /**
     * 审批状态-名称
     */
    private String statusName;

    /**
     * 操作类型：1-新建数据审核，2-完善评级审核
     */
    private Integer operateType;

    @Getter
    @AllArgsConstructor
    public enum BuildingStatusResultEnum {
        WAIT_AUDIT(0, "待审核"),
        AUDITED(1, "审核通过"),
        REJECTED(2, "审核驳回"),
        FAILED_AUDIT(3, "审核不通过"),
        ABANDONED(4, "已放弃");

        public final int value;
        private final String name;

        public static String getNameByValue(int value) {
            for (ScreenApproveRecordVo.BuildingStatusResultEnum type : BuildingStatusResultEnum.values()) {
                if (type.value == value) {
                    return type.name;
                }
            }
            throw new IllegalArgumentException("No enum constant with value " + value);
        }
    }

}
