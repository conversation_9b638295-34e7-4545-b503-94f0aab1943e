package com.coocaa.meht.module.web.dto.property;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
public class PersonParam {

    @NotNull(message = "物业id不能为空")
    @Schema(description = "物业id", type = "Int", example = "1")
    Integer propertyId;

    @Valid
    @Schema(description = "物业联系人")
    List<PropertyCompanyPersonParam>  personList;
}
