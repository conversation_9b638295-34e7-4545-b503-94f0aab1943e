package com.coocaa.meht.module.web.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class PriceApplyListDto {

    /**
     * 申请id(主键)
     */
    private Long applyId;

    /**
     * 价格申请编码： JGSQ+年月日+流水号（4位，从0001开始）
     */
    @Schema(description = "价格申请编码")
    private String applyCode;

    /**
     * 商机编码
     */
    @Schema(description = "商机编码")
    private String businessCode;

    /**
     * 商机名称
     */
    @Schema(description = "商机名称")
    private String businessName;


    /**
     * 楼宇编码
     */
    @Schema(description = "楼宇编码")
    private String buildingNo;

    /**
     * 申请名称(楼盘名称)
     */
    @Schema(description = "申请名称(楼盘名称)")
    private String projectName;

    /**
     * 楼盘类型
     */
    @Schema(description = "楼盘类型")
    private String projectType;


    /**
     * 附件ID列表
     */
    @Schema(description = "附件ID列表")
    private String fileIds;

    /**
     * 状态 [0:草稿 1:待审核 2:已审核 3:审核不通过]
     */
    @Schema(description = "状态 [0:草稿 1:待审核 2:已审核 3:审核不通过]")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusName;


    /**
     * 审批人
     */
    @Schema(description = "审批人")
    private String approveBy;

    /**
     * 申请人code
     */
    @Schema(description = "申请人code")
    private String  createBy;

    /**
     * 申请人名
     */
    @Schema(description = "申请人名")
    private String  createName;

    /**
     * 审批时间
     */
    @Schema(description = "审批时间")
    private LocalDateTime approveTime;

    /**
     * 审批备注
     */
    @Schema(description = "审批备注")
    private String approveRemark;

    /**
     * true 属于自己的申请
     */
    @Schema(description = "true 属于自己的申请")
    private Boolean isSelfApply;

    /**
     * 评级等级 有复核等级取复合等级，无复审等级取评级等级
     */
    @Schema(description = "评级等级")
    private String projectLevel;

    /**
     * 楼宇类型
     */
    @Schema(description = "楼宇类型")
    private Integer buildingType;

    /**
     * 省
     */
    @Schema(description = "省")
    private String mapProvince;

    /**
     * 市
     */
    @Schema(description = "市")
    private String mapCity;

    /**
     * 区
     */
    @Schema(description = "区")
    private String mapRegion;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址")
    private String mapAddress;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private String version;



    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
