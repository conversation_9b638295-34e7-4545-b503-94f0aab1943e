package com.coocaa.meht.config;

import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.exception.ErrorCode;
import com.coocaa.meht.config.filter.RequestIdFilter;
import com.coocaa.meht.utils.DateUtils;
import com.coocaa.meht.utils.RsaExample;
import com.coocaa.meht.utils.ServletUtils;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import jakarta.annotation.Resource;
import jakarta.servlet.Filter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.hibernate.validator.HibernateValidator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.util.ContentCachingRequestWrapper;

import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;
import java.util.Set;

@Configuration
public class WebBeanConfig implements WebMvcConfigurer {
    public static final String TOKEN_NAME = "Token";

    @Value("${rsa.publicKey}")
    private String publicKey;

    @Value("${rsa.privateKey}")
    private String privateKey;

    @Lazy
    @Resource
    private UserCacheHelper userCacheHelper;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new TokenInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/server-api/**")
                .excludePathPatterns("/doc.html", "/api/doc.html")
                .excludePathPatterns("/swagger-ui/**", "/api/swagger-ui/**")
                .excludePathPatterns("/v3/api-docs/**", "/api/v3/api-docs/**")
                .excludePathPatterns("/webjars/**", "/api/webjars/**");


        // 白名单
        Set<String> whiteUris = Set.of(
                "/sys/user/login",
                "/sys/user/token/{refreshToken}",
                "/sys/captcha",
                "/sys/phone",
                "/sys/user/login/{mobile}"
        );

        registry.addInterceptor(new com.coocaa.ad.common.core.interceptor.TokenInterceptor(userCacheHelper, whiteUris, false))
                .addPathPatterns("/**")
                .excludePathPatterns("/server-api/**")
                .excludePathPatterns("/doc.html", "/api/doc.html")
                .excludePathPatterns("/swagger-ui/**", "/api/swagger-ui/**")
                .excludePathPatterns("/v3/api-docs/**", "/api/v3/api-docs/**")
                .excludePathPatterns("/webjars/**", "/api/webjars/**");
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedHeaders("*")
                .allowedOriginPatterns("*")
                .allowedMethods("*")
                .allowCredentials(true);
    }

    @Bean
    public Validator validator() {
        try (ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class)
                .configure()
                .failFast(true)
                .buildValidatorFactory()) {
            return validatorFactory.getValidator();
        }
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> {
            builder.serializerByType(Long.class, ToStringSerializer.instance);
            builder.serializerByType(LocalDateTime.class, new LocalDateTimeSerializer(DateUtils.DTF_DT));
            builder.serializerByType(LocalDate.class, new LocalDateSerializer(DateUtils.DTF_DATE));
            builder.serializerByType(LocalTime.class, new LocalTimeSerializer(DateUtils.DTF_TIME));
            builder.deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer(DateUtils.DTF_DT));
            builder.deserializerByType(LocalDate.class, new LocalDateDeserializer(DateUtils.DTF_DATE));
            builder.deserializerByType(LocalTime.class, new LocalTimeDeserializer(DateUtils.DTF_TIME));
        };
    }

    @Bean
    public FilterRegistrationBean<AuthenticationFilter> authenticationFilter() {
        FilterRegistrationBean<AuthenticationFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.addUrlPatterns("/*");
        registrationBean.setFilter(new AuthenticationFilter());
        registrationBean.setName("authenticationFilter");
        registrationBean.setOrder(1);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<Filter> wrapperFilter() {
        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter((request, response, chain) -> {
            if (request instanceof HttpServletRequest) {
                String contentType = request.getContentType();
                if (contentType != null && contentType.contains("multipart/form-data")) {
                    chain.doFilter(request, response);
                } else {
                    HttpServletRequest requestWrapper = new ContentCachingRequestWrapper((HttpServletRequest) request);
                    chain.doFilter(requestWrapper, response);
                }
            } else {
                chain.doFilter(request, response);
            }
        });
        registrationBean.addUrlPatterns("/*");
        registrationBean.setName("contentCachingFilter");
        registrationBean.setOrder(0);
        return registrationBean;
    }

    private HandlerInterceptor getLoginInterceptor() {
        return new HandlerInterceptor() {
            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
                LoginUser user = SecurityUser.getUserAnonymity();
                if (Objects.nonNull(user)) {
                    return true;
                }
                if (handler instanceof HandlerMethod) {
                    HandlerMethod handlerMethod = (HandlerMethod) handler;
                    Method method = handlerMethod.getMethod();
                    Anonymous methodAnnotation = method.getAnnotation(Anonymous.class);
                    if (methodAnnotation != null) {
                        return true;
                    }
                }
                ServletUtils.respResult(response, Result.error(ErrorCode.UNAUTHORIZED));
                return false;
            }
        };
    }


    @Bean("rsaExample")
    public RsaExample getRsaExample() {
        return new RsaExample(this.publicKey, this.privateKey);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/api/doc.html", "/doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/api/webjars/**", "/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("/api/swagger-ui/**", "/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/swagger-ui/");
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
    }

    @Bean
    public FilterRegistrationBean<RequestIdFilter> requestIdFilterRegistration(RequestIdFilter requestIdFilter) {
        FilterRegistrationBean<RequestIdFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(requestIdFilter);
        registration.setOrder(1);  // 确保是最先执行的过滤器之一
        registration.addUrlPatterns("/*");
        return registration;
    }

}
