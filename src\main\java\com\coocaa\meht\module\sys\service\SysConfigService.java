package com.coocaa.meht.module.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.sys.entity.SysConfigEntity;

import jakarta.annotation.PostConstruct;
import org.apache.kafka.common.protocol.types.Field;

/**
 * 参数管理
 */
public interface SysConfigService extends IService<SysConfigEntity> {

    @PostConstruct
    void init();

    String getVal(String key);

    String getVal(String key, String def);

    String getValDb(String key);

    void setValue(String key, String value);

    String getFsAppId();
}