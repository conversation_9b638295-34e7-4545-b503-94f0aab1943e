<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.web.dao.CustomerFollowRecordMapper">

    <select id="getCustomerFollowRecord" resultType="com.coocaa.meht.module.crm.vo.FollowUpVO">
        SELECT
        cfr.id id,
        cfr.visit_time followUpTime,
        cfr.visit_type category,
        cfr.visit_objects followTarget,
        cfr.business_code businessCode,
        cfr.visit_purpose followUpGoal,
        cfr.visit_result followUpResult,
        bo.name businessName,
        bo.building_no buildingNo,
        cfr.batch_id batchId,
        cfr.create_by createBy
        FROM
        business_opportunity bo
        INNER JOIN customer_follow_record cfr ON bo.`code` = cfr.business_code
        <where>
            <if test="buildingNo != null">
                and bo.building_no = #{buildingNo}
            </if>
            <if test="businessCode != null and businessCode != '' ">
                and cfr.business_code = #{businessCode}
            </if>
            <if test="id != null">
                and cfr.id = #{id}
            </if>

            and cfr.deleted = false
        </where>

        order by cfr.visit_time desc
    </select>
</mapper>
