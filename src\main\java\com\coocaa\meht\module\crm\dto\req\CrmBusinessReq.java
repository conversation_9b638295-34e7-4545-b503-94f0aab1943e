package com.coocaa.meht.module.crm.dto.req;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 商机列表查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmBusinessReq extends CrmPageReq {
    /**
     * 查询关键字
     */
    private String search;

    /**
     * 查询场景ID
     */
    private String sceneId;

    /**
     * 查询类型
     */
    private Integer type;

    /**
     * 查询场景类型
     */
    @NotBlank(message = "场景类型不能为空")
    private String sceneType;

    /**
     * 商机状态(字典key 0043)，多选
     */
    private List<String> status;
}
