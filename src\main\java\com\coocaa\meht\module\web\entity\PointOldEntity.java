package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @program: cheese-meht
 * @ClassName PointOldEntity
 * @description:
 * @author: zhangbinxian
 * @create: 2025-01-17 10:00
 * @Version 1.0
 * todo 后期删除
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("point_old")
public class PointOldEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer waitingHallId;
    private String code;
    private String pointStatus;
    private String remark;
    private String deviceSize;
    private String createBy;
    private LocalDateTime createTime;
    private String updateBy;
    private LocalDateTime updateTime;
    private LocalDateTime expireTime;
    private String buildingRatingNo;
    private Integer city;
    private Integer number;
}
