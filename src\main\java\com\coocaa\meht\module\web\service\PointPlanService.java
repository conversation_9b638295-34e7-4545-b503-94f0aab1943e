package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.dataimport.pojo.PointPlanUpdateStatusBO;
import com.coocaa.meht.module.web.entity.PointPlanEntity;

import java.util.List;

public interface PointPlanService extends IService<PointPlanEntity> {
    List<PointPlanEntity> listByBusinessCode(List<String> businessCodeList);

    PointPlanEntity getByBusinessCode(String businessCode);

    void updatePointPlanStatus( PointPlanUpdateStatusBO bo);
} 