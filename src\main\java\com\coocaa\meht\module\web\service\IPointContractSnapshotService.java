package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.point.PointDetailParam;
import com.coocaa.meht.module.web.dto.point.ProjectPointVO;
import com.coocaa.meht.module.web.entity.PointContractSnapshotEntity;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 点位合同快照表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface IPointContractSnapshotService extends IService<PointContractSnapshotEntity> {

    List<PointContractSnapshotEntity> listByBuildingNos(Collection<String> buildingNos);

    ProjectPointVO listBuildingPoint(PointDetailParam param);
}