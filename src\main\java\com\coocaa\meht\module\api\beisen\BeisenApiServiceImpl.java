package com.coocaa.meht.module.api.beisen;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.coocaa.meht.utils.Converts;
import com.coocaa.meht.utils.HttpUtils;
import com.coocaa.meht.utils.JsonUtils;
import com.coocaa.meht.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class BeisenApiServiceImpl implements BeisenApiService {

    private static final String TOKEN_KEY = "meht:bs:access_token";

    @Value("${beisenAppKey}")
    private String appKey;
    @Value("${beisenAppSecret}")
    private String appSecret;
    @Value("${beisenDomain}")
    private String domain;
    @Value("${beisenGrantType}")
    private String grantType;

    @Resource
    private RedisUtils redisUtils;

    /**
     * 获取Token
     *
     * @return
     */
    @Override
    public String getAccessToken() {
        String token = redisUtils.get(TOKEN_KEY);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        Map<String, String> map = new HashMap<>();
        map.put("grant_type", grantType);
        map.put("app_key", appKey);
        map.put("app_secret", appSecret);
        String response = HttpUtils.post(domain + "/token", header, JsonUtils.toJson(map));
        Map<String, Object> json = JsonUtils.fromMap(response);
        String accessToken = Converts.toStr(json.get("access_token"));
        if (StringUtils.isNotBlank(accessToken)) {
            Long expire = Converts.toLong(json.get("expires_in"));
            redisUtils.set(TOKEN_KEY, accessToken, expire - 5);
            return accessToken;
        }
        return null;
    }

    /**
     * 员工信息
     *
     * @param scrollId
     * @return
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<Map<String, Object>> getEmployList(String scrollId) {
        LocalDate date = LocalDate.now();
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        header.put("Authorization", this.getAccessToken());
        Map<String, Object> map = new HashMap<>();
        map.put("empStatus", new Integer[]{2, 3, 4, 5, 12, 8});
        map.put("employType", new Integer[]{0, 2});
        map.put("startTime", date.plusDays(-30) + "T00:00:00");
        map.put("stopTime", date + "T23:59:59");
        map.put("enableTranslate", true);
        if (StringUtils.isNotEmpty(scrollId)) {
            map.put("scrollId", scrollId);
        }
        String response = HttpUtils.post(domain + "/TenantBaseExternal/api/v5/Employee/GetByTimeWindow",
                header, JsonUtils.toJson(map));
        //Map<String, Object> json = JsonUtils.fromMap(response);
        Map<String, Object> json = JSON.parseObject(response, Map.class);
        if (isSuccess(json)) {
            scrollId = Converts.toStr(json.get("scrollId"));
            List<?> data = (List<?>) json.get("data");
            if (CollectionUtils.isNotEmpty(data)) {
                List<Map<String, Object>> result = data.stream().map(e -> (Map<String, Object>) e)
                        .collect(Collectors.toList());
                if (StringUtils.isNotBlank(scrollId)) {
                    result.addAll(this.getEmployList(scrollId));
                }
                return result;
            }
        }
        return Collections.emptyList();
    }

    private boolean isSuccess(Map<String, Object> request) {
        return request != null && "200".equals(Converts.toStr(request.get("code")));
    }

}
