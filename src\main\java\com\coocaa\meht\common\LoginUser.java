package com.coocaa.meht.common;

import com.coocaa.meht.module.sys.entity.SysUserEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Set;

@Data
@Accessors(chain = true)
public class LoginUser implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String userCode;
    private String userName;
    private String phone;
    private String email;
    private String fsUserId;
    private String avatar;
    private Set<String> roles;
    private Set<String> menuIds;
    private Set<String> authority;
    private String token;
    private Integer userType = 0;

    public static LoginUser build(SysUserEntity user) {
        LoginUser loginUser = new LoginUser();
        loginUser.setId(user.getId());
        loginUser.setUserCode(user.getEmpCode());
        loginUser.setUserName(user.getRealName());
        loginUser.setPhone(user.getMobile());
        loginUser.setEmail(user.getEmail());
        loginUser.setFsUserId(user.getFsUserId());
        loginUser.setAvatar(user.getAvatar());
        return loginUser;
    }

}
