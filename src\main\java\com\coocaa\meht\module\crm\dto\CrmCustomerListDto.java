package com.coocaa.meht.module.crm.dto;


import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class CrmCustomerListDto {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 楼宇名称
     */
    private String customerName;

    /**
     * 详细地址
     */
    private String fieldCakork;


    /**
     * 楼宇评级等级
     */
    private String level;

    /**
     * 楼宇编号
     */
    private String fieldWpgtbg;

    /**
     * 负责人
     */
    private String createUserName;

    /**
     * 楼宇类型名称
     */
    private String buildingTypeName;

    /**
     * 楼层数
     */
    private Long buildingNumber;

    /**
     * 月租金
     */
    private Long buildingPrice;

    private String buildingNo;

    private String ownerUserId;

    /**
     * AI评级
     */
    private String projectAiLevel;
    /**
     * AI得分
     */
    private BigDecimal buildingAiScore;
    /**
     * 竞媒点位数量
     */
    private BigDecimal competitorPointCount;
    /**
     * 目标点位数量
     */
    private BigDecimal targetPointCount;

    /**
     * 创建人
     */
    private String createBy;


}
