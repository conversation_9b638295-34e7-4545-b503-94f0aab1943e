package com.coocaa.meht.module.web.controller.proxy;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.proxy.CsBrandPointQueryParam;
import com.coocaa.meht.module.api.proxy.BuildingPointService;
import com.coocaa.meht.module.web.dto.req.BrandPointQueryReq;
import com.coocaa.meht.module.web.vo.proxy.CsBrandPointDataWrapperVO;
import com.coocaa.meht.rpc.FeignMehtWebRpc;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.Map;

/**
 * <AUTHOR>
 * @file ExternalProxyController
 * @date 2025/1/21 9:58
 * @description 外部接口代理控制器
 */
@Tag(name = "外部接口代理管理")
@RestController
@RequestMapping("/external/proxy")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ExternalProxyController {
    private final FeignMehtWebRpc feignMehtWebRpc;

    private final BuildingPointService buildingPointService;

    @Anonymous
    @Operation(summary = "通过城市或百度坐标及公里数获取楼宇信息")
    @PostMapping("/cs/data/point-analysis/brand-point")
    public ResultTemplate<CsBrandPointDataWrapperVO> getCsBrandPoint(@RequestBody CsBrandPointQueryParam param) {
        return ResultTemplate.success(feignMehtWebRpc.getCsBrandPoint(param).getData());
    }

    @Operation(summary = "通过城市或百度坐标和公里数获取楼宇信息（创视、竞品、未开发的余量数据）")
    @PostMapping("/cs/data/point-analysis/building-point")
    public Map<String, Object> getCsBuildingPoint(@RequestBody BrandPointQueryReq req) {
        return buildingPointService.getBuildingPoint(req, false);
    }

    @Anonymous
    @Operation(summary = "通过城市或百度坐标和公里数获取楼宇信息（创视、竞品、未开发的余量数据）- 匿名")
    @PostMapping("/cs/data/point-analysis/building-point-map")
    public Map<String, Object> getCsBuildingPointAnonymous(@RequestBody BrandPointQueryReq req) {
        return buildingPointService.getBuildingPoint(req, true);
    }
}
