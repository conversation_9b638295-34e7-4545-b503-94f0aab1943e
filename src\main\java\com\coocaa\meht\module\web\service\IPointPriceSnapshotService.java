package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.point.PointDetailParam;
import com.coocaa.meht.module.web.dto.point.ProjectPointVO;
import com.coocaa.meht.module.web.entity.PointPriceSnapshotEntity;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 点位价格快照表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
public interface IPointPriceSnapshotService extends IService<PointPriceSnapshotEntity> {

    List<PointPriceSnapshotEntity> listByBuildingNos(Collection<String> set);

    ProjectPointVO listBuildingPoint(PointDetailParam param);
}