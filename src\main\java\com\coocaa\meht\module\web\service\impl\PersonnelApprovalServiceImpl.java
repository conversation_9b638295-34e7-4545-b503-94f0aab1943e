package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.AgentPersonnelDao;
import com.coocaa.meht.module.web.dao.PersonnelApprovalDao;
import com.coocaa.meht.module.web.entity.AgentPersonnelEntity;
import com.coocaa.meht.module.web.entity.PersonnelApprovalEntity;
import com.coocaa.meht.module.web.service.PersonnelApprovalService;
import org.apache.groovy.util.Maps;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Created by fengke on 2024/11/21.
 */
@Service
public class PersonnelApprovalServiceImpl extends ServiceImpl<PersonnelApprovalDao, PersonnelApprovalEntity> implements PersonnelApprovalService {

    @Override
    public PersonnelApprovalEntity getApprovalByCode(String empCode) {
        List<PersonnelApprovalEntity> list = this.listByMap(Maps.of("emp_code", empCode));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}
