package com.coocaa.meht.config.aspect;

import java.lang.annotation.*;

/**
 * Redis分布式锁注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RedisLock {


    /**
     * 锁的key,对应对象中的某几个字段以,号分割
     */
    String key();
    
    /**
     * 等待获取锁的时间
     */
    long waitTime() default 3;
    
    /**
     * 锁的过期时间
     */
    long expireTime() default 30;
    

} 