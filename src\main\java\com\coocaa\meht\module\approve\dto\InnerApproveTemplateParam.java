package com.coocaa.meht.module.approve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @className: InnerApproveTemplateParam
 * @description: 站内审批公共类
 * @author: jx
 * @date: 2025/3/7 17:17
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "InnerApproveTemplateParam", description = "站内审批公共类")
public class InnerApproveTemplateParam {

    /**
     * key
     */
    private String key;

    /**
     * type 对应ApproveFieldTypeEnum
     */
    private String type;

    /**
     * value
     */
    private String value;
}
