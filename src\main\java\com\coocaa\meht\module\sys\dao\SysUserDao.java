package com.coocaa.meht.module.sys.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.module.sys.dto.SysUserDto;
import com.coocaa.meht.module.sys.entity.SysUserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 系统用户
 */
@Mapper
public interface SysUserDao extends BaseMapper<SysUserEntity> {

    default SysUserEntity getByCode(String empCode) {
        return this.selectOne(new QueryWrapper<SysUserEntity>().eq("emp_code", empCode));
    }

    default SysUserEntity getByEmail(String email) {
        return this.selectOne(new QueryWrapper<SysUserEntity>().eq("email", email).last("limit 1"));
    }

    default SysUserEntity getByFsUserId(String fsUserId) {
        return this.selectOne(new QueryWrapper<SysUserEntity>().eq("fs_user_id", fsUserId).last("limit 1"));
    }

    /**
     * 获取名称
     *
     * @param userCode
     * @return
     */
    @Select("select real_name from sys_user where emp_code = #{userCode};")
    String getName(String userCode);

    /**
     * 获取飞书userID
     *
     * @param empCode
     * @return
     */
    @Select("select fs_user_id from sys_user where emp_code = #{userCode} and deleted = 0;")
    String getFsUserId(String empCode);

    @Select("select fs_open_id from sys_user where emp_code = #{userCode} and deleted = 0;")
    String getFsOpenId(String empCode);

    /**
     * 获取飞书编号
     */
    @Select("select fs_user_id, emp_code from sys_user where deleted = 0;")
    List<Map<String, String>> getFsUserIds();


    List<SysUserDto> getNameList(@Param("userCodes") List<String> userCodes);
}