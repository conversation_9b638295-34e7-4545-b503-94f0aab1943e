package com.coocaa.meht.module.approve.event.listener;

import com.coocaa.meht.module.approve.enums.ApprovalResultEnum;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.module.approve.event.ApprovalEventListener;
import com.coocaa.meht.module.approve.event.ApprovalStatusChangeEvent;
import com.coocaa.meht.module.approve.strategy.impl.PriceApplyApprovalStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 价格审批事件监听器
 * 处理价格审批相关的事件
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PriceApprovalEventListener implements ApprovalEventListener {

    private final PriceApplyApprovalStrategy priceApplyApprovalStrategy;

    @Override
    public boolean support(String approvalType) {
        return Objects.equals(approvalType, ApprovalTypeEnum.PRICE_APPROVAL.getCode());
    }

    @Override
    public void onStatusChanged(ApprovalStatusChangeEvent event) {
        if (event == null) {
            log.warn("接收到空的价格审批状态变更事件，已忽略。");
            return;
        }
        log.info("价格审批状态变更: {}", event);

        try {
            String businessKey = event.getBusinessKey();

            if (ApprovalResultEnum.FINISHED.getCode().equals(event.getResult())) {
                log.info("处理价格审批通过逻辑, 业务标识: {}", businessKey);
            } else if (ApprovalResultEnum.REJECTED.getCode().equals(event.getResult())) {
                log.info("处理价格审批拒绝逻辑, 业务标识: {}", businessKey);
            } else {
                log.info("其他状态变更，无需处理: {}", event.getResult());
            }
        } catch (Exception e) {
            log.error("处理价格审批事件异常", e);
            // 可以考虑将异常记录到专门的异常处理表，便于后续人工干预或重试
        }
    }
} 