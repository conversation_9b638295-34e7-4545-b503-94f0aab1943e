package com.coocaa.meht.module.web.dto.point;


import com.coocaa.meht.converter.Convert;
import com.coocaa.meht.converter.ConvertType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Data
public class ProjectPointCountVO {
    private String buildingRatingNo;
    private String buildingName;
    private String unitName;
    @Convert(type = ConvertType.DICT)
    private String floor;
    private String floorName;
    private String waitingHallName;
    private String waitingHallTypeName;
    private Integer waitingHallId;
    private Integer number;
}
