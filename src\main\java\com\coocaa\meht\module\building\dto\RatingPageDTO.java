package com.coocaa.meht.module.building.dto;

import com.coocaa.meht.common.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RatingPageDTO extends PageReq {

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "申请状态")
    private List<Integer> statuses;

    @Schema(description = "申请人工号")
    private List<String> wnos;

    @Schema(description = "申请开始日期")
    private LocalDate start;

    @Schema(description = "申请结束日期")
    private LocalDate end;

}
