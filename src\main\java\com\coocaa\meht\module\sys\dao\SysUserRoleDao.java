package com.coocaa.meht.module.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.sys.entity.SysUserRoleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户角色关系
 */
@Mapper
public interface SysUserRoleDao extends BaseMapper<SysUserRoleEntity> {

    /**
     * 角色ID列表
     *
     * @return  返回角色ID列表
     */
    @Select("select distinct role_code from sys_user_role where user_code = #{userCode};")
    List<String> getRoleCodes(String userCode);
}