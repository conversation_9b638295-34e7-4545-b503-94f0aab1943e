package com.coocaa.meht.module.dataimport.pojo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-05-21
 */
@Data
public class UpdateRatingStatusDTO {

    /**
     * 评级编码
     */
    @NotEmpty(message = "评级编码不能为空")
    private List<String> buildingNoList;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 审核人
     */
    @NotBlank
    private String approveUser;

}
