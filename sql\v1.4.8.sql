-- ************ 媒资楼宇 ************

ALTER TABLE `building_rating`
    ADD COLUMN `top_level` VARCHAR(10) NOT NULL DEFAULT '' COMMENT 'top楼宇等级' AFTER `target_point_count`;

ALTER TABLE `price_apply`
    ADD COLUMN `top_level` VARCHAR(10) NOT NULL DEFAULT '' COMMENT 'top楼宇等级' AFTER `business_code`;


ALTER TABLE `customer_follow_record`
    ADD COLUMN `role` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '拜访角色' AFTER `deleted`,
    ADD COLUMN `business_type` TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '业务类型 [0:自营, 1:代理]' AFTER `role`,
    ADD COLUMN `phone`         VARCHAR(200) NOT NULL DEFAULT '' COMMENT '联系方式' AFTER `business_type`,
    ADD COLUMN `valid`         TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '是否有效跟进 [1有效  0无效]' AFTER `phone`;

CREATE TABLE `follow_record_pic`
(
    `id`          BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `follow_id`   BIGINT(20)          NOT NULL DEFAULT '0' COMMENT '跟进id',
    `pic_url`     VARCHAR(150) NOT NULL DEFAULT '' COMMENT '图片地址',
    `create_by`   VARCHAR(20)  NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   VARCHAR(20)  NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY           `idx_followId` (`follow_id`)
) ENGINE = InnoDB COMMENT ='客户跟进记录-图片关联表';

CREATE TABLE `building_top`
(
    `id`            BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `building_name` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '楼宇名称',
    `province`      VARCHAR(15) NOT NULL DEFAULT '' COMMENT '省份',
    `city`          VARCHAR(15) NOT NULL DEFAULT '' COMMENT '城市',
    `top_level`     VARCHAR(10) NOT NULL DEFAULT '' COMMENT 'TOP等级',
    `building_type` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '楼宇类型',
    `create_by`     VARCHAR(20) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`     VARCHAR(20) NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `deleted`       tinyint(4)  NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='top楼宇';

ALTER TABLE business_opportunity ADD INDEX idx_building_no (building_no);
ALTER TABLE building_rating ADD INDEX idx_building_no (building_no);