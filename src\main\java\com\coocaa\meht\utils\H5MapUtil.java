package com.coocaa.meht.utils;

import com.coocaa.meht.module.api.map.MapApiService;
import com.coocaa.meht.module.web.dto.CityAddressDto;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
@Component
public class H5MapUtil {

    @Autowired
    private MapApiService mapApiService;

    public String getH5MapUrl(String mapCity, String buildingName) {
        CityAddressDto cityAddressDto = mapApiService.getCityId("路", mapCity);
        String cityId = cityAddressDto.getCityid();
        String url = "https://map.baidu.com/mobile/webapp/place/list/qt=con&wd=" + buildingName + "&c=" + cityId + "&contp=1/vt=map";
        return url;
    }
}
