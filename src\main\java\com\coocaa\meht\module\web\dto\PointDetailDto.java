package com.coocaa.meht.module.web.dto;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/12/18
 * @description 点位详情
 */
@Data
public class PointDetailDto {
    private String buildingRatingNo;
    private String buildingName;
    private String unitName;

    private String floor;
    // private String floorName;
    private String waitingHallName;

    private String waitingHallType;
    // private String waitingHallTypeName;
    private Integer waitingHallId;
    private String pointCode;

    private String pointRemark;

    private String deviceSize;
    //private String deviceSizeName;
    private Integer pointId;
    //private List<String> pointPics;
    private LocalDateTime pointCreateTime;
}
