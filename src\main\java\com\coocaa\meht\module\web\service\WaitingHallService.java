package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.common.bean.ProjectAddParam;
import com.coocaa.meht.module.web.dto.point.*;
import com.coocaa.meht.module.web.entity.WaitingHallEntity;

import java.util.List;

public interface WaitingHallService extends IService<WaitingHallEntity> {
    AddWaitingHallVO add(WaitingHallDTO waitingHallDTO);

    void updateWaitingHall(WaitingHallDTO waitingRoom);

    void delete(DeleteParam param);

    void updateTree(UpdateTreeParam param);


    WaitingHallDetail getWaitingHallById(Integer id,String businessCode);

    ProjectAddParam getProjectAddParam(String buildingNo);
}