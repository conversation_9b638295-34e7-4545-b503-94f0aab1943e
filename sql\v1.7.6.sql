-- 评论表
CREATE TABLE `comment`
(
    `id`             int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '评论ID',
    `business_type`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '业务类型（1楼宇、2价格申请）',
    `business_id`    varchar(20)   NOT NULL DEFAULT '' COMMENT '业务ID',
    `content`        varchar(2000) NOT NULL DEFAULT '' COMMENT '评论内容',
    `notified_users` varchar(200)           DEFAULT NULL COMMENT '通知用户列表，JSON格式',
    `attachment_ids` varchar(200)           DEFAULT NULL COMMENT '附件列表',
    `create_by`      varchar(10)   NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`    datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`      varchar(10)   NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time`    datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `delete_flag`    tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
    PRIMARY KEY (`id`),
    KEY              `idx_business` (`business_type`, `business_id`)
) ENGINE=InnoDB COMMENT='评论表';


CREATE TABLE `building_snapshot`
(
    `id`                 INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `building_rating_no` VARCHAR(20)      NOT NULL DEFAULT '' COMMENT '楼宇编码',
    `meta_snapshot`      TEXT             NULL COMMENT 'meta快照json',
    `rating_snapshot`    TEXT             NULL COMMENT 'building快照json',
    `details_snapshot`   TEXT             NULL COMMENT 'details快照json',
    `gene_snapshot`      TEXT             NULL COMMENT 'gene快照json',
    `create_by`          VARCHAR(10)      NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`        DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`          VARCHAR(10)      NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time`        DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `delete_flag`        TINYINT(1)       NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='楼宇快照';

ALTER TABLE `building_rating`
    ADD COLUMN `small_screen_rating_flag` TINYINT NOT NULL DEFAULT 0 COMMENT '小屏评级完成标识：0-否，1-是' AFTER `status`,
    ADD COLUMN `large_screen_rating_flag` TINYINT NOT NULL DEFAULT 0 COMMENT '大屏评级完成标识：0-否，1-是' AFTER `small_screen_rating_flag`;

ALTER TABLE `screen_approve_record`
    ADD COLUMN `operate_type` TINYINT NOT NULL DEFAULT 1 COMMENT '操作类型：1-新建数据审核，2-完善评级审核' AFTER `final_coefficient`;
