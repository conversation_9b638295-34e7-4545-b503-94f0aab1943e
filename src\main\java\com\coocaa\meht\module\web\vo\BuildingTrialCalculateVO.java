package com.coocaa.meht.module.web.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;

@Data
public class BuildingTrialCalculateVO {
    
    @ArraySchema(
        arraySchema = @Schema(name = "basicInfos", description = "基本信息板块，通过label和value动态渲染"),
        schema = @Schema(implementation = InfoItem.class)
    )
    private List<InfoItem> basicInfos;
    
    @ArraySchema(
        arraySchema = @Schema(name = "qualityInfos", description = "楼盘品质板块，通过label和value动态渲染"),
        schema = @Schema(implementation = InfoItem.class)
    )
    private List<InfoItem> qualityInfos;
    
    @ArraySchema(
        arraySchema = @Schema(name = "floorScores", description = "楼层得分板块，通过label和value动态渲染"),
        schema = @Schema(implementation = InfoItem.class)
    )
    private List<InfoItem> floorScores;

    @Schema(description = "楼宇AI等级")
    private String projectAiLevel;

    @Schema(description = "楼宇AI评分")
    private String buildingAiScore;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class InfoItem {

        private String key;
        /**
         * 信息项名称
         */
        @Schema(description = "信息项名称")
        private String label;
        
        /**
         * 信息项值
         */
        @Schema(description = "信息项值")
        private String value;
        
        /**
         * 额外信息（可选，如：是否合格等）
         */
        @Schema(description = "额外信息（可选，如：是否合格等）")
        private String extra;
    }

}
