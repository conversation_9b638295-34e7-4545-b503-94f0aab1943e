package com.coocaa.meht.module.web.dto.point;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

@Data
@Schema(description = "等候厅信息")
public class WaitingHallDTO {
    @Schema(description = "等候厅id新增的时候不用传,编辑的时候传")
    private Integer waitingHallId;

    @Schema(description = "点位方案id,不传代表第一次新增")
    private Integer pointPlanId;

    @Schema(description = "楼栋编码")
    @NotBlank(message = "楼栋编码不能为空")
    private String buildingRatingNo;

    @Schema(description = "商机编码")
    @NotBlank(message = "商机编码不能为空")
    private String businessCode;

    /**
     * 楼栋名称
     */
    @Schema(description = "楼栋名称", example = "1号楼")
    @NotBlank(message = "楼栋名称不能为空")
    @Size(max = 10, message = "楼栋名称长度不能超过10个字符")
    private String buildingName;
    
    /**
     * 单元名称
     */
    @Schema(description = "单元名称", example = "1单元")
    @NotBlank(message = "单元名称不能为空")
    @Size(max = 10, message = "单元名称长度不能超过10个字符")
    private String unitName;
    
    /**
     * 楼层
     */
    @Schema(description = "楼层字典0004", example = "1层")
    @NotBlank(message = "楼层不能为空")
    private String floor;
    
    /**
     * 等候厅
     */
    @Schema(description = "等候厅", example = "1号等候厅")
    @NotBlank(message = "等候厅不能为空")
    @Size(max = 15, message = "等候厅长度不能超过15个字符")
    private String waitingHall;
    
    /**
     * 等候厅类型
     */
    @Schema(description = "等候厅类型，字典0003", example = "普通等候厅")
    @NotBlank(message = "等候厅类型不能为空")
    private String waitingHallType;

    @Schema(description = "楼层描述1.1版本没有")
    private String floorDesc;
} 