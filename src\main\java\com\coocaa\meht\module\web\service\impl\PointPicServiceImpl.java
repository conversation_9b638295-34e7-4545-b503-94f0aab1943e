package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.PointPicMapper;
import com.coocaa.meht.module.web.entity.PointPicEntity;
import com.coocaa.meht.module.web.service.PointPicService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.List;

@Service
public class PointPicServiceImpl extends ServiceImpl<PointPicMapper, PointPicEntity> implements PointPicService {


    @Override
    public void removeByPointId(Integer pointId) {
        QueryWrapper<PointPicEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("point_id", pointId);
        remove(queryWrapper);
    }

    @Override
    public void removeByPointIds(List<Integer> pointIds) {
        if (CollectionUtils.isEmpty(pointIds)) {
            return;
        }
        QueryWrapper<PointPicEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("point_id", pointIds);
        remove(queryWrapper);
    }

    @Override
    public List<PointPicEntity> listByPointIds(List<Integer> pointIds) {
        if (!CollectionUtils.isEmpty(pointIds)) {
            return lambdaQuery().in(PointPicEntity::getPointId, pointIds).list();
        }
        return new ArrayList<>();
    }


}