package com.coocaa.meht.module.web.vo.kanban;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @file StatisticsDeviceDataVO
 * @date 2025/1/14 14:40
 * @description 设备统计数据
 */

@Data
@Accessors(chain = true)
public class StatisticsDeviceDataVO {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "下单数（全部时展示）", type = "number", example = "0")
    private Integer orderCount;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "生产交付数（全部时展示）", type = "number", example = "0")
    private Integer productionDeliveryCount;

    @Schema(description = "仓储物流数", type = "number", example = "0")
    private Integer warehouseLogisticsCount;

    @Schema(description = "派工数", type = "number", example = "0")
    private Integer workOrderDispatchCount;

    @Schema(description = "踏勘数", type = "number", example = "0")
    private Integer siteSurveyCount;

    @Schema(description = "挂板数", type = "number", example = "0")
    private Integer mountingPlateCount;

    @Schema(description = "安装点亮数", type = "number", example = "0")
    private Integer installationAndLightingCount;
}
