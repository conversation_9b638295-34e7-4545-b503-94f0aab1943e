package com.coocaa.meht.module.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-16
 */
@Data
public class BigScreenCalculateDTO {

    @Schema(description = "百度地图楼宇编码")
    @NotBlank(message = "百度地图楼宇编码不能为空")
    private String mapNo;

    @NotNull(message = "挑高不能为空")
    @Schema(description = "挑高")
    private BigDecimal buildingCeilingHeight;

    @NotNull(message = "间距不能为空")
    @Schema(description = "间距")
    private BigDecimal buildingSpacing;

    @NotNull(message = "楼龄不能为空")
    @Schema(description = "楼龄")
    private String buildingAgeInput;

    @NotNull(message = "楼层不能为空")
    @Schema(description = "楼层")
    private String buildingNumberInput;

    @NotNull(message = "地理等级不能为空")
    @Schema(description = "地理等级")
    private Long buildingLocation;

    @Schema(description = "地理等级文本")
    private String buildingLocationText;

    @NotBlank(message = "安装规格不能为空")
    @Schema(description = "规格", example = "[\"0013-3\",\"0013-4\"]")
    private String spec;

    @NotNull(message = "楼栋数量不能为空")
    @Schema(description = "楼栋数量")
    private Integer totalUnitCount;

    @NotNull(message = "电梯数量不能为空")
    @Schema(description = "电梯数量")
    private Integer elevatorCount;

    @NotNull(message = "入驻企业数不能为空")
    @Schema(description = "入驻企业数")
    private Integer companyCount;

    @NotBlank(message = "特殊说明不能为空")
    @Schema(description = "特殊说明")
    private String specialDesc;

    @Schema(description = "大屏点位系数", hidden = true)
    private String coefficient;

}
