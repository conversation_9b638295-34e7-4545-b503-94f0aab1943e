package com.coocaa.meht.module.web.vo.kanban;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


@Data
public class DataAccessVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", type = "Integer", example = "1")
    private Integer userId;

    /**
     * 用户名称
     */
    @Schema(description = "用户ID", type = "String", example = "王强")
    private String userName;

    /**
     * 工号
     */
    @Schema(description = "用户ID", type = "String", example = "CC2401")
    private String wno;

    /**
     * 权限类型
     */
    @Schema(description = "用户ID", type = "String", example = "看全部")
    private String accessType;



    @Schema(description = "城市集合")
    private List<DataAccessCityParam> cityList;


}


