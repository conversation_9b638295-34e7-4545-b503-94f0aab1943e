package com.coocaa.meht.common.bean;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.util.List;

/**
 * @program: cheese-ssp
 * @ClassName ProjectAddParam
 * @description:
 * @author: zhangbinxian
 * @create: 2025-01-08 15:12
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProjectAddParam {

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目编码
     */
    private String code;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 区县ID
     */
    private Integer districtId;

    /**
     * 物业类型
     */
    private String propertyType;

    /**
     * 开楼人员
     */
    private String businessOwner;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 楼宇编码
     */
    private String buildingRatingNo;

    /**
     * 禁忌行业
     */
    private List<String> forbiddenIndustry;

    /**
     * 楼宇编码BC打头
     */
    private String buildingMetaNo;
}
