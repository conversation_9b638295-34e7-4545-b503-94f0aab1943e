package com.coocaa.meht.module.sys.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: cheese-meht
 * @ClassName SysBuildingRatingDto
 * @description:
 * @create: 2024-12-24 12:01
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SysBuildingRatingDto {

    /**
     * 楼宇编码集合
     * 英文逗号分割
     */
    private String buildingNos;
    /**
     * 楼宇认证状态：0未认证，1认证中 2冻结中 3已认证
     */
    private Integer buildingStatus;
    /**
     * 楼宇状态：0待审核，1已审核 2已驳回 3审核不通过 4 已放弃
     */
    private Integer status;

    private String code;

}
