package com.coocaa.meht.module.api.map;

import com.coocaa.meht.module.web.dto.CityAddressDto;
import com.coocaa.meht.module.web.dto.MapAddressDto;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * Created by fengke on 2024/11/7.
 */
public interface MapApiService {

    Map<String, Object> getJsonMap(String query,String region,String city_limit,String output,String tag) throws UnsupportedEncodingException;

    Map<String, Object> getAccurate(String latitude,String longitude,String extensions_poi,String poi_types);


    MapAddressDto getConvertAddress(String latitude, String longitude);

    CityAddressDto getCityId(String query,String region);



}
