package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("price_apply_device_point")
@NoArgsConstructor
@AllArgsConstructor
public class PriceApplyDevicePointEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 价格申请ID
     */
    private Integer applyId;

    /**
     * 价格申请设备id
     */
    private Integer priceApplyDeviceId;

    /**
     * 点位名称
     */
    private String pointName;

    /**
     * 点位编码
     */
    private String pointCode;

}
