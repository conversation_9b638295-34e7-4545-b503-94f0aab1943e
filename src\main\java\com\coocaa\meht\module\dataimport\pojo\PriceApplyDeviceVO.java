package com.coocaa.meht.module.dataimport.pojo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 价格申请终端信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10
 */
@Data
public class PriceApplyDeviceVO {
    /**
     * 设备尺寸
     */
    private String size;

    /**
     * 设备数量
     */
    private Integer quantity;

    /**
     * 签约单价(元/台/月)
     */
    private BigDecimal signPrice;

    /**
     * 点位信息
     */
    private List<PriceApplyDevicePointVO> points;
}
