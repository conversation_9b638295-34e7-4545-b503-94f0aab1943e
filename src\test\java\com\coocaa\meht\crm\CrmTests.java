package com.coocaa.meht.crm;

import cn.hutool.core.util.IdUtil;
import com.coocaa.meht.common.*;
import com.coocaa.meht.module.crm.dto.CrmCustomerListDto;
import com.coocaa.meht.module.crm.dto.CrmFollowUpDto;
import com.coocaa.meht.module.crm.dto.CrmUserDto;
import com.coocaa.meht.module.crm.dto.req.CustomerListReq;
import com.coocaa.meht.module.crm.dto.req.CrmFollowUpAddReq;
import com.coocaa.meht.module.crm.dto.req.CrmFollowUpListReq;
import com.coocaa.meht.module.crm.service.CrmCustomerService;
import com.coocaa.meht.module.crm.service.CrmUserService;
import com.coocaa.meht.module.crm.vo.CustomerListVO;
import com.coocaa.meht.module.crm.vo.FollowUpVO;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.DataHandlerService;
import com.coocaa.meht.utils.JsonUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class CrmTests {

    @Autowired
    private DataHandlerService crmDataHandlerService;
    @Autowired
    private BuildingRatingService buildingRatingService;
    @Autowired
    private BuildingDetailsService buildingDetailsService;
    @Autowired
    private CrmCustomerService crmCustomerService;
    @Autowired
    private CrmUserService crmUserService;


    @Test
    public void test() {
        BuildingRatingEntity ratingEntity = buildingRatingService.getById(121);

        LoginUser loginUser = new LoginUser();
        loginUser.setPhone("13558727528");
        loginUser.setUserName("任林飞");
        SecurityUser.login(loginUser);

        crmDataHandlerService.handlerBuildingRating(ratingEntity,loginUser);
    }

    @Test
    public void syncCustomer() {
        crmDataHandlerService.syncCrmCustomerId();
    }

    @Test
    public void synFix() {
        crmDataHandlerService.fixOwnerUserId();
    }

    @Test
    public void testCronBuildingPushCrm() {
        buildingRatingService.cronBuildingPushCrm(null);
    }

    @Test
    public void testCrmCustomerService() {
        LoginUser loginUser = new LoginUser();
        loginUser.setPhone("18302895410");
        SecurityUser.login(loginUser);
        CustomerListReq req = new CustomerListReq();
        req.setPage(1);
        //req.setSearch("滨海");
        req.setLimit(10L);
        req.setSceneId("1861619475549495298");
        PageResult<CustomerListVO> pageResult = crmCustomerService.listCrmCustomer(req);
        System.out.println(JsonUtils.toJson(pageResult));
    }

    @Test
    public void testCrmFollowUpService() {
        LoginUser loginUser = new LoginUser();
        //loginUser.setCrmToken("10325fc94099447699e0ec229c9dd5d7");
        loginUser.setPhone("13558727528");
        SecurityUser.login(loginUser);
        CrmFollowUpListReq req = new CrmFollowUpListReq();
        req.setActivityTypeId("1861649740074295296");
        req.setLimit(2L);
        PageResult<FollowUpVO> crmFollowUpDtoPageResult = crmCustomerService.listCrmFollowup(req);
        System.out.println(JsonUtils.toJson(crmFollowUpDtoPageResult));
    }

    @Test
    public void testCrmFollowUpAdd() {
        LoginUser loginUser = new LoginUser();
        loginUser.setPhone("13558727528");
        SecurityUser.login(loginUser);

        ArrayList<KeyValue<String,String>> paramList = new ArrayList<>();
        paramList.add(new KeyValue("沟通时间", "2024-11-07 00:00:00"));
        paramList.add(new KeyValue("拜访对象","拜访对象老板1"));
        paramList.add(new KeyValue("沟通结果","无沟通结果1"));


        CrmFollowUpAddReq crmFollowUpAddReq = new CrmFollowUpAddReq().setCategory("面访").setContent("签合同1").setBatchId(IdUtil.simpleUUID())
                .setActivityTypeId("1861584504407433216").setTitleName("成都环球中心").setParamList(paramList);
                //.setId("1861034301303775232").setBatchId("7f4c696cf5254b979c02be2f73e5ada4");
        System.out.println(JsonUtils.toJson(crmFollowUpAddReq));

        crmCustomerService.addOrUpdateCrmFollowUp(crmFollowUpAddReq);
    }

    @Test
    public void testCrmFollowUpDelete() {
        LoginUser loginUser = new LoginUser();
        loginUser.setPhone("13558727528");
        SecurityUser.login(loginUser);

        crmCustomerService.deleteCrmFollowUp(1);
    }

    @Test
    public void testListQueryScenes() {
        LoginUser loginUser = new LoginUser();
        loginUser.setPhone("13558727528");
        SecurityUser.login(loginUser);
        Result<List<KeyValue<String, String>>> listResult = crmCustomerService.listQueryScenes();
        System.out.println(JsonUtils.toJson(listResult));
    }

    @Test
    public void testListChildUserId() {
        LoginUser loginUser = new LoginUser();
        loginUser.setPhone("18302895410");
        SecurityUser.login(loginUser);
        CrmUserDto crmUserInfo = crmUserService.getCrmUserInfo();
        List<String> strings = crmUserService.listChildUserId(crmUserInfo.getUserId());
        System.out.println(strings);
    }

}
