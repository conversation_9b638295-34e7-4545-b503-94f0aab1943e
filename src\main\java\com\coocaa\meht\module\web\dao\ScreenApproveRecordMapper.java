package com.coocaa.meht.module.web.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.meht.module.approve.dto.TodoListQueryDTO;
import com.coocaa.meht.module.approve.dto.TodoTaskDTO;
import com.coocaa.meht.module.approve.dto.TaskDealCountVO;
import com.coocaa.meht.module.web.entity.ScreenApproveRecordEntity;
import com.coocaa.meht.rpc.vo.InnerApproveNodeVO;
import org.apache.commons.compress.utils.Lists;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 审批记录 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Mapper
public interface ScreenApproveRecordMapper extends BaseMapper<ScreenApproveRecordEntity> {

    /**
     * 根据审批实例和版本号查询审批记录
     *
     * @param instanceCode 实例编码
     * @param version      版本号
     * @return 审批记录列表
     */
    default List<ScreenApproveRecordEntity> selectRecordByInstanceCode(
            @Param("instanceCode") String instanceCode, @Param("version") Integer version) {
        if (StrUtil.isBlank(instanceCode)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ScreenApproveRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenApproveRecordEntity::getInstanceCode, instanceCode)
                .eq(version != null, ScreenApproveRecordEntity::getVersion, version)
                .eq(ScreenApproveRecordEntity::getDeleteFlag, false)
                .orderByAsc(ScreenApproveRecordEntity::getApproveLevel);
        return selectList(queryWrapper);
    }

    /**
     * 根据业务编码和版本号查询审批记录
     *
     * @param businessCode 实例编码
     * @param version      版本号
     * @return 审批记录列表
     */
    default List<ScreenApproveRecordEntity> selectRecordByBusinessCode(
            @Param("businessCode") String businessCode, @Param("version") String version) {
        if (StrUtil.isBlank(businessCode)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ScreenApproveRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenApproveRecordEntity::getNaturalKey, businessCode)
                .eq(StrUtil.isNotBlank(version), ScreenApproveRecordEntity::getVersion, version)
                .eq(ScreenApproveRecordEntity::getDeleteFlag, false)
                .orderByDesc(ScreenApproveRecordEntity::getNodeId)
                .orderByDesc(ScreenApproveRecordEntity::getId);
        return selectList(queryWrapper);
    }

    /**
     * 获取第一条审批记录
     *
     * @param naturalKey   业务键
     * @param instanceCode 审批中心实例编码
     * @return 最新审批记录
     */
    default ScreenApproveRecordEntity selectFirstAuditRecord(
            @Param("naturalKey") String naturalKey, @Param("instanceCode") String instanceCode) {
        LambdaQueryWrapper<ScreenApproveRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenApproveRecordEntity::getNaturalKey, naturalKey)
                .eq(ScreenApproveRecordEntity::getDeleteFlag, false)
                .eq(ScreenApproveRecordEntity::getInstanceCode, instanceCode)
                .eq(ScreenApproveRecordEntity::getApproveLevel, 1)
                .last("LIMIT 1");
        return selectOne(queryWrapper);
    }

    /**
     * 根据节点id查询
     *
     * @param taskId 节点 ID
     * @return 当前节点审批记录
     */
    default ScreenApproveRecordEntity selectByTaskId(@Param("taskId") Integer taskId) {
        if (taskId == null) {
            return null;
        }
        LambdaQueryWrapper<ScreenApproveRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenApproveRecordEntity::getNodeId, taskId)
                .eq(ScreenApproveRecordEntity::getDeleteFlag, false)
                .last("LIMIT 1");
        return selectOne(queryWrapper);
    }

    /**
     * 分页查询待办/已办列表
     *
     * @param page     分页参数
     * @param queryDTO 查询条件
     * @param status   节点状态
     * @return 分页结果
     */
    IPage<TodoTaskDTO> selectTaskPage(IPage<TodoTaskDTO> page, @Param("query") TodoListQueryDTO queryDTO, @Param("status") String status);

    void batchUpdateNodeStatus(@Param("nodes") List<InnerApproveNodeVO> nodes);

    List<TaskDealCountVO> countTodoTasksByApprovalType(@Param("approveUser") String approveUser);

}
