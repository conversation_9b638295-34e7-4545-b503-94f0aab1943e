package com.coocaa.meht.module.crm.controller;

import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.crm.service.CrmPreDataSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @file CrmPreDataSyncController
 * @date 2024/12/30 16:26
 * @description CRM前置数据同步
 */

@RestController
@RequestMapping("/crm/pre-data/sync")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Tag(name = "CRM前置数据同步管理")
public class CrmPreDataSyncController {
    private final CrmPreDataSyncService crmPreDataSyncService;


    @PostMapping("/business") // ?batchInsertSize=100
    @Operation(summary = "商机动态同步")
    public Result<String> syncBusiness(@RequestParam(name = "batchInsertSize", defaultValue = "100") int batchInsertSize,
                                       @RequestParam(name = "fetchInterval", defaultValue = "0") int fetchInterval) {
        int rows = crmPreDataSyncService.syncBusinessDynamic(batchInsertSize, fetchInterval);
        if (rows == -1) return Result.error("商机动态数据同步失败！");
        if (rows == 0) return Result.ok("商机动态数据无需同步！");
        if (rows == -2) return Result.ok("商机动态数据正在同步中，请稍候！");
        return Result.ok("商机动态数据同步成功，共插入" + rows + "条数据！");
    }
}
