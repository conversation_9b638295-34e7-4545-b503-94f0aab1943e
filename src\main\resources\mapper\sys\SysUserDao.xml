<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.sys.dao.SysUserDao">
    <select id="getNameList" resultType="com.coocaa.meht.module.sys.dto.SysUserDto">
        SELECT real_name ,emp_code ,0 user_type, org_code belong_code, assess_dept belong_name
        FROM sys_user
        <if test='userCodes != null and userCodes.size() > 0'>
            WHERE
            emp_code IN
            <foreach collection='userCodes' item='code' open='(' separator=',' close=')'>
                #{code}
            </foreach>
        </if>
        UNION
        SELECT emp_name real_name,emp_code,1 user_type,agent_code belong_code, agent_name belong_name
        FROM agent_personnel
        <if test='userCodes != null and userCodes.size() > 0'>
            WHERE
            emp_code IN
            <foreach collection='userCodes' item='code' open='(' separator=',' close=')'>
                #{code}
            </foreach>
        </if>
    </select>
</mapper>