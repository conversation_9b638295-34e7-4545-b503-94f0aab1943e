package com.coocaa.meht.module.web.service.approval.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.PriceApprovalMapper;
import com.coocaa.meht.module.web.entity.PriceApprovalEntity;
import com.coocaa.meht.module.web.service.approval.IPriceApprovalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 价格审批 服务实现类
 *
 * @since 2024-12-04
 */
@Slf4j
@Primary
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PriceApprovalServiceImpl extends ServiceImpl<PriceApprovalMapper, PriceApprovalEntity> implements IPriceApprovalService {

}
