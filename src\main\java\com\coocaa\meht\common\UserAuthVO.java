package com.coocaa.meht.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户登陆
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-19
 */
@Data
@Accessors(chain = true)
public class UserAuthVO {
    @Schema(description = "验证码Hash", type = "String", example = "6d4965bbaf554c05f04a003b9ed4284a")
    private String hash;

    @Schema(description = "验证码", type = "String", example = "123456")
    private String code;

    @Schema(description = "提示信息", type = "String", example = "xxxx")
    private String message;
}
