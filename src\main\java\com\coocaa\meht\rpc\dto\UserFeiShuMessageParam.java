package com.coocaa.meht.rpc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025-04-02
 */
@Data
public class UserFeiShuMessageParam {

    @Schema(description = "应用类型编码，字典0124")
    @NotBlank(message = "应用类型编码不能为空")
    private String appCode;

    @Schema(description = "标题,默认为消息通知")
    private String title = "消息通知";

    @Schema(description = "内容")
    @NotBlank(message = "内容不能为空")
    private String content;

    @Schema(description = "跳转链接")
    private String url;

    @Schema(description = "飞书跳转链接的文本内容")
    private String urlText;

    @Schema(description = "接收用户ID")
    @NotEmpty
    private Set<Integer> receiveUserIds;

    @Schema(description = "发送用户ID,不传默认是当前登陆人")
    private Integer sendUserId;

}
