package com.coocaa.meht.utils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @Date 2023-12-27 10:21
 */
public class IoUtils {

    /**
     * @param input
     * @param output
     * @return
     * @throws IOException
     */
    public static long copy(final InputStream input, final OutputStream output) throws IOException {
        final byte[] buffer = new byte[8024];
        int len;
        long count = 0;
        while (-1 != (len = input.read(buffer))) {
            output.write(buffer, 0, len);
            count += len;
        }
        return count;
    }

    /**
     * @param is
     * @return
     * @throws IOException
     */
    public static byte[] getByte(InputStream is) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int len;
        byte[] b = new byte[8024];
        while ((len = is.read(b)) > 0)
            baos.write(b, 0, len);
        return baos.toByteArray();
    }

    /**
     * @param filename
     * @return
     * @throws IOException
     */
    private byte[] getFileText(String filename) throws IOException {
        File file = new File(filename);
        if (!file.exists() || !file.isFile() || !file.canRead()) {
            throw new IOException("文件不存在或者不可读: " + file.getAbsolutePath());
        }
        try (FileInputStream is = new FileInputStream(file)) {
            return getByte(is);
        }
    }

}
