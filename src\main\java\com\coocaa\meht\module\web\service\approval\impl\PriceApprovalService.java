package com.coocaa.meht.module.web.service.approval.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.feishu.BaseFormItem;
import com.coocaa.meht.common.bean.feishu.CurrencyFormItem;
import com.coocaa.meht.common.bean.feishu.FlowNode;
import com.coocaa.meht.common.bean.feishu.NumberFormItem;
import com.coocaa.meht.common.bean.feishu.StringFormItem;
import com.coocaa.meht.module.web.dao.PriceApplyDao;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.coocaa.meht.module.web.entity.PriceApprovalDetailEntity;
import com.coocaa.meht.module.web.entity.PriceApprovalEntity;
import com.coocaa.meht.module.web.enums.FeiShuApprovalFormIdEnum;
import com.coocaa.meht.module.web.enums.FeiShuFormItemTypeEnum;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.approval.IPriceApprovalDetailService;
import com.coocaa.meht.module.web.service.approval.IPriceApprovalService;
import com.coocaa.meht.utils.FeiShuApprovalUtil;
import com.lark.oapi.service.approval.v4.model.ApprovalNodeInfo;
import com.lark.oapi.service.approval.v4.model.GetInstanceRespBody;
import com.lark.oapi.service.approval.v4.model.InstanceTask;
import com.lark.oapi.service.approval.v4.model.InstanceTimeline;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * 价格审批服务类
 * 处理价格审批流程相关业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PriceApprovalService {


    private final FeiShuApprovalUtil feiShuApprovalUtil;
    private final IPriceApprovalService priceApprovalService;
    private final IPriceApprovalDetailService priceApprovalDetailService;
    private final BuildingRatingService buildingRatingService;
    private final PriceApplyDao priceApplyDao;
    @Value("${price.approval.approvalCode}")
    private String approvalCode;
    @Value("${price.approval.detail.prefix}")
    private String priceDetailPrefix;

    /**
     * 创建价格审批实例
     *
     * @param priceApplyId 价格申请ID
     */
    public void createPriceApprovalInstance(Integer priceApplyId) {
        try {
            // 生成表单提交信息
            String formData = generateContractApprovalFormData(priceApplyId);

            // 获取飞书用户信息
            String feiShuUserId = getFeiShuUserId();

            // 根据合同ID生成uuid;
            int version = generateApprovalVersion(priceApplyId);
            String uuid = priceApplyId + "-" + version + "-" + generateShortUuid();

            // 创建审批实例
            String instanceCode = feiShuApprovalUtil.createApprovalInstance(approvalCode, feiShuUserId, null, formData, uuid);

            //保存到本地数据库
            PriceApprovalEntity entity = new PriceApprovalEntity();
            entity.setPriceApplyId(priceApplyId);
            entity.setApprovalInstanceUuid(uuid);
            entity.setApprovalVersion(version);
            entity.setApprovalInstanceCode(instanceCode);
            entity.setApprovalStatus(0);
            priceApprovalService.save(entity);

            //更新价格申请状态为
            PriceApplyEntity updateBean = new PriceApplyEntity();
            updateBean.setId(priceApplyId);
            updateBean.setStatus(PriceApplyEntity.Status.PENDING.getCode());
            priceApplyDao.updateById(updateBean);

            log.info("创建合同审批实例成功 - userId: {}, instanceCode: {}, priceApplyId: {}, formData: {}, uuid: {}", instanceCode, priceApplyId, formData, uuid);
        } catch (Exception e) {
            log.error("创建合同审批实例失败 - contractId: {}", priceApplyId);
            log.error("异常堆栈:{}", e);
            throw new RuntimeException("创建合同审批实例失败", e);
        }
    }

    /**
     * 获取当前用户的飞书信息
     *
     * @return
     */
    private String getFeiShuUserId() {
        LoginUser user = SecurityUser.getUser();
        if (user == null) {
            throw new IllegalArgumentException("获取用户为空");
        }
        String fsUserId = user.getFsUserId();
        if (StringUtils.isBlank(fsUserId)) {
            throw new IllegalArgumentException("获取用户为空");
        }
        return fsUserId;

    }

    public String generateShortUuid() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase();
    }

    /**
     * 填充申请表单
     * <p>
     * 表单示例
     * [
     * {
     * "id": "contract_apply_no",
     * "type": "input",
     * "value": "1"
     * },
     * {
     * "id": "is_third",
     * "type": "radioV2",
     * "value": "m4mha171-i4wqwrp3iu-0"
     * },
     * {
     * "id": "amount",
     * "type": "amount",
     * "value": 1234.56,
     * "currency": "CNY"
     * }
     * ]
     *
     * @param priceApplyId
     * @return
     */
    private String generateContractApprovalFormData(Integer priceApplyId) {
        //数据库获取价格审批信息
        PriceApplyEntity priceApplyEntity = priceApplyDao.selectById(priceApplyId);
        if (priceApplyEntity == null) {
            throw new IllegalStateException("没有找到此价格申请单");
        }
        BuildingRatingEntity buildingRatingEntity = buildingRatingService.getOne(new QueryWrapper<BuildingRatingEntity>().eq("building_no", priceApplyEntity.getBuildingNo())
                .last("limit 1"));

        List<BaseFormItem> formData = new ArrayList<>();
        //获取价格申请信息

        //价格申请单号
        StringFormItem priceApplyNoItem = generateInputFormItem(FeiShuApprovalFormIdEnum.PRICE_APPLY_NO.getCode(), StringUtils.isNotBlank(priceApplyEntity.getApplyCode()) ? priceApplyEntity.getApplyCode() : "价格申请单号为空");
        formData.add(priceApplyNoItem);

        //项目名称
        StringFormItem priceProjectItem = generateInputFormItem(FeiShuApprovalFormIdEnum.ITEM_NAME.getCode(), StringUtils.isNotBlank(priceApplyEntity.getBuildingName()) ? priceApplyEntity.getBuildingName() : "项目名称为空");
        formData.add(priceProjectItem);

        // 项目城市
        StringFormItem buildingTypeItem = generateInputFormItem(FeiShuApprovalFormIdEnum.BUILDING_TYPE.getCode(), getWuYeType(buildingRatingEntity));
        formData.add(buildingTypeItem);

        // 项目城市
        StringFormItem priceCityItem = generateInputFormItem(FeiShuApprovalFormIdEnum.CITY.getCode(), buildingRatingEntity != null ? buildingRatingEntity.getMapCity() : "未知");
        formData.add(priceCityItem);

        // 总金额
        CurrencyFormItem totalAmountItem = generateAmountFormItem(FeiShuApprovalFormIdEnum.AMOUNT.getCode(), priceApplyEntity.getTotalAmount());
        formData.add(totalAmountItem);

        // 申请人姓名
        LoginUser user = SecurityUser.getUser();
        if (user == null) {
            throw new IllegalArgumentException("获取用户为空");
        }
        StringFormItem submitPersonItem = generateInputFormItem(FeiShuApprovalFormIdEnum.APPLY_USER.getCode(), user.getUserName());
        formData.add(submitPersonItem);
        // 申请时间
        StringFormItem submitTimeItem = generateInputFormItem(FeiShuApprovalFormIdEnum.APPLY_TIME.getCode(), LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        formData.add(submitTimeItem);
        // 合同申请详情
        String detailUrl = generateContractDetailUrl(priceApplyId);
        StringFormItem detailItem = generateInputFormItem(FeiShuApprovalFormIdEnum.DETAILS.getCode(), detailUrl);
        formData.add(detailItem);

        String jsonString = JSON.toJSONString(formData);
        log.info("提交的审批表单数据contractId：{},表单：{}", priceApplyId, jsonString);
        return jsonString;
    }

    /**
     * 物业类型
     *
     * @param buildingRatingEntity
     * @return
     */
    private String getWuYeType(BuildingRatingEntity buildingRatingEntity) {
        if (buildingRatingEntity == null) {
            return "未知";
        }
        Integer buildingType = buildingRatingEntity.getBuildingType();
        switch (buildingType) {
            case 0:
                return BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getName();
            case 1:
                return BuildingRatingEntity.BuildingType.COMMERCIAL_RESIDENTIAL_BUILDING.getName();
            case 2:
                return BuildingRatingEntity.BuildingType.COMPLEX.getName();
            case 3:
                return BuildingRatingEntity.BuildingType.INDUSTRIAL_PARK.getName();
            default:
        }

        return "未知";
    }

    /**
     * 生成审批单中的合同详情链接
     *
     * @param priceApplyId
     * @return
     */
    private String generateContractDetailUrl(Integer priceApplyId) {
        return priceDetailPrefix + priceApplyId;
    }

    /**
     * 创建amount类型条目
     *
     * @param id
     * @param amount
     * @return
     */
    private CurrencyFormItem generateAmountFormItem(String id, BigDecimal amount) {
        CurrencyFormItem item = new CurrencyFormItem();
        item.setId(id);
        item.setType(FeiShuFormItemTypeEnum.AMOUNT.getCode());
        item.setValue(amount);
        item.setCurrency("CNY");
        return item;
    }

    /**
     * 生成input控件item
     *
     * @param id
     * @param value
     * @return
     */
    private StringFormItem generateInputFormItem(String id, String value) {
        StringFormItem item = new StringFormItem();
        item.setId(id);
        item.setType(FeiShuFormItemTypeEnum.INPUT.getCode());
        item.setValue(value);
        return item;
    }

    /**
     * 生成number控件item
     *
     * @param id
     * @param value
     * @return
     */
    private NumberFormItem generateNumberFormItem(String id, BigDecimal value) {
        NumberFormItem item = new NumberFormItem();
        item.setId(id);
        item.setType(FeiShuFormItemTypeEnum.NUMBER.getCode());
        item.setValue(value);
        return item;
    }

    /**
     * 生成radio控件item
     *
     * @param id
     * @param value
     * @return
     */
    private StringFormItem generateRadioFormItem(String id, String value) {
        StringFormItem item = new StringFormItem();
        item.setId(id);
        item.setType(FeiShuFormItemTypeEnum.RADIOV2.getCode());
        item.setValue(value);
        return item;
    }

    /**
     * 生成此价格申请下的申请次数
     *
     * @param priceApplyId 价格申请id
     * @return
     */
    private int generateApprovalVersion(Integer priceApplyId) {
        // 查询此合同最大的已申请次数
        PriceApprovalEntity entity = priceApprovalService.lambdaQuery()
                .eq(PriceApprovalEntity::getPriceApplyId, priceApplyId)
                .orderByDesc(PriceApprovalEntity::getApprovalVersion).last("limit 1").one();
        if (entity == null) {
            return 1;
        }
        return entity.getApprovalVersion() + 1;

    }

    /**
     * 查看审批详情
     *
     * @param priceApplyId 价格id
     */
    public List<FlowNode> getApprovalInstance(Integer priceApplyId) {
        List<FlowNode> approvalFlowList = new ArrayList<>();
        try {
            //根据合同ID获取审批实例中id最大的
            PriceApprovalEntity entity = priceApprovalService.lambdaQuery()
                    .eq(PriceApprovalEntity::getPriceApplyId, priceApplyId)
                    .orderByDesc(PriceApprovalEntity::getApprovalVersion).last("limit 1").one();
            if (entity == null) {
                log.error("没有获取到合同审批实例,priceApplyId:{}", priceApplyId);
                throw new IllegalArgumentException("没有获取到合同审批实例,priceApplyId:" + priceApplyId);
            }

            // 流程已结束从数据库查询
            Integer approvalStatus = entity.getApprovalStatus();
            if (approvalStatus != null && approvalStatus != 0) {
                PriceApprovalDetailEntity detailEntity = priceApprovalDetailService.lambdaQuery()
                        .eq(PriceApprovalDetailEntity::getApprovalInstanceUuid, entity.getApprovalInstanceUuid()).one();
                if (detailEntity != null) {
                    log.error("没有获取到合同审批详情,priceApplyId:{}", priceApplyId);
                    try {
                        approvalFlowList = JSON.parseArray(detailEntity.getNodeListString(), FlowNode.class);
                        return approvalFlowList;
                    } catch (Exception e) {
                        log.error("解析审批详情失败", e);
                    }
                }
            }

            String instanceCode = entity.getApprovalInstanceCode();

            GetInstanceRespBody approvalInstance = feiShuApprovalUtil.getApprovalInstance(instanceCode);
            InstanceTimeline[] timeline = approvalInstance.getTimeline();
            if (timeline == null || timeline.length == 0) {
                return approvalFlowList;
            }
            InstanceTask[] taskListArray = approvalInstance.getTaskList();
            List<InstanceTimeline> timeLineList = Arrays.asList(timeline);
            List<InstanceTask> taskList = null;
            if (taskListArray == null || taskListArray.length == 0) {
                taskList = new ArrayList<>();
            } else {
                taskList = Arrays.asList(taskListArray);
            }
            // 获取taskList转换成map形式
            Map<String, InstanceTask> taskMap = taskList.stream()
                    .collect(Collectors.toMap(InstanceTask::getId, task -> task));

            //根据openId获取飞书用户信息
            List<String> openIds = timeLineList.stream().map(InstanceTimeline::getOpenId)
                    .filter(userId -> userId != null && !userId.isEmpty()).collect(Collectors.toList());
            Map<String, String> userNames = feiShuApprovalUtil.getUserInfoByUserIds(openIds);


            for (InstanceTimeline node : timeLineList) {
                FlowNode flowNode = new FlowNode();
                String openId = node.getOpenId();

                String type = node.getType();
                String taskId = node.getTaskId();

                InstanceTask instanceTask = null;

                //节点名称
                if (StringUtils.isEmpty(taskId)) {
                    if (StringUtils.equals(type, "START")) {
                        flowNode.setNodeName("提交申请");
                    } else {
                        log.info("taskId为空，type为{}", type);
                        continue;
                    }
                } else {
                    instanceTask = taskMap.get(taskId);
                    //审核名称
                    if (instanceTask != null) {
                        flowNode.setNodeName(instanceTask.getNodeName());
                    }
                }

                // 操作人
                flowNode.setOperator(userNames.get(openId));

                // 操作时间
                String formattedTime = formatTimestamp(node.getCreateTime());
                flowNode.setOperateTime(formattedTime);

                //审核意见
                flowNode.setComment(node.getComment());

                //审批状态
                if (instanceTask != null) {
                    flowNode.setStatus(StringUtils.equals(type, "START") ? "" : convertApprovalStatus(instanceTask.getStatus()));
                }
                approvalFlowList.add(flowNode);
            }
            // 获取taskListArray中最后一个对象
            if (taskList.size() == 0) {
                return approvalFlowList;
            }
            InstanceTask lastTask = taskList.get(taskList.size() - 1);
            // timeLineList转换taskIdSet
            Set<String> taskIdSet = timeLineList.stream().map(InstanceTimeline::getTaskId).collect(Collectors.toSet());
            if (lastTask != null && !taskIdSet.contains(lastTask.getId())) {
                FlowNode flowNode = new FlowNode();
                flowNode.setNodeName(lastTask.getNodeName());
                flowNode.setOperator(userNames.get(lastTask.getOpenId()));
                String formattedTime = formatTimestamp(lastTask.getStartTime());
                flowNode.setOperateTime(formattedTime);
                flowNode.setStatus(convertApprovalStatus(lastTask.getStatus()));
                approvalFlowList.add(flowNode);
            }
            return approvalFlowList;
        } catch (Exception e) {
            log.error("获取审批流异常，异常信息：{}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }


    private String convertApprovalStatus(String type) {
        switch (type) {
            case "PENDING":
                return "进行中";
            case "APPROVED":
                return "已同意";
            case "REJECTED":
                return "已拒绝";
            case "TRANSFERRED":
                return "已转交";
            case "DONE":
                return "已完成";
            default:
                return type;
        }
    }

    /**
     * 获取审批定义
     */
    public List<ApprovalNodeInfo> getApproveDefinition() {
        List<ApprovalNodeInfo> approvalDefinition = feiShuApprovalUtil.getApprovalDefinition(approvalCode);
        return approvalDefinition;
    }

    private String convertNodeName(String type) {
        switch (type) {
            case "START":
                return "提交申请";
            case "AUTO_PASS":
                return "自动通过";
            case "PASS":
                return "审核通过";
            case "REJECT":
                return "审核拒绝";
            default:
                return type;
        }
    }

    private String formatTimestamp(String timestamp) {
        // 将时间戳转换为 yyyy/M/d HH:mm:ss 格式
        long time = Long.parseLong(timestamp);
        return new SimpleDateFormat("yyyy/M/d HH:mm:ss").format(new Date(time));
    }
}
