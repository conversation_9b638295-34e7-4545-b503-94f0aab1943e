package com.coocaa.meht.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class UserLoginParam {
    @Schema(description = "用户名/手机号/邮箱/工号")
    private String name;

    @Schema(description = "密码 (需要对明文进行MD5后传输)")
    private String pwd;

    @Schema(description = "短信验证码")
    private String smsCode;

    @Schema(description = "验证码会话ID")
    private String sid;

    @Schema(description = "验证码选择的坐标点")
    private List<String> points;
}
