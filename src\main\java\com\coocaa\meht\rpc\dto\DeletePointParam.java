package com.coocaa.meht.rpc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: cheese-meht
 * @ClassName DeletePointParam
 * @description:
 * @author: zhangbinxian
 * @create: 2025-01-16 11:49
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeletePointParam {

    private List<Integer> waitingHallIds;

    private List<String> pointCodes;
}
