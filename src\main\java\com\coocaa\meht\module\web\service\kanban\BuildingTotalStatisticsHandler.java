package com.coocaa.meht.module.web.service.kanban;

import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.vo.kanban.IndexVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanVO;
import com.coocaa.meht.module.web.vo.kanban.SectionVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.common.util.StringUtils;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @file BuildingTotalStatisticsHandler
 * @date 2025/1/3 15:24
 * @description 楼宇 - 总量统计
 */

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingTotalStatisticsHandler {
    public final static String CACHE_KEY_FORMAT = "kanban:city:%s:building:total_statistics";
    public final static String KPI_FLAG = "总量统计";
    private final static String FOLLOWING_UP = "跟进中";
    private final static String DEAL = "成交";
    private final static String PROJECT_LEVEL = "已认证楼宇";
    private final static String RATING_APPLY = "评级申请";
    private final static String BUILDING_STATUS = "项目(商机)";
    private final static String INDEX_DEFAULT_PLACEHOLDER = "-";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final List<String> PROJECT_LEVEL_LIST = List.of("A", "AA", "AAA");

    // 初始化映射关系
    private final static Map<String, String> buildingStatusConvertMap;
    private final static Map<String, String> ratingApplicationStatusConvertMap;

    static {
        buildingStatusConvertMap = new HashMap<>(6);
        buildingStatusConvertMap.put(BuildingStatusChangeLogEntity.BizStatus.WAIT_NEGOTIATION.getCode(), FOLLOWING_UP);
        buildingStatusConvertMap.put(BuildingStatusChangeLogEntity.BizStatus.PRELIMINARY_NEGOTIATION.getCode(), FOLLOWING_UP);
        buildingStatusConvertMap.put(BuildingStatusChangeLogEntity.BizStatus.INTENTION_ACCOMPLISHED.getCode(), FOLLOWING_UP);
        buildingStatusConvertMap.put(BuildingStatusChangeLogEntity.BizStatus.QUOTATION.getCode(), FOLLOWING_UP);
        buildingStatusConvertMap.put(BuildingStatusChangeLogEntity.BizStatus.CONTRACT_PROCESS.getCode(), FOLLOWING_UP);
        buildingStatusConvertMap.put(BuildingStatusChangeLogEntity.BizStatus.DEAL.getCode(), DEAL);
        //
        ratingApplicationStatusConvertMap = Arrays.stream(BuildingStatusChangeLogEntity.RatingApplicationStatus.values())
                .collect(Collectors.toMap(BuildingStatusChangeLogEntity.RatingApplicationStatus::getCode,
                        BuildingStatusChangeLogEntity.RatingApplicationStatus::getDesc, (v1, v2) -> v1, LinkedHashMap::new));
    }

    /**
     * 计算城市的指标数据
     */
    public KanbanVO getKanbanVOByCityResultList(List<String> cityResultList) {
        // 汇总城市数据
        Map<String, Map<String, Integer>> allCityMap = initCityBaseMap();
        cityResultList.forEach(cityResult -> {
            try {
                KanbanVO kanbanVO = OBJECT_MAPPER.readValue(cityResult, KanbanVO.class);
                if (kanbanVO == null) {
                    return;
                }
                // 更新总量数据
                kanbanVO.getSections().forEach(sectionVO -> {
                    Map<String, Integer> map = allCityMap.get(sectionVO.getMainTitle());
                    sectionVO.getIndices().forEach(indexVO -> {
                        int kpiValue = Integer.parseInt(indexVO.getValue1());
                        if (kpiValue != 0) {
                            map.compute(indexVO.getName(), (k, v) -> v == null ? kpiValue : v + kpiValue);
                        }
                    });
                });
            } catch (JsonProcessingException e) {
                log.error("总量统计缓存数据转换失败！{}", e.getMessage());
                throw new RuntimeException(e);
            }
        });
        // 生成KanbanVO
        return genKanbanVO(allCityMap);
    }

    /**
     * 生成初始的指标Map结构
     */
    private Map<String, Map<String, Integer>> initCityBaseMap() {
        // 已认证楼宇
        Map<String, Integer> projectLevelMap = new LinkedHashMap<>(4);
        projectLevelMap.put(PROJECT_LEVEL, 0);
        for (String projectLevel : PROJECT_LEVEL_LIST.stream().sorted(Comparator.reverseOrder()).toList()) {
            projectLevelMap.put(projectLevel, 0);
        }
        // 评级申请
        Map<String, Integer> ratingApplicationStatusMap = new LinkedHashMap<>(ratingApplicationStatusConvertMap.size() + 1);
        ratingApplicationStatusMap.put(RATING_APPLY, 0);
        ratingApplicationStatusConvertMap.values().forEach(key -> {
            ratingApplicationStatusMap.put(key, 0);
        });
        // 项目(商机)
        Map<String, Integer> buildingStatusMap = new LinkedHashMap<>(3);
        buildingStatusMap.put(BUILDING_STATUS, 0);
        buildingStatusMap.put(FOLLOWING_UP, 0);
        buildingStatusMap.put(DEAL, 0);
        // ===================================================================
        Map<String, Map<String, Integer>> cityMap = new LinkedHashMap<>(3);
        cityMap.put(PROJECT_LEVEL, projectLevelMap);
        cityMap.put(RATING_APPLY, ratingApplicationStatusMap);
        cityMap.put(BUILDING_STATUS, buildingStatusMap);

        return cityMap;
    }

    /**
     * 对楼宇记录进行统计 - 按城市
     */
    public void buildingRecordsAnalysedByCity(
            Map<String, List<BuildingRatingEntity>> buildingRatingByCityCodeMap,
            Map<String, List<BusinessOpportunityEntity>> businessOpportunityByBuildingNoMap,
            Map<String, Map<String, List<BuildingStatusChangeLogEntity>>> buildingStatusChangeLogByBizCodeMap,
            Map<String, KanbanVO> kanbanVOByCityCodeMap
    ) {
        LocalDateTime yesterdayEndTime = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.of(23, 59, 59));
        // 对楼宇记录进行统计 - 按城市
        buildingRatingByCityCodeMap.forEach((cityCode, buildingRatingEntityList) -> {
            //
            //if (cityCode.equals("阿坝藏族羌族自治州")) {
            if (cityCode.equals("成都市")) {
                System.out.println(1);
            }
            //
            AtomicInteger projectLevelCount = new AtomicInteger();
            AtomicInteger businessOpportunityCount = new AtomicInteger();
            Map<String, Map<String, Integer>> cityMap = initCityBaseMap();

            // 仅楼宇评级的log记录
            Map<String, List<BuildingStatusChangeLogEntity>> ratingLogMap =
                    buildingStatusChangeLogByBizCodeMap.get(BuildingStatusChangeLogEntity.BizType.RATING.getCode());
            // 仅项目商机的log记录
            Map<String, List<BuildingStatusChangeLogEntity>> businessOpportunityLogMap =
                    buildingStatusChangeLogByBizCodeMap.get(BuildingStatusChangeLogEntity.BizType.BUSINESS.getCode());

            buildingRatingEntityList.forEach(buildingRatingEntity -> {
                // 已认证楼宇
                if (PROJECT_LEVEL_LIST.contains(buildingRatingEntity.getProjectLevel()) // 限定评级范围
                        && buildingRatingEntity.getStatus() == BuildingRatingEntity.Status.AUDITED.getValue() // 【已认证楼宇】指已经参与认证的(评级申请审核通过)的楼宇的总数量
                        && buildingRatingEntity.getApproveTime() != null && !buildingRatingEntity.getApproveTime().isAfter(yesterdayEndTime)) { // 限定时间范围
                    projectLevelHandle(buildingRatingEntity.getProjectLevel(), cityMap);
                    projectLevelCount.getAndIncrement();
                }
                // 评级申请
                if (ratingLogMap != null) {
                    List<BuildingStatusChangeLogEntity> buildingStatusChangeLogEntityList = ratingLogMap.get(buildingRatingEntity.getBuildingNo());
                    if (buildingStatusChangeLogEntityList != null) {
                        ratingApplicationHandle(buildingStatusChangeLogEntityList, cityMap);
                    }
                }
                // 项目(商机)
                if (businessOpportunityLogMap != null) {
                    // 获取商机记录
                    List<BusinessOpportunityEntity> businessOpportunityEntityList =
                            businessOpportunityByBuildingNoMap.get(buildingRatingEntity.getBuildingNo());
                    if (businessOpportunityEntityList != null) {
                        businessOpportunityCount.getAndAdd(businessOpportunityCount.get() + businessOpportunityEntityList.size());
                        businessOpportunityEntityList.forEach(businessOpportunityEntity -> {
                            // 获取商机log记录
                            List<BuildingStatusChangeLogEntity> businessOpportunityLogEntityList =
                                    businessOpportunityLogMap.get(businessOpportunityEntity.getCode());
                            if (businessOpportunityLogEntityList != null) {
                                businessOpportunityHandle(businessOpportunityLogEntityList, cityMap);
                            }
                        });
                    }
                }
            });
            // 更新总量数据
            cityMap.get(PROJECT_LEVEL).put(PROJECT_LEVEL, projectLevelCount.get());

            // 生成指标对象
            kanbanVOByCityCodeMap.put(cityCode, genKanbanVO(cityMap));
        });
    }

    /**
     * 已认证楼宇
     */
    private void projectLevelHandle(String projectLevel, Map<String, Map<String, Integer>> cityMap) {
        Map<String, Integer> projLevelMap = cityMap.get(PROJECT_LEVEL);
        if (projLevelMap.containsKey(projectLevel)) projLevelMap.put(projectLevel, projLevelMap.get(projectLevel) + 1);
    }

    /**
     * 评级申请
     * - 暂时没有可以统计的数据
     */
    private void ratingApplicationHandle(List<BuildingStatusChangeLogEntity> buildingStatusChangeLogEntityList, Map<String, Map<String, Integer>> cityMap) {
        Map<String, Integer> ratingApplicationStatusMap = cityMap.get(RATING_APPLY);
        BuildingStatusChangeLogEntity latestBuildingStatusChangeLogEntity;
        if (buildingStatusChangeLogEntityList.size() == 1)
            latestBuildingStatusChangeLogEntity = buildingStatusChangeLogEntityList.get(0);
        else latestBuildingStatusChangeLogEntity = buildingStatusChangeLogEntityList.stream()
                .max(Comparator.comparing(BuildingStatusChangeLogEntity::getChangeTime)).orElseThrow(() -> new NoSuchElementException("列表为空"));
        //
        String statusName = ratingApplicationStatusConvertMap.get(latestBuildingStatusChangeLogEntity.getStatus());
        if (StringUtils.isBlank(statusName)) return;
        ratingApplicationStatusMap.put(statusName, ratingApplicationStatusMap.get(statusName) + 1);
        // 总量数据
        ratingApplicationStatusMap.put(RATING_APPLY, ratingApplicationStatusMap.get(RATING_APPLY) + 1);
    }

    /**
     * 项目(商机)
     */
    private void businessOpportunityHandle(List<BuildingStatusChangeLogEntity> buildingStatusChangeLogEntityList, Map<String, Map<String, Integer>> cityMap) {
        // 找到商机的最新的动态
        BuildingStatusChangeLogEntity latestBuildingStatusEntity = buildingStatusChangeLogEntityList.stream()
                .max(Comparator.comparing(BuildingStatusChangeLogEntity::getChangeTime)).orElseThrow(() -> new NoSuchElementException("列表为空"));
        // buildingStatus
        Map<String, Integer> buildingStatusMap = cityMap.get(BUILDING_STATUS);
        String s = buildingStatusConvertMap.get(latestBuildingStatusEntity.getStatus());
        if (s != null) {
            buildingStatusMap.put(s, buildingStatusMap.get(s) + 1);
        }
        // 总量数据
        buildingStatusMap.put(BUILDING_STATUS, buildingStatusMap.get(BUILDING_STATUS) + 1);
    }

    /**
     * 生成指标对象
     */
    private KanbanVO genKanbanVO(Map<String, Map<String, Integer>> cityMap) {
        return genKanbanVO(cityMap, null);
    }

    private KanbanVO genKanbanVO(Map<String, Map<String, Integer>> cityMap, String defaultValue) {
        List<SectionVO> sections = new ArrayList<>(cityMap.size());
        KanbanVO kanbanVO = new KanbanVO().setMainTitle(KPI_FLAG).setSubTitle("截止昨日23:59:59").setShowType(1)
                .setTips("展示鼠标移动上去提示").setShow(true).setTab(true)
                .setSections(sections);
        cityMap.forEach((k1, map1) -> {
            //
            SectionVO sectionVO = new SectionVO();
            sections.add(sectionVO);
            List<IndexVO> indices = new ArrayList<>(map1.size());
            sectionVO.setMainTitle(k1).setSubTitle(INDEX_DEFAULT_PLACEHOLDER).setShowCount(map1.size())
                    .setDataType("index").setShow(true).setIndices(indices);
            //
            map1.forEach((k2, v2) -> {
                String val = defaultValue != null && v2 == 0 ? defaultValue : v2 + "";
                indices.add(new IndexVO().setKey(INDEX_DEFAULT_PLACEHOLDER).setName(k2)
                        .setValue1(val)
                        .setValue2(INDEX_DEFAULT_PLACEHOLDER).setUnitType("number")
                        .setShow(true).setMain(k1.equals(k2)));
            });
        });

        return kanbanVO;
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-13 21:25
     * @Description：生成统计结果的默认值
     */
    public KanbanVO getDefaultKanbanVO() {
        return genKanbanVO(initCityBaseMap(), INDEX_DEFAULT_PLACEHOLDER);
    }
}
