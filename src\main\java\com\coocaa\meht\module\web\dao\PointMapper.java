package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.web.dto.PointDetailDto;
import com.coocaa.meht.module.web.dto.WaitingHallInformationDto;
import com.coocaa.meht.module.web.dto.WaitingHallPointInformationDto;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.dto.point.ProjectPointCountVO;
import com.coocaa.meht.module.web.entity.PointEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PointMapper extends BaseMapper<PointEntity> {
    List<PointDetail> listBuildingPoint(String buildingNo);

    List<ProjectPointCountVO> listBuildingPointCount(String buildingNo);

    List<PointDetail> listPriceApplyPoint(@Param("list") List<String> list );


    List<String> getWaitingHallPointInformation(@Param("list")List<Integer> list);

    List<PointDetailDto> listPointToContract(String buildingNo);

    List<PointDetail> listByBusinessCodePoint(@Param("BusinessCode") String BusinessCode);


    List<String> getAllByBusinessCodeStrings();

}