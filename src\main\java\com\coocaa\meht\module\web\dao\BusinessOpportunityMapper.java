package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.module.crm.dto.req.CmsBusinessReq;
import com.coocaa.meht.module.crm.vo.BusinessVO;
import com.coocaa.meht.module.web.dto.BusinessOpportunityWithRatingDto;
import com.coocaa.meht.module.web.dto.BusinessProjectDto;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 商机管理
 */
@Mapper
public interface BusinessOpportunityMapper extends BaseMapper<BusinessOpportunityEntity> {

    List<BusinessProjectDto> getPriceApplyBusiness(@Param("name") String name, @Param("allUser") Boolean allUser,
                                                   @Param("status") List<String> status, @Param("userCode") String userCode);

    /**
     * 查询用户商机列表
     * (方案报价阶段)
     */
    default IPage<BusinessOpportunityEntity> pageByOwner(CmsBusinessReq cmsBusinessReq) {
        Page<BusinessOpportunityEntity> page = new Page<>(cmsBusinessReq.getPage(), cmsBusinessReq.getLimit());
        LambdaQueryWrapper<BusinessOpportunityEntity> queryWrapper = new QueryWrapper<BusinessOpportunityEntity>().lambda()
                .eq(BusinessOpportunityEntity::getOwner, cmsBusinessReq.getUserCode())
                .eq(BusinessOpportunityEntity::getStatus, BusinessChangeStatusEnum.PROPOSAL_QUOTATION.getCode())
                .like(StringUtils.isNotBlank(cmsBusinessReq.getSearch()), BusinessOpportunityEntity::getName, cmsBusinessReq.getSearch())
                .isNotNull(BusinessOpportunityEntity::getCustomerId)
                .isNotNull(BusinessOpportunityEntity::getBuildingNo)
                .orderByDesc(BusinessOpportunityEntity::getId);
        return this.selectPage(page, queryWrapper);
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-16 17:11
     * @Description：获取指定时间范围的商机列表
     */
    List<BusinessOpportunityWithRatingDto> getBusinessOpportunityWithRatingDtoList(@Param("timeRangeMap") Map<String, LocalDateTime> timeRangeMap);

    LocalDateTime getEarliestChangeTime(@Param("buildingNo") String buildingNo, @Param("status") String status);

    /**
     * 查询商机列表
     */
    Page<BusinessOpportunityEntity> list(Page<BusinessOpportunityEntity> page,
              @Param("name") String name,
              @Param("statuses") List<String> statuses,
              @Param("cities") List<String> cities,
              @Param("userCodes") List<String> userCodes,
              @Param("excludeUserCode") String excludeUserCode);

}
