package com.coocaa.meht.module.web.dto.point;

import com.coocaa.meht.converter.Convert;
import com.coocaa.meht.converter.ConvertType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/14
 */
@Data
@JsonInclude()
public class ProjectPointVO {
    @Schema(description = "top值")
    private String topLevel;
    private String orderCode;
    private String projectName;
    @Convert(type = ConvertType.DICT)
    private String pointPlanStatus;
    private String pointPlanStatusName;
    private List<PointDetail> pointDetails = new ArrayList<>();

    /**
     * 项目地址
     */
    @JsonIgnore
    private String projectAddress;

    /**
     * 商机名称
     */
    @JsonIgnore
    private String businessOpportunityName;

    /**
     * 楼宇编号
     */
    @JsonIgnore
    private String buildingNo;

}
