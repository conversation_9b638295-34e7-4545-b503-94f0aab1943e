@startuml
title 价格申请草稿保存 POST /price/apply/draft

start

if (商机编码为空?) then (是)
    :抛出异常 "请先选择商机";
endif

if (楼宇编码为空?) then (是)
    :抛出异常 "楼宇编码为空";
endif

:查询当前用户和商机下的旧草稿信息;
if (存在旧草稿?) then (是)
    :删除旧草稿记录;
    :删除旧草稿设备信息;
    :删除旧草稿点位信息;
endif

:构建新的 PriceApplyEntity;
if (存在旧草稿?) then (是)
    :复用旧草稿 ID;
    :保留创建时间;
    :复用申请编码;
else (否)
    :设置新创建时间;
    :生成新申请编码 getApplyCode();
endif

:将 applyDto 转换为 JSON 存入 draft 字段;
:补充楼宇评级信息 buildRatingInfo();

:保存 priceApplyDraft;
note right: 包括新增或更新操作

:获取设备列表 devices;
if (设备列表非空?) then (是)
    while (循环设备列表devices)
        :转换为 PriceApplyDeviceEntity;
        :设置 applyId;
        :保存设备信息;

        :获取点位列表 points;
        if (点位列表非空?)
            :构建 PriceApplyDevicePointEntity 列表;
            :加入 allPointEntities;
        endif
    endwhile

    if (存在点位信息?) then (是)
        :批量保存点位信息;
    endif
endif

:返回;

stop

footer
注:
1. 整个流程在 @Transactional 事务中执行
2. 所有异常均触发事务回滚
end footer

@enduml