package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.web.entity.CityCoefficientEntity;
import com.coocaa.meht.module.web.service.CityCoefficientService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *  城市系数
 */
@RestController
@RequestMapping("/coefficient")
@AllArgsConstructor
public class CityCoefficientController {
    @Resource
    private CityCoefficientService cityCoefficientService;

    @GetMapping("/getCoefficient")
    @Reqlog(value = "城市系数", type = Reqlog.LogType.SELECT)
    public Result<CityCoefficientEntity> list(String adCode) {
        CityCoefficientEntity cityCoefficientEntity = cityCoefficientService.getCoefficient(adCode);
        return Result.ok(cityCoefficientEntity);
    }
}
