package com.coocaa.meht.module.crm.service;

import java.util.Map;

/**
 * CRM接口代理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26
 */
public interface CrmProxyService {
    /**
     * 代理CRM接口
     *
     * @param url         URL
     * @param queryParams 查询参数
     * @return CRM结果
     */
    String proxyPost(String url, Map<String, String> queryParams);

    /**
     * 代理CRM接口
     *
     * @param url  URL
     * @param body Body
     * @return CRM结果
     */
    String proxyPost(String url, String body);

    /**
     * 获取CRM用户Token
     *
     * @return CRM Token
     */
    String getCrmToken();





}
