package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
* 公海数据出入记录
* <AUTHOR>
* @version 1.0
* @since 2025-04-15
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "high_sea_record")
public class HighSeaRecordEntity extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 客户编码
     */
    private String buildingNo;

    /**
     * 客户名称
     */
    private String buildingName;

    /**
     * 操作类型：0-转出，1-转入
     */
    private Integer operateType;

    /**
     * 原负责人
     */
    private String responsiblePerson;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

    /**
     * 备注
     */
    private String remark;

}