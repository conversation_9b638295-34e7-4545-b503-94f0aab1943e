package com.coocaa.meht.module.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 附件管理
 */
@Mapper
public interface SysFileDao extends BaseMapper<SysFileEntity> {

    @Select("select * from sys_file where url = #{url};")
    SysFileEntity getByUrl(String url);
}