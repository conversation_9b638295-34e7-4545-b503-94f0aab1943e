package com.coocaa.meht.common.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 大屏点位系数计算基准
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-16
 */
@Data
public class BigScreenCalculateRule {

    /**
     * 优先级，数字越小，优先级越高
     */
    private Integer priority;

    /**
     * 系数
     */
    private String coefficient;

    /**
     * 挑高
     */
    private BigDecimal height;

    /**
     * 间距
     */
    private BigDecimal spacing;

    /**
     * 楼龄
     */
    private Integer age;

    /**
     * 楼层
     */
    private Integer floor;

    /**
     * 核心区域标识
     */
    private Boolean coreFlag;

}
