package com.coocaa.meht.module.crm.dto.req;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@Accessors(chain = true)
public class CustomerListReq extends CrmPageReq {

    private String customerId;

    private String sceneId = "1859419586110681088";

    private Integer type = 2;

    private String search = "";

    @Schema(description = "场景类型")
    @NotBlank(message = "场景类型不能为空")
    private String sceneType;

    @Schema(description = "进公海时间开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String toSeaTimeStart;

    @Schema(description = "进公海时间结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String toSeaTimeEnd;

}
