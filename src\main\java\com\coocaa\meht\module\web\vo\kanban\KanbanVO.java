package com.coocaa.meht.module.web.vo.kanban;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 看板数据
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-27
 */
@Data
@Accessors(chain = true)
public class KanbanVO {
    /**
     * 主标题
     */
    @Schema(description = "主标题", type = "String", example = "主要指标")
    private String mainTitle;

    /**
     * 子标题
     */
    @Schema(description = "子标题", type = "String", example = "子指标")
    private String subTitle;

    /**
     * 显示类型（与前端约定）
     */
    @Schema(description = "显示类型", type = "Integer", example = "1")
    private Integer showType;

    /**
     * 提示信息
     */
    @Schema(description = "提示信息", type = "String", example = "这是提示信息")
    private String tips;

    /**
     * 是否显示
     */
    @Schema(description = "是否显示", type = "Boolean", example = "true")
    private boolean show;

    /**
     * 是否为标签
     */
    @Schema(description = "是否有标签", type = "Boolean", example = "false")
    private boolean tab;

    /**
     * 指标分组
     */
    @Schema(description = "指标分组", type = "List<SectionVO>")
    private List<SectionVO> sections;

    /**
     * 指标标签
     */
    @Schema(description = "指标标签", type = "List<TabVO>")
    private List<TabVO> tabs;
}
