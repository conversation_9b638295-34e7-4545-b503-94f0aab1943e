package com.coocaa.meht.config;

import jakarta.validation.Validation;
import jakarta.validation.ValidatorFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 验证器配置类
 * 定义 ValidatorFactory Bean，用于支持 Jakarta Validation
 * @since 2025-04-23
 */
@Configuration
public class ValidationConfig {
    @Bean
    public ValidatorFactory validatorFactory() {
        return Validation.buildDefaultValidatorFactory();
    }
}
