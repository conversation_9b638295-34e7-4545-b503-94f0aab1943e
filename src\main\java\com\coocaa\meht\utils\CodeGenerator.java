package com.coocaa.meht.utils;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.lang.id.NanoId;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;

/**
 * 各种编码生成
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public final class CodeGenerator {
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");

    private static final String PREFIX = "rating:code:";
    private static final String COMPLETE_RATING_CODE_KEY = "complete:code:";

    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 生成完善编码：楼宇编码+WS+“-”+个数，例如：BRR250611001WS-1
     *
     * @return 商机编码
     */
    public String getCompleteCode(String buildingNo) {
        String cacheKey = COMPLETE_RATING_CODE_KEY + buildingNo;
        Long index = stringRedisTemplate.opsForValue().increment(cacheKey);
        return buildingNo + "WS" + index;
    }

    /**
     * 生成楼宇评级编码：BRR+年月日+3位数字(每天从001开始)，例如BRR250611001
     *
     * @return 楼宇评级编码
     */
    public String generateRatingCode() {
        String today = DATE_FORMATTER.format(LocalDate.now());
        String cacheKey = PREFIX + today;
        Long index = stringRedisTemplate.opsForValue().increment(cacheKey);
        stringRedisTemplate.expire(cacheKey, 24, TimeUnit.HOURS);
        return String.format("BRR%s%03d", DATE_FORMATTER.format(LocalDate.now()), index);
    }

    /**
     * 生成楼宇评级版本号
     *
     * @return 楼宇评级版本号
     */
    public String generateRatingVersion() {
        return NanoId.randomNanoId(20);
    }

}
