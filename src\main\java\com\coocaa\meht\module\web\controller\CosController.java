package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.cos.CosVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * COS读写
 */
@RestController
@RequestMapping("/cos")
@AllArgsConstructor
@Tag(name = "cos相关接口", description = "cos相关接口")
public class CosController {

    @Autowired
    private final FeignAuthorityRpc feignAuthorityRpc;

    @Anonymous
    @Operation(summary = "获取私有读写桶临时密钥", description = "获取私有读写桶临时密钥")
    @GetMapping("/temp/key/private")
    public Result<CosVO> getPrivateTempKey() {
        ResultTemplate<CosVO> cosVO = feignAuthorityRpc.getPrivateTempKey();
        return Result.ok(cosVO.getData());
    }
}
