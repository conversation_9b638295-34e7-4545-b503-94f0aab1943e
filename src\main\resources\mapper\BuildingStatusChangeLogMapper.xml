<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.coocaa.meht.module.web.dao.BuildingStatusChangeLogMapper">
    <select id="selectByStatus" resultType="com.coocaa.meht.module.web.dto.BuildingStatusChangeLogWithRatingDto">
        SELECT log.biz_id, log.biz_code, log.status, log.change_time, rating.project_level, rating.map_city
        FROM building_rating rating
        JOIN building_status_change_log log
        ON rating.id = log.biz_id
        WHERE log.status = #{status} AND log.`type`= #{type} AND log.delete_flag = 0
        <if test="timeRangeMap!=null and timeRangeMap.size() > 0">
            AND log.change_time BETWEEN #{timeRangeMap.start} AND #{timeRangeMap.end}
        </if>
        <if test="projectLevelList != null and projectLevelList.size() > 0">
            AND rating.project_level IN
            <foreach collection="projectLevelList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectOfRatingApplicationRecords"
            resultType="com.coocaa.meht.module.web.dto.BuildingStatusChangeLogWithRatingDto">
        SELECT log.biz_id, log.`type`, log.biz_code, log.`status`, log.change_time, rating.map_city
        FROM building_rating rating
        JOIN building_status_change_log log
        ON rating.id = log.biz_id
        WHERE log.status = #{status} AND log.`type`= #{type} AND log.delete_flag = 0
        <if test="timeRangeMap!=null and timeRangeMap.size() > 0">
            AND log.change_time BETWEEN #{timeRangeMap.start} AND #{timeRangeMap.end}
        </if>
    </select>
    <select id="getBuildingRatingStatusChangeList" resultType="com.coocaa.meht.module.web.vo.kanban.StatusChangeVO"
            parameterType="com.coocaa.meht.module.web.vo.kanban.StatusChangeVO">
        SELECT
        log.id AS id,
        log.type AS type,
        log.sub_type AS subType,
        log.biz_id AS bizId,
        log.STATUS AS status,
        log.change_time AS changeTime,
        rating.map_city AS cityName
        FROM
        building_status_change_log log
        LEFT JOIN building_rating rating ON log.biz_id = rating.id
        WHERE
        log.delete_flag = 0 AND rating.map_city IS NOT NULL
        <if test="param.status != null">
            AND log.STATUS = #{param.status}
        </if>
        <if test="param.type != null">
            AND log.`type`= #{param.type}
        </if>
        <if test="param.subType != null">
            AND log.`sub_type`= #{param.subType}
        </if>
        <if test="param.changeTimeStart!=null">
            AND log.change_time &gt;= #{param.changeTimeStart}
        </if>
        <if test="param.changeTimeEnd!=null">
            AND log.change_time &lt;= #{param.changeTimeEnd}
        </if>
        <if test="param.bizIds != null and param.bizIds.size() > 0">
            AND log.biz_id IN
            <foreach collection="param.bizIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getProjectStatusChangeList" resultType="com.coocaa.meht.module.web.vo.kanban.StatusChangeVO"
            parameterType="com.coocaa.meht.module.web.vo.kanban.StatusChangeVO">
        SELECT
        log.id AS id,
        log.type AS type,
        log.sub_type AS subType,
        log.biz_id AS bizId,
        log.STATUS AS STATUS,
        log.change_time AS changeTime,
        rating.map_city AS cityName
        FROM
        building_status_change_log log
        LEFT JOIN business_opportunity bo ON log.biz_id = bo.id
        LEFT JOIN building_rating rating ON rating.building_no = bo.building_no
        WHERE
        log.delete_flag = 0
        <if test="param.status != null">
            AND log.STATUS = #{param.status}
        </if>
        <if test="param.type != null">
            AND log.`type`= #{param.type}
        </if>
        <if test="param.subType != null">
            AND log.`sub_type`= #{param.subType}
        </if>
        <if test="param.changeTimeStart!=null">
            AND log.change_time &gt;= #{param.changeTimeStart}
        </if>
        <if test="param.changeTimeEnd!=null">
            AND log.change_time &lt;= #{param.changeTimeEnd}
        </if>
        <if test="param.bizIds != null and param.bizIds.size() > 0">
            AND log.biz_id IN
            <foreach collection="param.bizIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>