package com.coocaa.meht.module.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月18 15:55
 */
@Data
public class BuildingMetaImgListDto {

    private Long id;

    /**
     * 楼宇编码
     */
    @Schema(description = "楼宇编码")
    private String buildingMetaNo;

    /**
     * 外墙材料附件地址
     */
    @Schema(description = "外墙材料附件地址")
    private List<String> buildingExteriorPic;
    /**
     * 大堂高度及装饰附件地址
     */
    @Schema(description = "大堂高度及装饰附件地址")
    private List<String> buildingLobbyPic;
    /**
     * 大堂高度及装饰附件地址
     */
    @Schema(description = "大堂高度及装饰附件地址")
    private List<String> buildingHallPic;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Integer creator;
}
