package com.coocaa.meht.rpc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 审批发起参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-04
 */
@Data
public class ApprovalInitiateParam {
    
    @Schema(description = "规则编号")
    @NotNull(message = "规则编号不能为空")
    private Integer ruleCode;

    @Schema(description = "审批表单数据")
    @NotBlank(message = "审批表单不能为空") 
    private String from;
    
    @Schema(description = "业务唯一标识")
    private String businessKey;
} 