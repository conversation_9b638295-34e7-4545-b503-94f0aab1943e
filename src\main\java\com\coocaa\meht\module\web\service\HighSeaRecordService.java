package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.HighSeaRecordEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
* <AUTHOR>
* @version 1.0
* @since 2025-04-15
*/
public interface HighSeaRecordService extends IService<HighSeaRecordEntity>{

        void record(LocalDateTime operateTime, String operateUserCode, String enterSeaReason, BuildingRatingEntity.HighSeaFlagEnum highSeaFlagEnum, List<BuildingRatingEntity> buildingRatings);
    }
