<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.web.dao.PriceApplyDeviceDao">

    <select id="getPointByApplyNumber" resultType="java.lang.String">
        SELECT
            padp.point_code
        FROM
            price_apply pa
                LEFT JOIN price_apply_device_point padp ON pa.id = padp.apply_id
        where pa.apply_code = #{applyNumber}
    </select>
</mapper>
