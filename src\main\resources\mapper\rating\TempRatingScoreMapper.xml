<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.rating.dao.TempRatingScoreMapper">
  <resultMap id="BaseResultMap" type="com.coocaa.meht.module.rating.entity.TempRating">
    <!--@mbg.generated-->
    <!--@Table temp_rating_score-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="building_age" jdbcType="VARCHAR" property="buildingAge" />
    <result column="building_age_score" jdbcType="DECIMAL" property="buildingAgeScore" />
    <result column="building_brand" jdbcType="VARCHAR" property="buildingBrand" />
    <result column="building_brand_score" jdbcType="DECIMAL" property="buildingBrandScore" />
    <result column="building_exterior" jdbcType="VARCHAR" property="buildingExterior" />
    <result column="building_exterior_score" jdbcType="DECIMAL" property="buildingExteriorScore" />
    <result column="building_garage" jdbcType="VARCHAR" property="buildingGarage" />
    <result column="building_garage_score" jdbcType="DECIMAL" property="buildingGarageScore" />
    <result column="building_grade" jdbcType="VARCHAR" property="buildingGrade" />
    <result column="building_grade_score" jdbcType="DECIMAL" property="buildingGradeScore" />
    <result column="building_hall" jdbcType="VARCHAR" property="buildingHall" />
    <result column="building_hall_score" jdbcType="DECIMAL" property="buildingHallScore" />
    <result column="building_lobby" jdbcType="VARCHAR" property="buildingLobby" />
    <result column="building_lobby_score" jdbcType="DECIMAL" property="buildingLobbyScore" />
    <result column="building_location" jdbcType="VARCHAR" property="buildingLocation" />
    <result column="building_location_score" jdbcType="DECIMAL" property="buildingLocationScore" />
    <result column="building_name" jdbcType="VARCHAR" property="buildingName" />
    <result column="building_no" jdbcType="VARCHAR" property="buildingNo" />
    <result column="building_number" jdbcType="VARCHAR" property="buildingNumber" />
    <result column="building_number_score" jdbcType="DECIMAL" property="buildingNumberScore" />
    <result column="building_price" jdbcType="VARCHAR" property="buildingPrice" />
    <result column="building_price_score" jdbcType="DECIMAL" property="buildingPriceScore" />
    <result column="building_rating" jdbcType="VARCHAR" property="buildingRating" />
    <result column="building_rating_score" jdbcType="DECIMAL" property="buildingRatingScore" />
    <result column="building_score" jdbcType="DECIMAL" property="buildingScore" />
    <result column="building_settled" jdbcType="VARCHAR" property="buildingSettled" />
    <result column="building_settled_score" jdbcType="DECIMAL" property="buildingSettledScore" />
    <result column="building_type" jdbcType="TINYINT" property="buildingType" />
    <result column="db_building_age" jdbcType="VARCHAR" property="dbBuildingAge" />
    <result column="db_building_age_score" jdbcType="DECIMAL" property="dbBuildingAgeScore" />
    <result column="db_building_brand" jdbcType="VARCHAR" property="dbBuildingBrand" />
    <result column="db_building_brand_score" jdbcType="DECIMAL" property="dbBuildingBrandScore" />
    <result column="db_building_exterior" jdbcType="VARCHAR" property="dbBuildingExterior" />
    <result column="db_building_exterior_score" jdbcType="DECIMAL" property="dbBuildingExteriorScore" />
    <result column="db_building_garage" jdbcType="VARCHAR" property="dbBuildingGarage" />
    <result column="db_building_garage_score" jdbcType="DECIMAL" property="dbBuildingGarageScore" />
    <result column="db_building_grade" jdbcType="VARCHAR" property="dbBuildingGrade" />
    <result column="db_building_grade_score" jdbcType="DECIMAL" property="dbBuildingGradeScore" />
    <result column="db_building_hall" jdbcType="VARCHAR" property="dbBuildingHall" />
    <result column="db_building_hall_score" jdbcType="DECIMAL" property="dbBuildingHallScore" />
    <result column="db_building_lobby" jdbcType="VARCHAR" property="dbBuildingLobby" />
    <result column="db_building_lobby_score" jdbcType="DECIMAL" property="dbBuildingLobbyScore" />
    <result column="db_building_location" jdbcType="VARCHAR" property="dbBuildingLocation" />
    <result column="db_building_location_score" jdbcType="DECIMAL" property="dbBuildingLocationScore" />
    <result column="db_building_number" jdbcType="VARCHAR" property="dbBuildingNumber" />
    <result column="db_building_number_score" jdbcType="DECIMAL" property="dbBuildingNumberScore" />
    <result column="db_building_price" jdbcType="VARCHAR" property="dbBuildingPrice" />
    <result column="db_building_price_score" jdbcType="DECIMAL" property="dbBuildingPriceScore" />
    <result column="db_building_rating" jdbcType="VARCHAR" property="dbBuildingRating" />
    <result column="db_building_rating_score" jdbcType="DECIMAL" property="dbBuildingRatingScore" />
    <result column="db_building_score" jdbcType="DECIMAL" property="dbBuildingScore" />
    <result column="db_building_settled" jdbcType="VARCHAR" property="dbBuildingSettled" />
    <result column="db_building_settled_score" jdbcType="DECIMAL" property="dbBuildingSettledScore" />
    <result column="db_building_type" jdbcType="TINYINT" property="dbBuildingType" />
    <result column="db_project_level" jdbcType="VARCHAR" property="dbProjectLevel" />
    <result column="deepseek_building_age" jdbcType="VARCHAR" property="deepseekBuildingAge" />
    <result column="deepseek_building_age_score" jdbcType="DECIMAL" property="deepseekBuildingAgeScore" />
    <result column="deepseek_building_brand" jdbcType="VARCHAR" property="deepseekBuildingBrand" />
    <result column="deepseek_building_brand_score" jdbcType="DECIMAL" property="deepseekBuildingBrandScore" />
    <result column="deepseek_building_exterior" jdbcType="VARCHAR" property="deepseekBuildingExterior" />
    <result column="deepseek_building_exterior_score" jdbcType="DECIMAL" property="deepseekBuildingExteriorScore" />
    <result column="deepseek_building_garage" jdbcType="VARCHAR" property="deepseekBuildingGarage" />
    <result column="deepseek_building_garage_score" jdbcType="DECIMAL" property="deepseekBuildingGarageScore" />
    <result column="deepseek_building_grade" jdbcType="VARCHAR" property="deepseekBuildingGrade" />
    <result column="deepseek_building_grade_score" jdbcType="DECIMAL" property="deepseekBuildingGradeScore" />
    <result column="deepseek_building_hall" jdbcType="VARCHAR" property="deepseekBuildingHall" />
    <result column="deepseek_building_hall_score" jdbcType="DECIMAL" property="deepseekBuildingHallScore" />
    <result column="deepseek_building_lobby" jdbcType="VARCHAR" property="deepseekBuildingLobby" />
    <result column="deepseek_building_lobby_score" jdbcType="DECIMAL" property="deepseekBuildingLobbyScore" />
    <result column="deepseek_building_location" jdbcType="VARCHAR" property="deepseekBuildingLocation" />
    <result column="deepseek_building_location_score" jdbcType="DECIMAL" property="deepseekBuildingLocationScore" />
    <result column="deepseek_building_number" jdbcType="VARCHAR" property="deepseekBuildingNumber" />
    <result column="deepseek_building_number_score" jdbcType="DECIMAL" property="deepseekBuildingNumberScore" />
    <result column="deepseek_building_price" jdbcType="VARCHAR" property="deepseekBuildingPrice" />
    <result column="deepseek_building_price_score" jdbcType="DECIMAL" property="deepseekBuildingPriceScore" />
    <result column="deepseek_building_rating" jdbcType="VARCHAR" property="deepseekBuildingRating" />
    <result column="deepseek_building_rating_score" jdbcType="DECIMAL" property="deepseekBuildingRatingScore" />
    <result column="deepseek_building_score" jdbcType="DECIMAL" property="deepseekBuildingScore" />
    <result column="deepseek_building_settled" jdbcType="VARCHAR" property="deepseekBuildingSettled" />
    <result column="deepseek_building_settled_score" jdbcType="DECIMAL" property="deepseekBuildingSettledScore" />
    <result column="deepseek_building_type" jdbcType="TINYINT" property="deepseekBuildingType" />
    <result column="deepseek_project_level" jdbcType="VARCHAR" property="deepseekProjectLevel" />
    <result column="map_address" jdbcType="VARCHAR" property="mapAddress" />
    <result column="map_city" jdbcType="VARCHAR" property="mapCity" />
    <result column="map_no" jdbcType="VARCHAR" property="mapNo" />
    <result column="map_province" jdbcType="VARCHAR" property="mapProvince" />
    <result column="map_region" jdbcType="VARCHAR" property="mapRegion" />
    <result column="project_level" jdbcType="VARCHAR" property="projectLevel" />
    <result column="qw_building_age" jdbcType="VARCHAR" property="qwBuildingAge" />
    <result column="qw_building_age_score" jdbcType="DECIMAL" property="qwBuildingAgeScore" />
    <result column="qw_building_brand" jdbcType="VARCHAR" property="qwBuildingBrand" />
    <result column="qw_building_brand_score" jdbcType="DECIMAL" property="qwBuildingBrandScore" />
    <result column="qw_building_exterior" jdbcType="VARCHAR" property="qwBuildingExterior" />
    <result column="qw_building_exterior_score" jdbcType="DECIMAL" property="qwBuildingExteriorScore" />
    <result column="qw_building_garage" jdbcType="VARCHAR" property="qwBuildingGarage" />
    <result column="qw_building_garage_score" jdbcType="DECIMAL" property="qwBuildingGarageScore" />
    <result column="qw_building_grade" jdbcType="VARCHAR" property="qwBuildingGrade" />
    <result column="qw_building_grade_score" jdbcType="DECIMAL" property="qwBuildingGradeScore" />
    <result column="qw_building_hall" jdbcType="VARCHAR" property="qwBuildingHall" />
    <result column="qw_building_hall_score" jdbcType="DECIMAL" property="qwBuildingHallScore" />
    <result column="qw_building_lobby" jdbcType="VARCHAR" property="qwBuildingLobby" />
    <result column="qw_building_lobby_score" jdbcType="DECIMAL" property="qwBuildingLobbyScore" />
    <result column="qw_building_location" jdbcType="VARCHAR" property="qwBuildingLocation" />
    <result column="qw_building_location_score" jdbcType="DECIMAL" property="qwBuildingLocationScore" />
    <result column="qw_building_number" jdbcType="VARCHAR" property="qwBuildingNumber" />
    <result column="qw_building_number_score" jdbcType="DECIMAL" property="qwBuildingNumberScore" />
    <result column="qw_building_price" jdbcType="VARCHAR" property="qwBuildingPrice" />
    <result column="qw_building_price_score" jdbcType="DECIMAL" property="qwBuildingPriceScore" />
    <result column="qw_building_rating" jdbcType="VARCHAR" property="qwBuildingRating" />
    <result column="qw_building_rating_score" jdbcType="DECIMAL" property="qwBuildingRatingScore" />
    <result column="qw_building_score" jdbcType="DECIMAL" property="qwBuildingScore" />
    <result column="qw_building_settled" jdbcType="VARCHAR" property="qwBuildingSettled" />
    <result column="qw_building_settled_score" jdbcType="DECIMAL" property="qwBuildingSettledScore" />
    <result column="qw_building_type" jdbcType="TINYINT" property="qwBuildingType" />
    <result column="qw_project_level" jdbcType="VARCHAR" property="qwProjectLevel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, building_age, building_age_score, building_brand, building_brand_score, building_exterior, 
    building_exterior_score, building_garage, building_garage_score, building_grade, 
    building_grade_score, building_hall, building_hall_score, building_lobby, building_lobby_score, 
    building_location, building_location_score, building_name, building_no, building_number, 
    building_number_score, building_price, building_price_score, building_rating, building_rating_score, 
    building_score, building_settled, building_settled_score, building_type, db_building_age, 
    db_building_age_score, db_building_brand, db_building_brand_score, db_building_exterior, 
    db_building_exterior_score, db_building_garage, db_building_garage_score, db_building_grade, 
    db_building_grade_score, db_building_hall, db_building_hall_score, db_building_lobby, 
    db_building_lobby_score, db_building_location, db_building_location_score, db_building_number, 
    db_building_number_score, db_building_price, db_building_price_score, db_building_rating, 
    db_building_rating_score, db_building_score, db_building_settled, db_building_settled_score, 
    db_building_type, db_project_level, deepseek_building_age, deepseek_building_age_score, 
    deepseek_building_brand, deepseek_building_brand_score, deepseek_building_exterior, 
    deepseek_building_exterior_score, deepseek_building_garage, deepseek_building_garage_score, 
    deepseek_building_grade, deepseek_building_grade_score, deepseek_building_hall, deepseek_building_hall_score, 
    deepseek_building_lobby, deepseek_building_lobby_score, deepseek_building_location, 
    deepseek_building_location_score, deepseek_building_number, deepseek_building_number_score, 
    deepseek_building_price, deepseek_building_price_score, deepseek_building_rating, 
    deepseek_building_rating_score, deepseek_building_score, deepseek_building_settled, 
    deepseek_building_settled_score, deepseek_building_type, deepseek_project_level, 
    map_address, map_city, map_no, map_province, map_region, project_level, qw_building_age, 
    qw_building_age_score, qw_building_brand, qw_building_brand_score, qw_building_exterior, 
    qw_building_exterior_score, qw_building_garage, qw_building_garage_score, qw_building_grade, 
    qw_building_grade_score, qw_building_hall, qw_building_hall_score, qw_building_lobby, 
    qw_building_lobby_score, qw_building_location, qw_building_location_score, qw_building_number, 
    qw_building_number_score, qw_building_price, qw_building_price_score, qw_building_rating, 
    qw_building_rating_score, qw_building_score, qw_building_settled, qw_building_settled_score, 
    qw_building_type, qw_project_level
  </sql>
</mapper>