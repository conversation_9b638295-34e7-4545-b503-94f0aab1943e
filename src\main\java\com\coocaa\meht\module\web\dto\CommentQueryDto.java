package com.coocaa.meht.module.web.dto;


import com.coocaa.meht.common.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @version 1.0
 * @description 评论查询请求
 * @since 2025-04-29
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class CommentQueryDto extends PageReq {
    
    /**
     * 业务类型（1楼宇、2价格申请）
     */
    @Schema(description = "业务类型（1楼宇、2价格申请）")
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;
    
    /**
     * 业务ID
     */
    @NotBlank(message = "业务ID不能为空")
    @Schema(description = "业务ID")
    private String businessId;
}