package com.coocaa.meht.module.web.vo.kanban;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 指标
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-26
 */
@Data
@Accessors(chain = true)
public class CityStatisticsItemVO {
    @Schema(description = "城市名称", type = "number", example = "成都市")
    private String cityName;
    @Schema(description = "最近3天楼宇认证通过转化率", type = "list")
    private List<DataItemVO> threeDaysBuildingData;
    @Schema(description = "最近7天楼宇认证通过转化率", type = "list")
    private List<DataItemVO> sevenDaysBuildingData;
    @Schema(description = "最近7天项目(商机)转化率", type = "list")
    private List<DataItemVO> sevenDaysProjectData;
    @Schema(description = "最近7天项目(商机)转化率", type = "list")
    private List<DataItemVO> fourteenDaysProjectData;
}
