package com.coocaa.meht.module.web.dto.point;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @program: cheese-meht-web-api
 * @ClassName PointVO
 * @description:
 * @author: zhangbinxian
 * @create: 2025-01-16 17:34
 * @Version 1.0
 * todo 后期删除
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PointVO {
    private Integer id;
    private Integer waitingHallId;
    private String code;
    private String pointStatus;
    private String remark;
    private String deviceSize;
    private String createBy;
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime createTime;
    private String updateBy;
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime updateTime;
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime expireTime;
    private String buildingRatingNo;
    private Integer city;
    private Integer number;
    private String businessCode;
}
