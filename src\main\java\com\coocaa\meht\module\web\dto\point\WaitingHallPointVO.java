package com.coocaa.meht.module.web.dto.point;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: cheese-meht-web-api
 * @ClassName WaitingHallPointLitsVO
 * @description:
 * @author: zhangbinxian
 * @create: 2025-01-16 17:30
 * @Version 1.0
 * todo 后期删除
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WaitingHallPointVO {

    private List<WaitingHallVO> waitingHallVOS;

    private List<PointVO> pointVOS;

}
