package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.module.web.dao.HighSeaRecordMapper;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.HighSeaRecordEntity;
import com.coocaa.meht.module.web.service.HighSeaRecordService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-15
 */
@Service
public class HighSeaRecordServiceImpl extends ServiceImpl<HighSeaRecordMapper, HighSeaRecordEntity> implements HighSeaRecordService {

    @Override
    public void record(LocalDateTime operateTime, String operateUserCode, String remark, BuildingRatingEntity.HighSeaFlagEnum highSeaFlag, List<BuildingRatingEntity> buildingRatings) {
        if (CollUtil.isEmpty(buildingRatings)) {
            return;
        }

        List<HighSeaRecordEntity> entities = buildingRatings.stream()
                .map(buildingRatingEntity -> HighSeaRecordEntity.builder()
                        .buildingNo(buildingRatingEntity.getBuildingNo())
                        .buildingName(buildingRatingEntity.getBuildingName())
                        .operateType(highSeaFlag.getCode())
                        .operateTime(operateTime)
                        .responsiblePerson(buildingRatingEntity.getSubmitUser())
                        .operator(operateUserCode)
                        .remark(remark)
                        .build())
                .toList();

        saveBatch(entities);
    }

}
