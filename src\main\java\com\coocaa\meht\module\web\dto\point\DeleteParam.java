package com.coocaa.meht.module.web.dto.point;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
public class DeleteParam {

    @Schema(description = "楼宇编码")
    @NotBlank(message = "楼宇编码不能为空")
    private String buildingRatingNo;

    @Schema(description = "楼栋名称，修改单元，楼层的时候传")
    private String buildingName;
    @Schema(description = "单元名称,修改楼层的时候传")
    private String unitName;

    @Schema(description = "楼层传字典")
    private String floorName;

    @Schema(description = "节点类型: building/unit/floor/waitingHall/point")
    private String type;

    @Schema(description = "等候厅id，删除等候厅的时候传这个")
    private Integer waitingHallId;

    @Schema(description = "点位id，删除点位的时候传这个")
    private Integer pointId;

    @Schema(description = "商机编码，删除点位的时候传这个")
    private String businessCode;

    @Schema(description = "等候厅id")
    private List<Integer> waitingHallIds;
}
