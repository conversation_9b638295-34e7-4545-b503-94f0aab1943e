package com.coocaa.meht.rpc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/24
 */
@Data
public class CityVO {

    /**
     * 城市Id
     */
    private Integer id;

    /**
     * 业务负责人id
     */
    private Integer businessHead;

    /**
     * 业务负责人名称
     */
    private String businessHeadName;

    /**
     * 法务bp Id
     */
    private Integer legalBp;

    /**
     * 法务bp名称
     */
    private String legalBpName;

    /**
     * 财务bp Id
     */
    private Integer financeBp;

    /**
     * 财务bp名称
     */
    private String financeBpName;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 城市名称
     */
    private String name;

}
