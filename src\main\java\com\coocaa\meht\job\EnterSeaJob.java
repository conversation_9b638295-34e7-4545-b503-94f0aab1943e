package com.coocaa.meht.job;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.module.building.param.TransferAndDropParam;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.HighSeaCustomerService;
import com.coocaa.meht.module.web.service.HighSeaRecordService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.utils.MessageSendUtil;
import com.google.common.collect.Lists;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 客户自动入公海定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
@Slf4j
@Component
@RefreshScope
public class EnterSeaJob {

    @Autowired
    private BuildingRatingService buildingRatingService;

    @Autowired
    private IBuildingMetaService buildingMetaService;

    @Autowired
    private BusinessOpportunityService businessOpportunityService;

    @Autowired
    private HighSeaRecordService highSeaRecordService;

    @Autowired
    private HighSeaCustomerService highSeaCustomerService;

    @Autowired
    private FeignCmsRpc feignCmsRpc;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;

    @Autowired
    private MessageSendUtil messageSendUtil;

    @Autowired
    private CompleteRatingService completeRatingService;

    /**
     * 楼宇评级提交后入公海天数阈值
     */
    @Value("${high-sea.enter.threshold.submit:5}")
    public Integer submitThreshold;

    /**
     * 商机初步洽谈后入公海天数阈值
     */
    @Value("${high-sea.enter.threshold.preliminary-negotiations:15}")
    public Integer preliminaryNegotiationsThreshold;

    /**
     * 商机达成意向后入公海天数阈值
     */
    @Value("${high-sea.enter.threshold.reaching-intention:7}")
    public Integer reachingIntentionThreshold;

    /**
     * 客户数据入公海提前提醒天数
     */
    @Value("${high-sea.enter.warn-days:1,3,5}")
    public List<Integer> warnDays;

    /**
     * 忽略自动入公海的商机状态
     */
    private static final Set<String> IGNORE_STATUS = Set.of(BusinessChangeStatusEnum.CONTRACT_PHASE.getCode(),
            BusinessChangeStatusEnum.DEAL.getCode());

    /**
     * 按达成意向时间处理的商机状态
     */
    private static final Set<String> REACHING_INTENTION_STATUS = Set.of(BusinessChangeStatusEnum.REACHING_INTENTION.getCode(),
            BusinessChangeStatusEnum.PROPOSAL_QUOTATION.getCode());

    /**
     * 按初步洽谈时间处理的商机状态
     */
    private static final Set<String> PRELIMINARY_NEGOTIATIONS_STATUS = Set.of(BusinessChangeStatusEnum.PRELIMINARY_NEGOTIATIONS.getCode());

    /**
     * 客户状态
     */
    public static final List<Integer> CUSTOMER_STATUS = List.of(BuildingRatingEntity.Status.WAIT_AUDIT.getValue(),
            BuildingRatingEntity.Status.AUDITED.getValue(), BuildingRatingEntity.Status.REJECTED.getValue());

    /**
     * 系统触发
     */
    private static final String REASON_SYSTEM_TRIGGERED = "系统触发";
    private static final String OPERATOR_SYSTEM = "系统";

    private static final String ENTER_SEA_MSG = "系统已经将客户%s放入公海";

    /**
     * 客户数据入公海定时任务
     */
    @XxlJob("enterSea")
    public void enterSea() {
        log.info("自动入公海定时任务开始");
        LocalDateTime now = LocalDateTime.now();
        // 分批查询
        Page<BuildingRatingEntity> page = new Page<>();
        page.setSize(500);
        int pageNo = 1;
        List<BuildingRatingEntity> entities;
        // 待入公海数据
        List<BuildingRatingEntity> needEnterEntities = new ArrayList<>(5000);
        // 需要更新或置空入公海时间数据
        List<BuildingRatingEntity> needUpdateEntities = new ArrayList<>(5000);
        // 需要置空入公海时间数据id集合
        List<Long> neverEnterIds = new ArrayList<>(5000);
        // 倒计时提醒消息模板与提醒客户映射
        Map<String, List<BuildingRatingEntity>> warningMapping = Maps.newHashMapWithExpectedSize(warnDays.size());
        do {
            page.setCurrent(pageNo++);
            entities = buildingRatingService.lambdaQuery()
                    .select(BuildingRatingEntity::getId,
                            BuildingRatingEntity::getBuildingNo,
                            BuildingRatingEntity::getBuildingName,
                            BuildingRatingEntity::getSubmitUser,
                            BuildingRatingEntity::getSubmitTime,
                            BuildingRatingEntity::getEnterSeaTime,
                            BuildingRatingEntity::getEnterSeaCalculateTime)
                    .eq(BuildingRatingEntity::getHighSeaFlag, BuildingRatingEntity.HighSeaFlagEnum.NO.getCode())
                    .in(BuildingRatingEntity::getStatus, CUSTOMER_STATUS)
                    .orderByAsc(BuildingRatingEntity::getId)
                    .page(page)
                    .getRecords();

            if (CollUtil.isNotEmpty(entities)) {
                // 数据检查判断是否需要更新或入公海
                check(now, neverEnterIds, entities, needEnterEntities, needUpdateEntities, warningMapping);
            }
        } while (CollUtil.isNotEmpty(entities));

        // 更新入公海时间
        if (CollUtil.isNotEmpty(needUpdateEntities)) {
            buildingRatingService.updateBatchById(needUpdateEntities);
            log.info("更新入公海时间客户数{}", needUpdateEntities.size());
        }

        // 置空入公海时间
        if (CollUtil.isNotEmpty(neverEnterIds)) {
            LambdaUpdateWrapper<BuildingRatingEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(BuildingRatingEntity::getId, neverEnterIds);
            wrapper.set(BuildingRatingEntity::getEnterSeaTime, null);
            buildingRatingService.update(wrapper);
            log.info("置空入公海时间客户{}", JSON.toJSONString(neverEnterIds));
        }

        // 发送倒计时提醒消息
        if (CollUtil.isNotEmpty(warningMapping)) {
            warningMapping.forEach((key, value) -> highSeaCustomerService.sendMessage(key, value));
        }

        if (CollUtil.isEmpty(needEnterEntities)) {
            log.info("没有需要入公海的客户");
            return;
        }

        log.info("需要入公海客户数{}", needEnterEntities.size());

        List<List<BuildingRatingEntity>> partitions = Lists.partition(needEnterEntities, 500);
        for (List<BuildingRatingEntity> subs : partitions) {
            // 开启事务
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
            TransactionStatus transaction = transactionManager.getTransaction(def);
            try {
                doEnter(subs, now);
                transactionManager.commit(transaction);
                log.info("客户入公海成功，本批次数据量{}", subs.size());
            } catch (Exception e) {
                log.error("客户入公海异常，数据回滚, 本批次数据量{}", subs.size(), e);
                transactionManager.rollback(transaction);
                continue;
            }

            // 发送入公海消息
            highSeaCustomerService.sendMessage(ENTER_SEA_MSG, subs);
        }

        log.info("自动入公海定时任务结束");
    }

    private void check(LocalDateTime now, List<Long> neverEnterIds, List<BuildingRatingEntity> buildingRatings, List<BuildingRatingEntity> needEnterEntities,
                       List<BuildingRatingEntity> needUpdateEntities, Map<String, List<BuildingRatingEntity>> warningMapping) {

        for (BuildingRatingEntity buildingRating : buildingRatings) {
            List<BusinessOpportunityEntity> businessOpportunityEntities = businessOpportunityService.lambdaQuery()
                    .select(BusinessOpportunityEntity::getId)
                    .select(BusinessOpportunityEntity::getStatus)
                    .eq(BusinessOpportunityEntity::getBuildingNo, buildingRating.getBuildingNo())
                    .list();

            if (CollUtil.isEmpty(businessOpportunityEntities)) {
                // 商机为空直接掉公海
                needEnterEntities.add(buildingRating);
                log.info("客户({} {} {})商机为空准备自动入公海",
                        buildingRating.getBuildingNo(), buildingRating.getBuildingName(), buildingRating.getSubmitUser());
                continue;
            }

            Set<Integer> businessIds = new HashSet<>(businessOpportunityEntities.size());
            Set<String> status = businessOpportunityEntities.stream()
                    .peek(entity -> businessIds.add(entity.getId()))
                    .map(BusinessOpportunityEntity::getStatus)
                    .collect(Collectors.toSet());

            log.info("客户({} {})商机状态：{}", buildingRating.getBuildingNo(), buildingRating.getBuildingName(), status);

            // 有合同状态及以后的商机，或有前面有进入过合同流程的商机，忽略入公海
            if (CollUtil.containsAny(status, IGNORE_STATUS) || businessOpportunityService.isContractedBefore(businessIds)) {
                if (Objects.nonNull(buildingRating.getEnterSeaTime())) {
                    // 入公海时间置空
                    neverEnterIds.add(buildingRating.getId());
                    log.info("客户({} {})入公海时间将置空", buildingRating.getBuildingNo(), buildingRating.getBuildingName());
                }
                log.info("客户({} {})存在合同流程及以后或前面有进入过合同流程的商机，忽略", buildingRating.getBuildingNo(), buildingRating.getBuildingName());
                continue;
            }

            // 按商机最早达成意向时间判断入公海
            if (CollUtil.containsAny(status, REACHING_INTENTION_STATUS)) {
                LocalDateTime changeTime = businessOpportunityService.getEarliestChangeTime(
                        buildingRating.getBuildingNo(), BusinessChangeStatusEnum.REACHING_INTENTION.getCode());
                log.info("客户({} {})按商机最早达成意向时间({})判断入公海", buildingRating.getBuildingNo(), buildingRating.getBuildingName(), changeTime);
                if (isEnterSea(reachingIntentionThreshold, changeTime, now, buildingRating, needUpdateEntities, warningMapping)) {
                    needEnterEntities.add(buildingRating);
                }
                continue;
            }

            // 按商机最早初步洽谈时间判断入公海
            if (CollUtil.containsAny(status, PRELIMINARY_NEGOTIATIONS_STATUS)) {
                LocalDateTime changeTime = businessOpportunityService.getEarliestChangeTime(
                        buildingRating.getBuildingNo(), BusinessChangeStatusEnum.PRELIMINARY_NEGOTIATIONS.getCode());
                log.info("客户({} {})按商机最早初步洽谈时间({})判断入公海", buildingRating.getBuildingNo(), buildingRating.getBuildingName(), changeTime);
                if (isEnterSea(preliminaryNegotiationsThreshold, changeTime, now, buildingRating, needUpdateEntities, warningMapping)) {
                    needEnterEntities.add(buildingRating);
                }
                continue;
            }

            // 按楼宇提交时间判断入公海
            log.info("客户({} {})按楼宇提交时间({})判断入公海", buildingRating.getBuildingNo(), buildingRating.getBuildingName(), buildingRating.getSubmitTime());
            if (isEnterSea(submitThreshold, buildingRating.getSubmitTime(), now, buildingRating, needUpdateEntities, warningMapping)) {
                needEnterEntities.add(buildingRating);
            }
        }
    }

    private boolean isEnterSea(Integer threshold, LocalDateTime changeTime, LocalDateTime now, BuildingRatingEntity buildingRating,
                               List<BuildingRatingEntity> needUpdateEntities, Map<String, List<BuildingRatingEntity>> warningMapping) {
        LocalDateTime calculateTime = getCalculateTime(buildingRating, changeTime);
        if (Objects.isNull(calculateTime)) {
            return false;
        }

        // 实际入公海时间
        LocalDateTime enterSeaTime = calculateTime.plusDays(threshold);
        log.info("客户({} {})自动入公海阈值{}天，入公海计算起始时间{}，入公海时间{}",
                buildingRating.getBuildingNo(), buildingRating.getBuildingName(), threshold, calculateTime, enterSeaTime);

        // 入公海时间精确到小时来比较
        LocalDateTime compareTime = enterSeaTime.withMinute(0).withSecond(0).withNano(0);

        // 需要入公海客户
        if (compareTime.isBefore(now)) {
            log.info("客户({} {} {})准备自动入公海",
                    buildingRating.getBuildingNo(), buildingRating.getBuildingName(), buildingRating.getSubmitUser());
            return true;
        }

        // 未到阈值，不入公海客户
        log.info("客户({} {})未到自动入公海阈值{}天，忽略",
                buildingRating.getBuildingNo(), buildingRating.getBuildingName(), threshold);

        if (Objects.isNull(buildingRating.getEnterSeaTime())
                || !enterSeaTime.equals(buildingRating.getEnterSeaTime())) {
            // 需要刷新入公海时间
            buildingRating.setEnterSeaTime(enterSeaTime);
            needUpdateEntities.add(buildingRating);
            log.info("客户({} {})入公海时间将更新为{}", buildingRating.getBuildingNo(), buildingRating.getBuildingName(), enterSeaTime);
        }

        Duration duration = Duration.between(now.withMinute(0).withSecond(0).withNano(0), compareTime);
        // 天数差
        long days = duration.toDays();
        log.info("客户({} {})距离入公海还剩{}天", buildingRating.getBuildingNo(), buildingRating.getBuildingName(), days);
        if (warnDays.contains(Long.valueOf(days).intValue())) {
            // 客户掉入公海前指定时间，需要发送消息提醒
            List<BuildingRatingEntity> entities = warningMapping.computeIfAbsent("系统将在" + days + "天后把客户%s放入公海", k -> new ArrayList<>());
            entities.add(buildingRating);
        }
        return false;
    }

    private void doEnter(List<BuildingRatingEntity> buildingRatings, LocalDateTime now) {
        if (CollUtil.isEmpty(buildingRatings)) {
            return;
        }

        List<String> buildingNos = buildingRatings.stream().map(BuildingRatingEntity::getBuildingNo).toList();
        // 入公海
        buildingRatingService.lambdaUpdate()
                .set(BuildingRatingEntity::getHighSeaFlag, BuildingRatingEntity.HighSeaFlagEnum.YES.getCode())
                .set(BuildingRatingEntity::getSubmitUser, "")
                .set(BuildingRatingEntity::getEnterSeaTime, now)
                .set(BuildingRatingEntity::getEnterSeaReason, REASON_SYSTEM_TRIGGERED)
                .in(BuildingRatingEntity::getBuildingNo, buildingNos)
                .update();

        buildingMetaService.lambdaUpdate()
                .set(BuildingMetaEntity::getManager, "")
                .in(BuildingMetaEntity::getBuildingRatingNo, buildingNos)
                .update();

        businessOpportunityService.lambdaUpdate()
                .set(BusinessOpportunityEntity::getSubmitUser, "")
                .set(BusinessOpportunityEntity::getOwner, "")
                .in(BusinessOpportunityEntity::getBuildingNo, buildingNos)
                .update();

        // 完善评级记录入公海
        completeRatingService.transferAndDrop(new TransferAndDropParam(buildingNos, ""));

        // 入公海记录
        highSeaRecordService.record(now, OPERATOR_SYSTEM, REASON_SYSTEM_TRIGGERED, BuildingRatingEntity.HighSeaFlagEnum.YES, buildingRatings);
    }

    /**
     * 查询楼宇自动入公海的阈值和计算起始时间
     */
    public Map<Integer, LocalDateTime> getThreshold(BuildingRatingEntity buildingRating) {
        List<BusinessOpportunityEntity> businessOpportunityEntities = businessOpportunityService.lambdaQuery()
                .select(BusinessOpportunityEntity::getId,
                        BusinessOpportunityEntity::getStatus)
                .eq(BusinessOpportunityEntity::getBuildingNo, buildingRating.getBuildingNo())
                .list();

        log.info("客户({} {})查询商机{}", buildingRating.getBuildingNo(), buildingRating.getBuildingName(), JSON.toJSONString(businessOpportunityEntities));

        if (CollUtil.isEmpty(businessOpportunityEntities)) {
            // 没有商机，默认阈值为1天
            return Map.of(1, LocalDateTime.now());
        }

        Set<Integer> businessIds = new HashSet<>(businessOpportunityEntities.size());
        Set<String> status = businessOpportunityEntities.stream()
                .peek(entity -> businessIds.add(entity.getId()))
                .map(BusinessOpportunityEntity::getStatus)
                .collect(Collectors.toSet());

        log.info("客户({} {})查询商机状态{}, 商机id{}", buildingRating.getBuildingNo(), buildingRating.getBuildingName(),
                JSON.toJSONString(status), JSON.toJSONString(businessIds));

        if (CollUtil.containsAny(status, IGNORE_STATUS) || businessOpportunityService.isContractedBefore(businessIds)) {
            return null;
        }

        LocalDateTime changeTime;
        Integer threshold;

        if (CollUtil.containsAny(status, REACHING_INTENTION_STATUS)) {
            // 按商机最早达成意向时间判断入公海
            changeTime = businessOpportunityService.getEarliestChangeTime(
                    buildingRating.getBuildingNo(), BusinessChangeStatusEnum.REACHING_INTENTION.getCode());
            threshold = reachingIntentionThreshold;
        } else if (CollUtil.containsAny(status, PRELIMINARY_NEGOTIATIONS_STATUS)) {
            // 按商机最早初步洽谈时间判断入公海
            changeTime = businessOpportunityService.getEarliestChangeTime(
                    buildingRating.getBuildingNo(), BusinessChangeStatusEnum.PRELIMINARY_NEGOTIATIONS.getCode());
            threshold = preliminaryNegotiationsThreshold;
        } else {
            // 只有待洽谈商机，按楼宇提交时间计算入公海时间
            changeTime = buildingRating.getSubmitTime();
            threshold = submitThreshold;
        }

        LocalDateTime calculateTime = getCalculateTime(buildingRating, changeTime);
        if (Objects.isNull(calculateTime)) {
            return null;
        }

        return Map.of(threshold, calculateTime);
    }

    private LocalDateTime getCalculateTime(BuildingRatingEntity buildingRating, LocalDateTime changeTime) {
        if (Objects.isNull(changeTime) && Objects.isNull(buildingRating.getEnterSeaCalculateTime())) {
            log.info("客户({} {})自动入公海计算起始时间为空，忽略", buildingRating.getBuildingNo(), buildingRating.getBuildingName());
            return null;
        }

        if (Objects.isNull(changeTime)) {
            return buildingRating.getEnterSeaCalculateTime();
        }

        // 都不为空，取最新的时间
        return Objects.isNull(buildingRating.getEnterSeaCalculateTime())
                ? changeTime : changeTime.isAfter(buildingRating.getEnterSeaCalculateTime())
                ? changeTime : buildingRating.getEnterSeaCalculateTime();
    }

}
