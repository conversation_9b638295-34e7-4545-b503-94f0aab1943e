<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.web.dao.PointMapper">

    <select id="listBuildingPoint" resultType="com.coocaa.meht.module.web.dto.point.PointDetail">
        SELECT
            IFNULL(p.code, '') as pointCode,
            IFNULL(p.remark, '') as pointRemark,
            IFNULL(p.device_size, '') as deviceSize,
            IFNULL(p.id, null) as pointId,
            IFNULL(p.create_time, NULL) as pointCreateTime,
            IFNULL(p.expire_time, NULL) as expireTime
        FROM point p
        WHERE p.building_rating_no = #{buildingNo}
        order by p.create_time asc
    </select>


    <select id="listBuildingPointCount" resultType="com.coocaa.meht.module.web.dto.point.ProjectPointCountVO">
        SELECT
            wh.building_rating_no AS buildingRatingNo,
            wh.building_name AS buildingName,
            wh.unit_name AS unitName,
            wh.floor AS floor,
            wh.waiting_hall_name AS waitingHallName,
            wh.id AS waitingHallId,
            IFNULL(COUNT(p.id),0) number
        FROM
            waiting_hall wh
                LEFT JOIN point p ON wh.id = p.waiting_hall_id
        WHERE wh.building_rating_no = #{buildingNo}
        GROUP BY wh.building_rating_no,
                 wh.building_name,
                 wh.unit_name,
                 wh.floor,
                 wh.waiting_hall_name,
                 wh.id
        ORDER BY
            number DESC
    </select>




    <select id="listPriceApplyPoint" resultType="com.coocaa.meht.module.web.dto.point.PointDetail">
        SELECT
            wh.building_rating_no as buildingRatingNo,
            wh.building_name as buildingName,
            wh.unit_name as unitName,
            wh.floor as floor,
            wh.waiting_hall_name as waitingHallName,
            wh.waiting_hall_type as waitingHallType,
            wh.id as waitingHallId,
            IFNULL(p.code, '') as pointCode,
            IFNULL(p.remark, '') as pointRemark,
            IFNULL(p.device_size, '') as deviceSize,
            IFNULL(p.id, null) as pointId,
            IFNULL(p.create_time, NULL) as pointCreateTime
        FROM waiting_hall wh
        LEFT JOIN point p ON wh.id = p.waiting_hall_id
        WHERE
            p.code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        order by p.create_time asc
    </select>



    <select id="getWaitingHallPointInformation" resultType="string">
        SELECT padp.point_code
        FROM
            price_apply_device_point padp
        WHERE
            padp.price_apply_device_id in
            <foreach collection="list" item="item" separator="," close=")" open="(">
            #{item}
            </foreach>
    </select>


    <select id="listPointToContract" resultType="com.coocaa.meht.module.web.dto.PointDetailDto">
        SELECT
            wh.building_rating_no as buildingRatingNo,
            wh.building_name as buildingName,
            wh.unit_name as unitName,
            wh.floor as floor,
            wh.waiting_hall_name as waitingHallName,
            wh.waiting_hall_type as waitingHallType,
            wh.id as waitingHallId,
            IFNULL(p.code, '') as pointCode,
            IFNULL(p.remark, '') as pointRemark,
            IFNULL(p.device_size, '') as deviceSize,
            IFNULL(p.id, 0) as pointId,
            IFNULL(p.create_time, NULL) as pointCreateTime
        FROM waiting_hall wh
                 INNER JOIN point p ON wh.id = p.waiting_hall_id
        WHERE wh.building_rating_no = #{buildingNo}
    </select>

    <select id="listByBusinessCodePoint" resultType="com.coocaa.meht.module.web.dto.point.PointDetail">
        SELECT
        IFNULL(p.code, '') as pointCode,
        IFNULL(p.device_size, '') as deviceSize,
        IFNULL(p.id, null) as pointId,
        IFNULL(p.create_time, NULL) as pointCreateTime,
        IFNULL(p.expire_time, NULL) as expireTime,
        p.business_code as businessCode
        FROM point p
        WHERE p.business_code = #{BusinessCode}
        order by p.create_time asc
    </select>


    <select id="getAllByBusinessCodeStrings" resultType="java.lang.String">
        SELECT building_no FROM business_opportunity WHERE  building_no = `code`
    </select>

</mapper>