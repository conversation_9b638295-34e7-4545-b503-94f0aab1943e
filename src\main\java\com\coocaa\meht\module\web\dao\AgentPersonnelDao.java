package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.web.entity.AgentPersonnelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-11-08 15:19
 */
@Mapper
public interface AgentPersonnelDao extends BaseMapper<AgentPersonnelEntity> {

    AgentPersonnelEntity getAgentPersonnelByCode(@Param("empCode") String empCode);

    List<AgentPersonnelEntity> getAgentPersonnelsByCodes(@Param("empCodes") List<String> empCodes);
}
