package com.coocaa.meht.module.web.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.common.constants.TopicConstants;
import com.coocaa.meht.kafka.KafkaProducerService;
import com.coocaa.meht.module.web.dao.BusinessOpportunityMapper;
import com.coocaa.meht.module.web.dto.BusinessOpportunityWithRatingDto;
import com.coocaa.meht.module.web.dto.BusinessProjectDto;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.CustomerFollowRecordEntity;
import com.coocaa.meht.module.web.entity.PointPlanEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.enums.FollowTypeEnum;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.CustomerFollowRecordService;
import com.coocaa.meht.module.web.service.IBuildingStatusChangeLogService;
import com.coocaa.meht.module.web.service.PointPlanService;
import com.coocaa.meht.module.web.vo.BusinessStatusChangeVO;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 商机
 */
@Service
public class BusinessOpportunityServiceImpl extends ServiceImpl<BusinessOpportunityMapper, BusinessOpportunityEntity> implements BusinessOpportunityService {

    @Autowired
    private BusinessOpportunityMapper businessOpportunityMapper;

    @Autowired
    private CustomerFollowRecordService customerFollowRecordService;

    @Autowired
    private IBuildingStatusChangeLogService changeLogService;

    @Autowired
    private PointPlanService pointPlanService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Override
    public List<BusinessProjectDto> getPriceApplyBusiness(String name, Boolean allUser, List<String> status, String userCode) {
        return businessOpportunityMapper.getPriceApplyBusiness(name, allUser, status, userCode);
    }

    @Override
    public List<BusinessOpportunityWithRatingDto> getBusinessOpportunityWithRatingDtoList(Map<String, LocalDateTime> timeRangeMap) {
        return businessOpportunityMapper.getBusinessOpportunityWithRatingDtoList(timeRangeMap);
    }

    @Override
    public void updateFollowRecord(String buildingNo) {
        List<String> list = this.lambdaQuery()
                .eq(BusinessOpportunityEntity::getBuildingNo, buildingNo)
                .list().stream().map(BusinessOpportunityEntity::getCode).toList();
        if (CollectionUtil.isNotEmpty(list)) {
            customerFollowRecordService.lambdaUpdate()
                    .set(CustomerFollowRecordEntity::getValid, 0)
                    .in(CustomerFollowRecordEntity::getBusinessCode, list)
                    .update();
        }
    }

    @Override
    public LocalDateTime getEarliestChangeTime(String buildingNo, String status) {
        return businessOpportunityMapper.getEarliestChangeTime(buildingNo, status);
    }

    @Override
    public boolean isContractedBefore(Collection<Integer> businessIds) {
        if (CollUtil.isEmpty(businessIds)) {
            return false;
        }

        return changeLogService.lambdaQuery()
                .eq(BuildingStatusChangeLogEntity::getType, BuildingStatusChangeLogEntity.BizType.BUSINESS.getCode())
                .in(BuildingStatusChangeLogEntity::getBizId, businessIds)
                .eq(BuildingStatusChangeLogEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(BuildingStatusChangeLogEntity::getStatus,
                        BusinessChangeStatusEnum.CONTRACT_PHASE.getCode(), BusinessChangeStatusEnum.DEAL.getCode())
                .count() > 0;
    }

    @Override
    public void recover(List<BusinessOpportunityEntity> businessEntities) {
        if (CollUtil.isEmpty(businessEntities)) {
            return;
        }

        Set<String> businessCodes = businessEntities.stream()
                .map(BusinessOpportunityEntity::getCode)
                .collect(Collectors.toSet());

        // 跟进
        Set<String> followRecords = customerFollowRecordService.lambdaQuery()
                .select(CustomerFollowRecordEntity::getBusinessCode)
                .in(CustomerFollowRecordEntity::getBusinessCode, businessCodes)
                .list()
                .stream()
                .map(CustomerFollowRecordEntity::getBusinessCode)
                .collect(Collectors.toSet());

        // 点位方案
        Set<String> pointPlans = pointPlanService.lambdaQuery()
                .select(PointPlanEntity::getBusinessCode)
                .in(PointPlanEntity::getBusinessCode, businessCodes)
                .list()
                .stream()
                .map(PointPlanEntity::getBusinessCode)
                .collect(Collectors.toSet());

        // 恢复商机状态
        businessEntities.forEach(businessEntity -> {
            if (pointPlans.contains(businessEntity.getCode())) {
                // 包含点位方案，恢复为达成意向
                businessEntity.setStatus(BusinessChangeStatusEnum.REACHING_INTENTION.getCode());
                return;
            }

            if (followRecords.contains(businessEntity.getCode())) {
                // 包含跟进，恢复为初步洽谈
                businessEntity.setStatus(BusinessChangeStatusEnum.PRELIMINARY_NEGOTIATIONS.getCode());
                return;
            }

            // 恢复为待洽谈
            businessEntity.setStatus(BusinessChangeStatusEnum.TO_BE_DISCUSSED.getCode());
        });
        updateBatchById(businessEntities);

        // 无效的面访跟进恢复为有效
        customerFollowRecordService.recover(businessCodes);

        // 发送商机状态变更消息
        CachedUser cachedUser = UserThreadLocal.getUser();
        businessEntities.forEach(businessEntity -> {
            BusinessStatusChangeVO businessStatusChangeVO = new BusinessStatusChangeVO();
            businessStatusChangeVO.setBusinessCode(businessEntity.getCode());
            businessStatusChangeVO.setStatus(businessEntity.getStatus());
            businessStatusChangeVO.setOperatorId(cachedUser.getId());
            businessStatusChangeVO.setOperatorWno(cachedUser.getWno());
            businessStatusChangeVO.setOperatorName(cachedUser.getName());
            kafkaProducerService.sendMessageAfterCommit(TopicConstants.BUSINESS_STATUS_CHANGE, JSON.toJSONString(businessStatusChangeVO));
        });
    }

}
