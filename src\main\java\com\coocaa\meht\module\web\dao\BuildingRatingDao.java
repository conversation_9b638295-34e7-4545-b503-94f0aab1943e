package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.module.building.vo.RatingPageVO;
import com.coocaa.meht.module.crm.dto.req.CmsBusinessReq;
import com.coocaa.meht.module.web.dto.HighSeaCustomerDTO;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.vo.HighSeaCustomerVO;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface BuildingRatingDao extends BaseMapper<BuildingRatingEntity> {

    default IPage<BuildingRatingEntity> pageBySubmitUser(CmsBusinessReq cmsBusinessReq) {
        Page<BuildingRatingEntity> page = new Page<>(cmsBusinessReq.getPage(), cmsBusinessReq.getLimit());
        LambdaQueryWrapper<BuildingRatingEntity> queryWrapper = new QueryWrapper<BuildingRatingEntity>().lambda()
                .eq(BuildingRatingEntity::getSubmitUser, cmsBusinessReq.getUserCode())
                .like(StringUtils.isNotBlank(cmsBusinessReq.getSearch()), BuildingRatingEntity::getBuildingName, cmsBusinessReq.getSearch())
                .eq(BuildingRatingEntity::getCrmPushStatus, 1)
                .and(status -> status.isNull(BuildingRatingEntity::getSignStatus)
                        .or(a -> a.in(BuildingRatingEntity::getSignStatus, Sets.newHashSet(
                                BuildingRatingEntity.BusinessStatusEnum.PENDING.getCode(),
                                BuildingRatingEntity.BusinessStatusEnum.UNSIGNED.getCode()))))
                .isNotNull(BuildingRatingEntity::getCustomerId)
                .orderByDesc(BuildingRatingEntity::getId);
        return this.selectPage(page, queryWrapper);
    }

    @Select(" SELECT '驳回' x,SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) y\n" +
            " FROM building_rating where deleted = 0 \n" +
            " union\n" +
            " SELECT '通过' x,SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) y\n" +
            " FROM building_rating where deleted = 0 \n" +
            " union\n" +
            " SELECT '申请中' x,SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) y\n" +
            " FROM building_rating where deleted = 0\n" +
            " union \n" +
            " SELECT '已放弃' x,SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) y\n" +
            " FROM building_rating where deleted = 0\n" +
            " union \n" +
            " SELECT '审核不通过' x,SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) y\n" +
            " FROM building_rating where deleted = 0")
    List<Map<String, Object>> getStatusChart();

    @Select("select date(create_time) dt,CONVERT(count(*),DECIMAL(12,2)) v  from building_rating  where deleted = 0 and \n" +
            "create_time >= NOW() - INTERVAL 30 DAY group by date(create_time)")
    List<Map<String, Object>> getUv();

    @Select("select create_time dt,building_name name,project_level  from building_rating  order by create_time desc ")
    List<Map<String, Object>> getList();

    @Select("SELECT '北京' name,\n" +
            "    SUM(CASE WHEN map_province = '北京市' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '天津' name,\n" +
            "    SUM(CASE WHEN map_province = '天津市' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '河北' name,\n" +
            "    SUM(CASE WHEN map_province = '河北省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '山西' name,\n" +
            "    SUM(CASE WHEN map_province = '山西省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '内蒙古' name,\n" +
            "    SUM(CASE WHEN map_province = '内蒙古自治区' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '辽宁' name,\n" +
            "    SUM(CASE WHEN map_province = '辽宁省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '吉林' name,\n" +
            "    SUM(CASE WHEN map_province = '吉林省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '黑龙江' name,\n" +
            "    SUM(CASE WHEN map_province = '黑龙江省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '上海' name,\n" +
            "    SUM(CASE WHEN map_province = '上海市' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '江苏' name,\n" +
            "    SUM(CASE WHEN map_province = '江苏省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '浙江' name,\n" +
            "    SUM(CASE WHEN map_province = '浙江省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '安徽' name,\n" +
            "    SUM(CASE WHEN map_province = '安徽省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '福建' name,\n" +
            "    SUM(CASE WHEN map_province = '福建省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '江西' name,\n" +
            "    SUM(CASE WHEN map_province = '江西省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '山东' name,\n" +
            "    SUM(CASE WHEN map_province = '山东省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '河南' name,\n" +
            "    SUM(CASE WHEN map_province = '河南省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '湖北' name,\n" +
            "    SUM(CASE WHEN map_province = '湖北省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '湖南' name,\n" +
            "    SUM(CASE WHEN map_province = '湖南省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '广东' name,\n" +
            "    SUM(CASE WHEN map_province = '广东省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '广西' name,\n" +
            "    SUM(CASE WHEN map_province = '广西壮族自治区' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '海南' name,\n" +
            "    SUM(CASE WHEN map_province = '海南省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '重庆' name,\n" +
            "    SUM(CASE WHEN map_province = '重庆市' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '四川' name,\n" +
            "    SUM(CASE WHEN map_province = '四川省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '贵州' name,\n" +
            "    SUM(CASE WHEN map_province = '贵州省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '云南' name,\n" +
            "    SUM(CASE WHEN map_province = '云南省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '西藏' name,\n" +
            "    SUM(CASE WHEN map_province = '西藏自治区' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '陕西' name,\n" +
            "    SUM(CASE WHEN map_province = '陕西省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '甘肃' name,\n" +
            "    SUM(CASE WHEN map_province = '甘肃省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '青海' name,\n" +
            "    SUM(CASE WHEN map_province = '青海省' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '宁夏' name,\n" +
            "    SUM(CASE WHEN map_province = '宁夏回族自治区' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0\n" +
            "union\n" +
            "SELECT '新疆' name,\n" +
            "    SUM(CASE WHEN map_province = '新疆维吾尔自治区' THEN 1 ELSE 0 END) value\n" +
            "FROM building_rating where deleted = 0")
    List<Map<String, Object>> getProvinceList();

    @Select("select sum(v) v from (\n" +
            "select date(create_time) dt,CONVERT(count(*),DECIMAL(12,2)) v  from building_rating  where deleted = 0 and \n" +
            "create_time >= NOW() - INTERVAL 30 DAY group by date(create_time)) tt")
    List<Map<String, Object>> getTotal();

    // ====================================================================
    List<BuildingRatingEntity> selectBuildingRatingUninvolvedLogRecord(@Param("type") String type,
                                                                       @Param("status") String status,
                                                                       @Param("timeRangeMap") Map<String, LocalDateTime> timeRangeMap,
                                                                       @Param("projectLevelList") List<String> projectLevelList);

    /** 查询评级申请涉及的rating记录 */
    List<BuildingRatingEntity> selectOfRatingApplicationRecordsInvolvedLogRecord(@Param("type") String type,
                                                                                 @Param("status") String status,
                                                                                 @Param("timeRangeMap") Map<String, LocalDateTime> timeRangeMap);


    List<BuildingRatingEntity> findBuildingMeta();

    Page<BuildingRatingEntity> approveList(@Param("page") Page<BuildingRatingEntity> page, @Param("name") String name , @Param("approveUser") String approveUser);

    Page<HighSeaCustomerVO> listHighSeaCustomer(Page<HighSeaCustomerVO> objectPage,
                                                @Param("condition")HighSeaCustomerDTO condition);

    List<String> getHighSeaCities();

    Page<RatingPageVO> ratingPage(Page<RatingPageVO> page,
                                  @Param("buildingName") String buildingName,
                                  @Param("statuses") List<Integer> statuses,
                                  @Param("startTime") String startTime,
                                  @Param("endTime") String endTime,
                                  @Param("cities") List<String> cities,
                                  @Param("userCodes") List<String> userCodes,
                                  @Param("submitUser") String submitUser);

}
