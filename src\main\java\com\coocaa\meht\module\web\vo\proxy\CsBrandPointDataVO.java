package com.coocaa.meht.module.web.vo.proxy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @file CsBrandPointDataVO
 * @date 2025/1/21 10:13
 * @description 创视 - BrandPointDataVO对象
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CsBrandPointDataVO {
    /**
     * 品牌编号
     */
    @Schema(description = "品牌编号")
    private String brandCode;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    private String brandName;

    /**
     * 楼宇名称
     */
    @Schema(description = "楼宇名称")
    private String building;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private String longitude;

    /**
     * 维度
     */
    @Schema(description = "维度")
    private String latitude;

    /**
     * 设备数
     */
    @Schema(description = "设备数")
    private Integer total;
}
