package com.coocaa.meht.common.bean;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Component
public class RpcCommonService {
    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;

    public List<CodeNameVO> listDictSelect(String code) {
        return RpcUtils.unBox(feignAuthorityRpc.listByParentCode(code));
    }

    public List<CodeNameVO> listCityByGbCodes(List<String> gbCodes) {
        return RpcUtils.unBox(feignAuthorityRpc.listByGbCode(gbCodes));
    }

    public CodeNameVO getByGbCode(String gbCode) {
        List<CodeNameVO> codeNameVOS = listCityByGbCodes(Lists.newArrayList(gbCode));
        if (CollectionUtil.isEmpty(codeNameVOS)) {
            throw new ServerException("没有查到当前城市" + gbCode);
        }
        return codeNameVOS.get(0);
    }

    public CodeNameVO getCityByGbCode(String gbCode) {
        return RpcUtils.unBox(feignAuthorityRpc.getByGbCode(gbCode));
    }

    public String getDictValue(String code) {
        ResultTemplate<List<CodeNameVO>> resultTemplate = feignAuthorityRpc.listDictByCodes(Lists.newArrayList(code));
        List<CodeNameVO> codeNameVOS = RpcUtils.unBox(resultTemplate);
        if(CollectionUtil.isNotEmpty(codeNameVOS)){
            return codeNameVOS.get(0).getName();
        }
        throw new ServerException("没有找到字典");
    }
}
