package com.coocaa.meht.module.web.vo.kanban;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class BuildingDataVO {
    private String cityName;
    private int waitApprovedCount;
    private int approvedCount;
    private double conversionRate;

    public double getConversionRate() {
        if (waitApprovedCount == 0) {
            return 0;
        }
        // 计算百分比，保留1位小数 四舍五入
        return Math.round((double) approvedCount / waitApprovedCount * 100) / 100.0;
    }
}
