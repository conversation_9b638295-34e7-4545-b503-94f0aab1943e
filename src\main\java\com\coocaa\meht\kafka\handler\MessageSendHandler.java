package com.coocaa.meht.kafka.handler;

import com.alibaba.fastjson2.JSON;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.kafka.TopicCallback;
import com.coocaa.meht.kafka.TopicHandler;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.dto.UserBatchMessageParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 消息发送处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-23
 */
@Slf4j
@Component
@TopicHandler("msg_send")
public class MessageSendHandler implements TopicCallback {

    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;

    @Override
    public void process(String message) {
        try {
            UserBatchMessageParam messageParam = JSON.parseObject(message, UserBatchMessageParam.class);
            ResultTemplate<Void> result = feignAuthorityRpc.sendUserMessageBatch(messageParam);
            log.info("发送消息结果，result:{}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("发送消息异常，消息内容{}，", message, e);
        }
    }

}
