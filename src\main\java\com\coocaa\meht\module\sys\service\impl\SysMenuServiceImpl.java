package com.coocaa.meht.module.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.sys.dao.SysMenuDao;
import com.coocaa.meht.module.sys.entity.SysMenuEntity;
import com.coocaa.meht.module.sys.service.SysMenuService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 菜单管理
 */
@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl extends ServiceImpl<SysMenuDao, SysMenuEntity> implements SysMenuService {

//    @Override
//    public List<SysMenuEntity> getListByUser(String userCode) {
//        String value = sysConfigService.getVal("SuperSdministratorCodes");
//        if (StringUtils.isNotBlank(value) && value.contains(userCode)) {
//            return this.list(new QueryWrapper<SysMenuEntity>().orderByAsc("`sort`"));
//        } else {
//            int flag = this.baseMapper.conutPlotObj(userCode) ? 1 : 0;
//            if (flag == 0) {
//                flag = this.baseMapper.conutTaskPlot(userCode) ? 1 : 0;
//            }
//            List<SysMenuEntity> list = this.baseMapper.getListByUser(userCode, flag);
//            return list.stream().distinct()
//                    .sorted(Comparator.comparing(SysMenuEntity::getSort))
//                    .collect(Collectors.toList());
//        }
//    }
//
//    @Override
//    public Set<String> getUserAuthority(String userCode) {
//        return this.convertPermi(this.getListByUser(userCode));
//    }
//
//    @Override
//    public Set<String> convertPermi(List<SysMenuEntity> list) {
//        if (CollectionUtils.isEmpty(list)) {
//            return Collections.emptySet();
//        }
//        return list.stream().flatMap(ele -> Arrays.stream(ele.getAuthority().trim().split(",")))
//                .filter(StringUtils::isNotBlank)
//                .collect(Collectors.toSet());
//    }

}