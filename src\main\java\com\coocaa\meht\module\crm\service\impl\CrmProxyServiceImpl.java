package com.coocaa.meht.module.crm.service.impl;

import cn.hutool.core.map.MapUtil;
import com.coocaa.meht.module.crm.service.CrmProxyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.StringJoiner;

/**
 * CRM 接口代理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CrmProxyServiceImpl extends CrmBaseService implements CrmProxyService {
    @Override
    public String proxyPost(String url, Map<String, String> queryParams) {
        if (MapUtil.isNotEmpty(queryParams)) {
            StringJoiner joiner = new StringJoiner("&");
            queryParams.forEach((k, v) -> joiner.add(k + "=" + v));
            url = url + "?" + joiner.toString();
        }
        return callCrmApi(url, null);
    }

    @Override
    public String proxyPost(String url, String body) {
        return callCrmApi(url, body);
    }

    @Override
    public String getCrmToken() {
        return super.getCrmToken();
    }
}
