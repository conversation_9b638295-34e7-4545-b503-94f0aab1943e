package com.coocaa.meht.rpc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Schema(name = "InnerApproveTaskPageQueryParam", description = "查询站内审批任务列表入参")
public class InnerApproveTaskPageQueryParam {

    @Schema(description = "规则编号")
    private Integer ruleCode;

    @Schema(description = "0:待审，1：已审")
    private Integer flag;

    @Schema(description = "用户ID，默认为当前登陆人")
    private Integer userId;

    @Schema(description = "排序字段，开始时间：startTime,默认开始时间")
    private String sortFiled;

    @Schema(description = "排序字段，顺序：asc,倒序：desc，默认倒序")
    private String sortRule;
}
