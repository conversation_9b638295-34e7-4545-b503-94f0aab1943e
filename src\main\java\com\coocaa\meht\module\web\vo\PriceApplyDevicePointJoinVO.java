package com.coocaa.meht.module.web.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 设备点位Join返回对象
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-29 17:10
 */
@Data
public class PriceApplyDevicePointJoinVO {
    /**
     * 价格申请code
     */
    private String applyCode;

    /**
     * 设备激励金
     */
    private BigDecimal incentivePrice;

    /**
     * 点位code
     */
    private String pointCode;
}
