package com.coocaa.meht.module.web.dto.crm;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class CrmBusinessParamDto {

    private EntityDto entity;

    private List<FieldDto> field;


    @Data
    @Accessors(chain = true)
    public static class EntityDto {

        /**
         * 商机名称
         */
        private String businessName;

        /**
         * 负责人id
         */
        private String ownerUserId;


        private String money;

        private String typeId;

        /**
         * 客户信息
         */
        private List<Map<String,String>> customer;

    }




    @Data
    @Accessors(chain = true)
    public static class FieldDto {

        private String fieldId;

        private String fieldName;

        private Integer fieldType;

        private String name;

        private Integer type;

        private String value;


    }

}
