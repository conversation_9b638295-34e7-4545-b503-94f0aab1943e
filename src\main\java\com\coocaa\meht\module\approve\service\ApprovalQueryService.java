package com.coocaa.meht.module.approve.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.approve.dto.ApprovalDetailVO;
import com.coocaa.meht.module.approve.dto.ScreenApproveRecordDTO;
import com.coocaa.meht.module.approve.dto.TaskDealCountParam;
import com.coocaa.meht.module.approve.dto.TaskDealCountVO;
import com.coocaa.meht.module.approve.dto.TodoListQueryDTO;
import com.coocaa.meht.module.approve.dto.TodoTaskDTO;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.rpc.vo.InnerApproveNodeVO;
import com.coocaa.meht.rpc.vo.InnerInstanceTaskVO;

import java.util.List;

/**
 * 审批查询服务接口
 * 处理审批查询相关操作
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface ApprovalQueryService {

    /**
     * 查询待办列表
     *
     * @param queryDTO 查询条件
     * @return 待办列表
     */
    Result<IPage<TodoTaskDTO>> queryTodoList(PageRequestVO<TodoListQueryDTO> queryDTO);

    /**
     * 查询已办列表
     *
     * @param queryDTO 查询条件
     * @return 已办列表
     */
    Result<IPage<TodoTaskDTO>> queryDoneList(PageRequestVO<TodoListQueryDTO> queryDTO);

    /**
     * 查询审批详情
     * 查询当前实例-审核中心节点
     *
     * @param instanceCode 审批实例编码
     * @return 审批详情
     */
    Result<ApprovalDetailVO> queryDetail(String instanceCode);

    /**
     * 查询本地审核节点
     *
     * @param businessKey 业务编码
     * @return 审批详情
     */
    Result<List<ScreenApproveRecordDTO>> queryLocalNodes(String businessKey);

    /**
     * 根据业务键和场景类型查询审批详情
     * 查询当前实例-审核中心节点
     *
     * @param businessKey 业务键
     * @param approveType 审批类型
     * @param version     版本号
     * @return 审批详情
     */
    Result<ApprovalDetailVO> queryDetailByBusinessKey(String businessKey, String approveType, String version);

    /**
     * 审批-用户待办统计
     *
     * @param param a
     * @return {@link Result}<{@link List}<{@link TaskDealCountVO}>>
     */
    Result<List<TaskDealCountVO>> getTaskDealCount(TaskDealCountParam param);

    /**
     * 根据审批实例获取当前审核节点
     *
     * @param instanceCode 审批实例
     * @return
     */
    InnerInstanceTaskVO getCurrentTask(String instanceCode);

    /**
     * 判断下一个审批人是否是当前登陆人
     * 默认返回false
     *
     * @param instanceCode 流程实例编码
     * @return true:是当前登陆人，false:不是当前登陆人
     */
    Boolean judgeNextApproveUser(String instanceCode);

    /**
     * 根据业务编码和版本号查询当前审核节点
     *
     * @param businessKey 业务编码
     * @param version     版本号
     * @return
     */
    InnerInstanceTaskVO getCurrentTask(String businessKey, String version);


    /**
     * 飞书审核链接查询跳转参数
     *
     * @param instanceCode
     * @return
     */
    Object getDetailParams(String instanceCode, Integer type);


    /**
     * 获取待处理节点
     *
     * @param instanceCode 审批实例
     * @return
     */
    InnerApproveNodeVO getPendingNode(String instanceCode);

    /**
     * 判断审批任务是否存在
     *
     * @param businessKey 业务键
     * @param version     版本号
     * @param type        审批类型
     * @return
     */
    Boolean isTaskExist(String businessKey, String version, ApprovalTypeEnum type);

}