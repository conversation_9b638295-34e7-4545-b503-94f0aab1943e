package com.coocaa.meht.module.web.dto;

import com.coocaa.meht.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 楼宇品牌
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-22
 */
@Data
public class BuildingBrandDto {
    /**
     * 品牌ID
     */
    private Integer id;

    /**
     * 名称
     */
    @NotBlank(message = "品牌名称不能为空")
    private String name;

    /**
     * 编码
     */
    @NotBlank(message = "品牌编码不能为空")
    private String type;

    /**
     * 品牌指数
     */
    @DecimalMin(value = "0.0", message = "品牌指数必须大于等于0")
    private BigDecimal index;

    /**
     * 排名(自然顺序，越小越靠前)
     */
    @Min(value = 1, message = "排名必须大于等于1")
    private Integer rank;

    /**
     * 状态 [0:禁用, 1:启用]
     */
    private Integer status;

    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;
}