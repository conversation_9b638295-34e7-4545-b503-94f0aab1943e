package com.coocaa.meht.common;

import com.coocaa.meht.common.exception.ErrorCode;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.utils.RedisUtils;
import com.coocaa.meht.utils.ServletUtils;
import com.coocaa.meht.utils.SpringBeanUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024-11-05 10:58
 */
public class SecurityUser {

    public static final String LOGIN_USER_KEY = "meht:user:login:";

    private static final InheritableThreadLocal<LoginUser> userThreadLocal = new InheritableThreadLocal<>();

    /**
     * 获取登录用户
     *
     * @return
     */
    public static LoginUser getUser() {
        LoginUser user = userThreadLocal.get();
        if (user != null) {
            return user;
        }
        throw new ServerException(ErrorCode.UNAUTHORIZED);
    }

    public static String getUserCode() {
        return getUser().getUserCode();
    }

    /**
     * 根据token获取登录用户
     *
     * @param accessToken
     * @return
     */
    public static LoginUser getUser(String accessToken) {
        RedisUtils redis = SpringBeanUtils.getBean(RedisUtils.class);
        return redis.get(SecurityUser.userLoginKey(accessToken), LoginUser.class);
    }

    /**
     * 获取登录token
     *
     * @return
     */
    public static String getAccessToken(HttpServletRequest request) {
        if (request == null) {
            request = ServletUtils.getRequest();
        }
        if (request != null) {
            String accessToken = request.getHeader("token");
            if (StringUtils.isBlank(accessToken)) {
                accessToken = request.getParameter("accessToken");
            }
            return accessToken;
        }
        return null;
    }

    /**
     * 登录
     *
     * @param user
     */
    public static void login(LoginUser user) {
        userThreadLocal.set(user);
    }

    /**
     * 不验证的登录用户
     *
     * @return
     */
    public static LoginUser getUserAnonymity() {
        return userThreadLocal.get();
    }

    public static void clearLogin() {
        userThreadLocal.remove();
    }

    public static String userLoginKey(String token) {
        return LOGIN_USER_KEY + token;
    }

}
