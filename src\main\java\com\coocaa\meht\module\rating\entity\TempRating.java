package com.coocaa.meht.module.rating.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @version 1.0
* @since 2025-05-28
*/
/**
 * 楼宇评级分数表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "temp_rating")
public class TempRating {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * 楼龄
     */
    @TableField(value = "building_age")
    private String buildingAge;

    /**
     * 楼龄分数
     */
    @TableField(value = "building_age_score")
    private BigDecimal buildingAgeScore;

    /**
     * 综合体品牌
     */
    @TableField(value = "building_brand")
    private String buildingBrand;

    /**
     * 综合体品牌分数
     */
    @TableField(value = "building_brand_score")
    private BigDecimal buildingBrandScore;

    /**
     * 外观造型
     */
    @TableField(value = "building_exterior")
    private String buildingExterior;

    /**
     * 外观造型分数
     */
    @TableField(value = "building_exterior_score")
    private BigDecimal buildingExteriorScore;

    /**
     * 地下车库
     */
    @TableField(value = "building_garage")
    private String buildingGarage;

    /**
     * 地下车库分数
     */
    @TableField(value = "building_garage_score")
    private BigDecimal buildingGarageScore;

    /**
     * 写字楼等级
     */
    @TableField(value = "building_grade")
    private String buildingGrade;

    /**
     * 写字楼等级分数
     */
    @TableField(value = "building_grade_score")
    private BigDecimal buildingGradeScore;

    /**
     * 侯梯厅
     */
    @TableField(value = "building_hall")
    private String buildingHall;

    /**
     * 侯梯厅分数
     */
    @TableField(value = "building_hall_score")
    private BigDecimal buildingHallScore;

    /**
     * 楼盘大堂
     */
    @TableField(value = "building_lobby")
    private String buildingLobby;

    /**
     * 楼盘大堂分数
     */
    @TableField(value = "building_lobby_score")
    private BigDecimal buildingLobbyScore;

    /**
     * 地理位置
     */
    @TableField(value = "building_location")
    private String buildingLocation;

    /**
     * 地理位置分数
     */
    @TableField(value = "building_location_score")
    private BigDecimal buildingLocationScore;

    /**
     * 楼宇申请编码
     */
    @TableField(value = "building_no")
    private String buildingNo;

    /**
     * 楼层数
     */
    @TableField(value = "building_number")
    private String buildingNumber;

    /**
     * 楼层数分数
     */
    @TableField(value = "building_number_score")
    private BigDecimal buildingNumberScore;

    /**
     * 月租金
     */
    @TableField(value = "building_price")
    private String buildingPrice;

    /**
     * 月租金分数
     */
    @TableField(value = "building_price_score")
    private BigDecimal buildingPriceScore;

    /**
     * 点评评分
     */
    @TableField(value = "building_rating")
    private String buildingRating;

    /**
     * 点评评分分数
     */
    @TableField(value = "building_rating_score")
    private BigDecimal buildingRatingScore;

    /**
     * 认证总分
     */
    @TableField(value = "building_score")
    private BigDecimal buildingScore;

    /**
     * 入驻率
     */
    @TableField(value = "building_settled")
    private String buildingSettled;

    /**
     * 入驻率分数
     */
    @TableField(value = "building_settled_score")
    private BigDecimal buildingSettledScore;

    /**
     * 楼宇类型
     */
    @TableField(value = "building_type")
    private String buildingType;

    /**
     * 认证等级
     */
    @TableField(value = "project_level")
    private String projectLevel;

    /**
     * 数据来源 0 人工 1 豆包  2 deepseek 3 千问
     */
    @TableField(value = "`source`")
    private Integer source;

}