package com.coocaa.meht.module.web.dto.convert;

import com.coocaa.meht.module.dataimport.pojo.PointDataVO;
import com.coocaa.meht.module.web.dto.PointDetailDto;
import com.coocaa.meht.module.web.dto.PointDetailOutDto;
import com.coocaa.meht.module.web.dto.point.PointDTO;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.entity.PointContractSnapshotEntity;
import com.coocaa.meht.module.web.entity.PointEntity;
import com.coocaa.meht.module.web.entity.PointPriceSnapshotEntity;
import com.coocaa.meht.rpc.dto.SspPointAddParam;
import com.coocaa.meht.rpc.dto.UpdatePointParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.control.DeepClone;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface PointConvert {
    @Mapping(source = "description",target = "remark")
    PointEntity toPointEntity(PointDTO pointAddDTO);

    List<PointDetailOutDto> toPointDetails(List<PointDetailDto> pointDetailVOS);

    @Mappings(value = {
            @Mapping(source = "remark",target = "pointRemark"),
            @Mapping(source = "code",target = "pointCode"),
            @Mapping(source = "createTime",target = "pointCreateTime"),
    })
    PointDetail toPointDetail(PointPriceSnapshotEntity entity);
    List<PointDetail> toPointDetailsByPrice(List<PointPriceSnapshotEntity> list);

    @Mappings(value = {
            @Mapping(source = "remark",target = "pointRemark"),
            @Mapping(source = "code",target = "pointCode"),
            @Mapping(source = "createTime",target = "pointCreateTime"),
    })
    PointDetail toContractPointDetails(PointContractSnapshotEntity entity);

    List<PointDetail> toPointDetailsByContract(List<PointContractSnapshotEntity> list);

    @Mapping(source = "pointCode",target = "code")
    PointEntity toPointEntity(SspPointAddParam pointAddParam);

    @Mapping(source = "description", target = "remark")
    UpdatePointParam toUpdatePointParam(PointDTO pointAddDTO);

    List<PointEntity> toPointEntitySys(List<PointDataVO> pointList);
}
