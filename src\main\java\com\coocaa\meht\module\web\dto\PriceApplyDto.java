package com.coocaa.meht.module.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 价格申请DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-22
 */
@Data
public class PriceApplyDto {

    /**
     * 如果由草稿的话，把草稿的申请编码传过来，提交的时候处理对应的草稿
     */
    @Schema(description = "申请编码")
    private String applyCode;

    /**
     * 审批实例编码
     */
    @Schema(description = "审批实例编码")
    private String instanceCode;

    /**
     * 楼宇编码
     */
    @NotBlank(message = "楼宇编码不能为空")
    @Schema(description = "楼宇编码")
    private String buildingNo;

    @NotBlank(message = "商机编码不能为空")
    @Schema(description = "商机编码")
    private String businessCode;

    /**
     * 合同年限
     */
    @NotNull(message = "合同年限不能为空")
    @DecimalMin(value = "0.0", message = "合同年限不能小于0")
    @Schema(description = "合同年限")
    private BigDecimal contractDuration;

    /**
     * 合同总金额
     */
    @NotNull(message = "合同总金额不能为空")
    @DecimalMin(value = "0.0", message = "合同总金额不能小于0")
    @Schema(description = "合同总金额")
    private BigDecimal totalAmount;

    /**
     * 付款方式
     */
    @NotBlank(message = "付款方式不能为空")
    @Size(max = 10, message = "付款方式长度不能超过10个字符")
    @Schema(description = "付款方式")
    private String paymentType;

    /**
     * 是否有押金
     */
    @NotNull(message = "请选择是否有押金")
    @Schema(description = "是否有押金")
    private Boolean isDeposit;

    /**
     * 押金金额
     */
    @DecimalMin(value = "0.0", message = "押金金额不能小于0")
    @Schema(description = "押金金额")
    private BigDecimal depositAmount;

    /**
     * 备注
     */
    @Size(max = 300, message = "备注长度不能超过300个字符")
    @Schema(description = "备注")
    private String remark;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    private List<Integer> fileIds;

    /**
     * 设备列表
     */
    @Schema(description = "设备列表")
    private List<PriceApplyDeviceDto> devices;
}
