package com.coocaa.meht.module.web.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.crm.dto.req.CrmFollowUpListReq;
import com.coocaa.meht.module.crm.vo.FollowUpVO;
import com.coocaa.meht.module.web.dao.CustomerFollowRecordMapper;
import com.coocaa.meht.module.web.entity.CustomerFollowRecordEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.enums.FollowTypeEnum;
import com.coocaa.meht.module.web.service.CustomerFollowRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 客户跟进记录
 */
@Service
public class CustomerFollowRecordServiceImpl extends ServiceImpl<CustomerFollowRecordMapper, CustomerFollowRecordEntity> implements CustomerFollowRecordService {
    @Autowired
    private CustomerFollowRecordMapper customerFollowRecordMapper;

    @Override
    public Page<FollowUpVO> getCustomerFollowRecord(CrmFollowUpListReq req) {
        Page<FollowUpVO> page = new Page<>(req.getPage(), req.getLimit());
        Page<FollowUpVO> customerFollowRecord = customerFollowRecordMapper.getCustomerFollowRecord(page, req.getBuildingNo(), req.getId(), req.getBusinessCode());
        if (!CollectionUtil.isEmpty(customerFollowRecord.getRecords())) {
            for (FollowUpVO followUpVO : customerFollowRecord.getRecords()) {
                String category = FollowTypeEnum.getByName(followUpVO.getCategory());
                followUpVO.setCategory(category);
                followUpVO.setFollowMethod(category);
            }
        }
        return customerFollowRecord;
    }

    @Override
    public void recover(Set<String> businessCodes) {
        if (CollUtil.isEmpty(businessCodes)) {
            return;
        }

        // 面访跟进恢复为有效
        lambdaUpdate()
                .set(CustomerFollowRecordEntity::getValid, BooleFlagEnum.YES.getCode())
                .eq(CustomerFollowRecordEntity::getVisitType, FollowTypeEnum.INTERVIEW.name())
                .in(CustomerFollowRecordEntity::getBusinessCode, businessCodes)
                .update();
    }

}
