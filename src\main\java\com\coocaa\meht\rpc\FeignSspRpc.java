package com.coocaa.meht.rpc;

import com.coocaa.ad.common.config.FeignConfig;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.module.web.dto.ProjectForbidIndustriesDTO;
import com.coocaa.meht.module.web.dto.point.DeleteParam;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.dto.point.ProjectPointCountVO;
import com.coocaa.meht.module.web.dto.point.WaitingHallDetail;
import com.coocaa.meht.rpc.dto.DeletePointParam;
import com.coocaa.meht.rpc.dto.PointTreeUpdate;
import com.coocaa.meht.rpc.dto.SspPointAddParam;
import com.coocaa.meht.rpc.dto.SspPointWatingHallVO;
import com.coocaa.meht.rpc.dto.UpdatePointParam;
import com.coocaa.meht.rpc.dto.WaitingHallAddParam;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * ssp远程调用
 */
@FeignClient(value = "cheese-ssp-api",
//        url = "localhost:8002",
        configuration = FeignConfig.class)
public interface FeignSspRpc {

    /**
     * 新增等候厅
     *
     * @return
     */
    @PutMapping("/ssp/point/waiting/add")
    ResultTemplate<Integer> addWaitingHall(@RequestBody @Validated WaitingHallAddParam param);

    /**
     * 修改等候厅
     *
     * @param param
     * @return
     */
    @PutMapping("/ssp/point/tree/update")
    ResultTemplate<Void> updateTreeNode(@RequestBody @Validated PointTreeUpdate param);

    /**
     * 等候厅详情
     *
     * @param id
     * @return
     */
    @GetMapping("/ssp/point/waiting-hall/{id}")
    ResultTemplate<WaitingHallDetail> getWaitingHallById(@PathVariable("id") Integer id);

    /**
     * 分别按照楼栋，单元，楼层，等候厅进行删除
     *
     * @param param
     * @return
     */
    @DeleteMapping("/ssp/point/deletePoint")
    ResultTemplate<DeletePointParam> deletePoint(@Validated @RequestBody DeleteParam param);

    /**
     * 新增点位
     *
     * @param param
     * @return
     */
    @PutMapping("/ssp/point/add")
    ResultTemplate<SspPointAddParam> addPoint(@RequestBody @Validated SspPointAddParam param);

    /**
     * 修改点位
     * @param param
     * @return
     */
    @PutMapping("/ssp/point/update")
    ResultTemplate<Void> updatePoint(@RequestBody @Validated UpdatePointParam param);

    /**
     * 查询点位列表
     * @param buildingRatingNo
     * @return
     */
    @GetMapping("/ssp/point/rpc/list")
    ResultTemplate<List<PointDetail>> pointList(@RequestParam(name = "buildingRatingNo") String buildingRatingNo);

    /**
     * 根据等候厅id查询点位列表
     * @param waitingHallIds
     * @return
     */
    @PostMapping("/ssp/point/rpc/waitingHall/list")
    ResultTemplate<List<PointDetail>> findByPointList(@RequestBody @NotEmpty List<Integer> waitingHallIds);

    /**
     * 根据点位编码查询快照信息
     * @param pointCode
     * @return
     */
    @PostMapping("/list/pointWaitingHallDetail")
     ResultTemplate<List<SspPointWatingHallVO>> pointWaitingHallDetail(@PathVariable("pointCodes") List<String> pointCode);

    /**
     * 统计等候厅点位数量
     * @param pointCodes
     * @return
     */
    @PostMapping("/ssp/point/countWaitingHallPoint")
     ResultTemplate<List<ProjectPointCountVO>> countWaitingHallPoint(@RequestBody @NotEmpty List<String> pointCodes);

    /**
     * 同步修改项目的禁忌行业
     * @param param
     * @return
     */
    @PutMapping("/ssp/project/update-project-forbidIndustries")
    ResultTemplate<Void> updateProjectForbidIndustries(@RequestBody ProjectForbidIndustriesDTO param);

}
