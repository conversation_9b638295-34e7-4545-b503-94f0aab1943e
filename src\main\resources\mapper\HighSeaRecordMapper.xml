<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.web.dao.HighSeaRecordMapper">
  <resultMap id="BaseResultMap" type="com.coocaa.meht.module.web.entity.HighSeaRecordEntity">
    <!--@mbg.generated-->
    <!--@Table high_sea_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="building_name" jdbcType="VARCHAR" property="buildingName" />
    <result column="building_no" jdbcType="VARCHAR" property="buildingNo" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="operate_type" jdbcType="TINYINT" property="operateType" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="responsible_person" jdbcType="VARCHAR" property="responsiblePerson" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, building_name, building_no, operate_time, operate_type, `operator`, remark, responsible_person
  </sql>
</mapper>