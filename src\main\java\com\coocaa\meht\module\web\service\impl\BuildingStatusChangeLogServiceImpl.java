package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.BuildingStatusChangeLogMapper;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.service.IBuildingStatusChangeLogService;
import java.util.List;
import java.util.Objects;

import com.coocaa.meht.module.web.vo.kanban.StatusChangeVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @file BuildingStatusChangeLogServiceImpl
 * @date 2025/1/2 14:05
 * @description 楼宇/商机状态变更记录
 */

@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingStatusChangeLogServiceImpl extends ServiceImpl<BuildingStatusChangeLogMapper, BuildingStatusChangeLogEntity> implements IBuildingStatusChangeLogService {
    private final BuildingStatusChangeLogMapper buildingStatusChangeLogMapper;

    @Override
    public List<StatusChangeVO> getBuildingRatingStatusChangeList(StatusChangeVO param) {
        return buildingStatusChangeLogMapper.getBuildingRatingStatusChangeList(param);
    }

    @Override
    public List<StatusChangeVO> getProjectStatusChangeList(StatusChangeVO param) {
        return buildingStatusChangeLogMapper.getProjectStatusChangeList(param);
    }

    @Override
    public void deleteLatestLog(Long id, BuildingStatusChangeLogEntity.BizType bizType,
                                BuildingStatusChangeLogEntity.RatingApplicationStatus ratingApplicationStatus) {
        BuildingStatusChangeLogEntity logEntity = lambdaQuery()
                .eq(BuildingStatusChangeLogEntity::getType, bizType.getCode())
                .eq(BuildingStatusChangeLogEntity::getBizId, id)
                .eq(BuildingStatusChangeLogEntity::getStatus, ratingApplicationStatus.getCode())
                .orderByDesc(BuildingStatusChangeLogEntity::getChangeTime)
                .last("limit 1")
                .one();

        if (Objects.nonNull(logEntity)) {
            lambdaUpdate()
                    .set(BuildingStatusChangeLogEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                    .eq(BuildingStatusChangeLogEntity::getId, logEntity.getId())
                    .update();
        }
    }

}
