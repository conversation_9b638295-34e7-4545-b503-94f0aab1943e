package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024-11-07 16:49
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("message_record")
public class MessageRecordEntity extends BaseEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String targetUser;    //目标用户
    private String sendUser;      //发送用户
    private Integer sendType;     //类型：0用户发送，1系统发送
    private String classify;      //消息类别
    @TableField("`content`")
    private String content;       //消息内容
    private String fsMessageId;         //飞书消息ID
    private Integer fsReadStatus;       //飞书读状态: 0未读，1已读
    private LocalDateTime fsReadTime;   //飞书读时间
}
