package com.coocaa.meht.module.web.dto;


import com.coocaa.meht.converter.Convert;
import com.coocaa.meht.converter.ConvertType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/18
 * @description 点位详情
 */
@Data
public class PointDetailOutDto {
    // private String projectName;
    private String buildingRatingNo;
    private String buildingName;
    private String unitName;
    @Convert(type = ConvertType.DICT)
    private String floor;
    private String floorName;
    private String waitingHallName;
    @Convert(type = ConvertType.DICT)
    private String waitingHallType;
    private String waitingHallTypeName;
    private Integer waitingHallId;
    private String pointCode;

    private String pointRemark;
    @Convert(type = ConvertType.DICT)
    private String deviceSize;
    private String deviceSizeName;
    private Integer pointId;
    private List<String> pointPics;
    private LocalDateTime pointCreateTime;
}
