package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 价格申请
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("price_apply")
public class PriceApplyEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 价格申请编码： JGSQ+年月日+流水号（4位，从0001开始）
     */
    private String applyCode;

    /**
     * 商机编码
     */
    private String businessCode;

    /**
     * 楼宇编码
     */
    private String buildingNo;

    /**
     * 楼宇名称
     */
    private String buildingName;

    /**
     * 合同年限
     */
    private BigDecimal contractDuration;

    /**
     * 合同总金额(元)
     */
    private BigDecimal totalAmount;

    /**
     * 付款方式 [一次性、一年付、半年付、季度付、其他]
     */
    private String paymentType;

    /**
     * 是否有押金 [0:否, 1:是]
     */
    private Integer isDeposit;

    /**
     * 押金金额(元)
     */
    private BigDecimal depositAmount;

    /**
     * 其他信息
     */
    private String remark;

    /**
     * 附件ID列表
     */
    private String fileIds;

    /**
     * 状态 [0:草稿 1:待审核 2:已审核 3:审核不通过]
     */
    private Integer status;

    /**
     * 审批人
     */
    private String approveBy;

    /**
     * 审批时间
     */
    private LocalDateTime approveTime;

    /**
     * 审批备注
     */
    private String approveRemark;

    /**
     * CRM的用户ID
     */
    private String crmUserId;

    /**
     * top值
     */
    private String topLevel;

    /**
     * 是否是大屏 [1:小屏, 2:大屏, 3:大小屏]
     */
    private Integer largeScreenFlag;

    /**
     * 评级版本
     */
    private String ratingVersion;

    /**
     * 等级评价
     */
    private String projectLevel;

    /**
     * 大屏复核系数
     */
    private BigDecimal finalCoefficient;

    /**
     * 小屏水位价
     */
    private BigDecimal smallWaterMarkPrice;

    /**
     * 大屏水位价
     */
    private BigDecimal bigWaterMarkPrice;

    /**
     * 草稿json，用于前端回显
     */
    private String draft;

    /**
     * 楼宇类型
     */
    private Integer buildingType;

    /**
     * 省
     */
    private String mapProvince;

    /**
     * 市
     */
    private String mapCity;

    /**
     * 区名称
     */
    private String mapRegion;

    /**
     * 详细地址
     */
    private String mapAddress;

    /**
     * 审批实例编码
     */
    private String instanceCode;

    /**
     * 数据版本号
     */
    private String version;

    /**
     * 地理位置
     */
    private String locationName;


    @Getter
    @AllArgsConstructor
    public enum Status {
        DRAFT(0, "草稿"),
        PENDING(1, "审核中"),
        PASSED(2, "审核通过"),
        REJECTED(3, "审核不通过"),
        CANCEL(4, "取消或作废");

        private final Integer code;
        private final String desc;

        public static String getDesc(int value) {
            for (Status status : Status.values()) {
                if (Objects.equals(status.getCode(), value)) return status.desc;
            }
            return null;
        }
    }
}
