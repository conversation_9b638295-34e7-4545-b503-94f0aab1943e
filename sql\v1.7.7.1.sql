-- 楼宇meht
ALTER TABLE `building_parameter`
    ADD COLUMN `data_flag` int(2) NOT NULL DEFAULT 0 COMMENT '版本标识' AFTER `parameter_rule`;


CREATE TABLE `building_city_rent`
(
    `id`            int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `ad_code`       bigint(20)   NOT NULL DEFAULT 0 COMMENT '行政区域编码',
    `city`          varchar(20)   NOT NULL DEFAULT '' COMMENT '城市',
    `office_rent`   decimal(8, 2) NOT NULL DEFAULT '0.00' COMMENT '租金',
    `building_type` int(2) NOT NULL DEFAULT '0' COMMENT '0',
    `create_by`     varchar(10)   NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`     varchar(10)   NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time`   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='租金配置表';


INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('北京', 110100, 158.10, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');
INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('上海', 310100, 105.30, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');
INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('广州', 440100, 81.90, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');
INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('深圳', 440300, 99, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');
INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('成都', 510100, 52.2, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');
INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('武汉', 420100, 47.1, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');
INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('南京', 320100, 59.7, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');
INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('重庆', 500100, 41.4, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');
INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('西安', 610100, 46.2, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');
INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('长沙', 430100, 47.1, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');
INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('苏州', 320500, 59.1, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');
INSERT INTO `cheese_merchant`.`building_city_rent` (`city`, `ad_code`, `office_rent`, `building_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('杭州', 330100, 73.8, 0, 'CC0000', '2025-05-20 09:21:58', 'CC0000', '2025-05-20 09:22:12');



INSERT INTO `building_parameter`
(`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 362,0, 0, '写字楼等级', 'buildingGrade', 0.00, 5.00000000, 1, 0, '', '', '2024-12-18 20:16:12', '写字楼等级', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 363,0, 362, '超甲级', 'buildingGrade', 10.00, 5.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '超甲级', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 364,0,362, '甲级', 'buildingGrade', 8.00, 5.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '甲级', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 365,0, 362, '乙级', 'buildingGrade', 6.00, 5.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '乙级', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 366,0, 362, '其他', 'buildingGrade', 0.00, 5.00000000, 4, 0, '', '', '2024-12-26 21:00:01', '其它', 1);



INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 367,0, 0, '地理位置', 'buildingLocation', 0.00, 25.00000000, 2, 0, '', '', '2024-12-18 20:16:12', '地理位置', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 368,0, 367, '城市核心区', 'buildingLocation', 10.00, 25.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '城市核心区', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 369,0, 367, '城市非核心区', 'buildingLocation', 5.00, 25.00000000, 2, 0, '', '', '2024-12-26 20:51:26', '非城市核心区', 1);



INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 370,0, 0, '楼层数', 'buildingNumber', 0.00, 10.00000000, 3, 0, '', '', '2024-12-18 20:16:12', '楼层数', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 371,0, 370, '30层（含）以上', 'buildingNumber', 10.00, 10.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '>=30', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 372,0, 370, '20（含）~30层', 'buildingNumber', 9.00, 10.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '>=20,<30', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 373,0, 370, '20层以下', 'buildingNumber', 7.00, 10.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '<20', 1);



INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 374,0, 0, '月租金', 'buildingPrice', 0.00, 40.00000000, 4, 0, '', '', '2024-12-18 20:16:12', '月租金', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (375, 0, 374, '超过城市租金均价50%以上', 'buildingPrice', 10.00, 40.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '>1.5', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 376,0, 374, '超过城市租金均价的20%', 'buildingPrice', 8.00, 40.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '>1.2,<=1.5', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 377,0, 374, '超过城市租金均价的20%以内', 'buildingPrice', 6.00, 40.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '>=1,<=1.2', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 378,0, 374, '低于城市租金均价', 'buildingPrice', 4.00, 40.00000000, 4, 0, '', '', '2024-12-26 20:21:38', '<1', 1);


INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 379,0, 0, '楼龄', 'buildingAge', 0.00, 5.00000000, 5, 0, '', '', '2024-12-18 20:16:12', '楼龄', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 380,0, 379, '5年以内', 'buildingAge', 10.00, 5.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '<=5', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 381,0, 379, '5-10年', 'buildingAge', 9.00, 5.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '>5,<=10', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 382,0, 379, '10-15年', 'buildingAge', 7.00, 5.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '>10,<=15', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 383,0, 379, '15年以上', 'buildingAge', 6.00, 5.00000000, 4, 0, '', '', '2024-12-26 20:21:38', '>15', 1);




INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 384,0, 0, '楼盘品质-外观造型', 'buildingExterior', 0.00, 5.00000000, 6, 0, '', '', '2024-12-18 20:16:12', '楼盘品质-外观造型', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 385,0, 384, '外墙材料为铝合金、优质石材、高档陶瓷、玻璃幕墙', 'buildingExterior', 10.00, 5.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '外墙材料为铝合金、优质石材、高档陶瓷、玻璃幕墙', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 386,0, 384, '外墙材料为涂料，砖，马赛克等且无脱落，裂痕', 'buildingExterior', 8.00, 5.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '外墙材料为涂料，砖，马赛克等且无脱落，裂痕', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 387,0, 384, '外墙有脱落，裂痕等情形', 'buildingExterior', 0.00, 5.00000000, 3, 0, '', '', '2024-12-18 20:16:13', '外墙有脱落，裂痕等', 1);




INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 388,0, 0, '楼盘品质-大堂', 'buildingLobby', 0.00, 5.00000000, 7, 0, '', '', '2024-12-18 20:16:12', '楼盘品质-大堂', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 389,0, 388, '大堂挑高7米以上且材料为石材、木材、玻璃装饰、陶瓷、不锈钢等', 'buildingLobby', 10.00, 5.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '7米以上,石材、木材、玻璃装饰、陶瓷、不锈钢等', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 390,0, 388, '大堂挑高不足7米且材料为石材、木材、玻璃装饰、陶瓷、不锈钢等', 'buildingLobby', 8.00, 5.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '不足7米,石材、木材、玻璃装饰、陶瓷、不锈钢等', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 391,0, 388, '大堂不满足上述精装修范畴', 'buildingLobby', 0.00, 5.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '大堂不满足上述精装修范畴', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 392,0, 388, '大堂墙面污秽，剥落，梯厅环境脏乱差', 'buildingLobby', 0.00, 5.00000000, 4, 0, '', '', '2024-12-18 20:16:13', '墙面污秽，剥落，脏乱差', 1);




INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 393,0, 0, '楼盘品质-地下车库', 'buildingGarage', 0.00, 5.00000000, 8, 0, '', '', '2024-12-18 20:16:12', '楼盘品质-地下车库', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 394,0, 393, '地下车库有2层及以上', 'buildingGarage', 10.00, 5.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '地下车库有2层及以上', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (395, 0, 393, '地下车库有1层', 'buildingGarage', 8.00, 5.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '地下车库有1层', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 396,0, 393, '无地下车库', 'buildingGarage', 0.00, 5.00000000, 3, 0, '', '', '2024-12-18 20:16:12', '无地下车库', 1);




INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 397,1, 0, '地理位置', 'buildingLocation', 0.00, 30.00000000, 2, 0, '', '', '2024-12-18 20:16:12', '地理位置', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 398,1, 397, '城市核心区', 'buildingLocation', 10.00, 30.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '城市核心区', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 399,1, 397, '城市非核心区', 'buildingLocation', 7.00, 30.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '城市非核心区', 1);



INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 400,1, 0, '楼层数', 'buildingNumber', 0.00, 15.00000000, 3, 0, '', '', '2024-12-18 20:16:12', '楼层数', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 401,1, 400, '30层（含）以上', 'buildingNumber', 10.00, 15.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '>=30', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 402,1, 400, '20（含）~30层', 'buildingNumber', 9.00, 15.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '>=20,<30', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 403,1, 400, '15（含）~20层', 'buildingNumber', 8.00, 15.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '>=15,<20', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 404,1, 400, '15层以下', 'buildingNumber', 6.00, 15.00000000, 4, 0, '', '', '2024-12-26 20:21:38', '<15', 1);


INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 405,1, 0, '月租金', 'buildingPrice', 0.00, 10.00000000, 4, 0, '', '', '2024-12-18 20:16:12', '月租金', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 406,1, 405, '超过城市租金均价30%以上', 'buildingPrice', 10.00, 10.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '>1.3', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 407,1, 405, '超过城市租金均价的30%以内', 'buildingPrice', 9.00, 10.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '>1,<=1.3', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 408,1, 405, '低于城市租金均价的30%以内', 'buildingPrice', 7.00, 10.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '>=0.7,<1', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 409,1, 405, '低于城市租金均价30%以上', 'buildingPrice', 5.00, 10.00000000, 4, 0, '', '', '2024-12-26 20:21:38', '<0.7', 1);


INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 410,1, 0, '楼盘品质-外观造型', 'buildingExterior', 0.00, 10.00000000, 5, 0, '', '', '2024-12-18 20:16:12', '楼盘品质-外观造型', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 411,1, 410, '外墙材料为铝合金、优质石材、高档陶瓷、玻璃幕墙', 'buildingExterior', 10.00, 10.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '外墙材料为铝合金、优质石材、高档陶瓷、玻璃幕墙', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 412,1, 410, '外墙材料为涂料，砖，马赛克等且无脱落，裂痕', 'buildingExterior', 7.00, 10.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '外墙材料为涂料，砖，马赛克等且无脱落，裂痕', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 413,1, 410, '外墙有脱落，裂痕等情形', 'buildingExterior', 0.00, 10.00000000, 3, 0, '', '', '2024-12-18 20:16:12', '外墙有脱落，裂痕等情形', 1);


INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 414,1, 0, '楼盘品质-侯梯厅', 'buildingHall', 0.00, 10.00000000, 6, 0, '', '', '2024-12-18 20:16:12', '楼盘品质-侯梯厅', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 415,1,  414, '材料为石材、木材、玻璃装饰、陶瓷、不锈钢等', 'buildingHall', 10.00, 10.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '材料为石材、木材、玻璃装饰、陶瓷、不锈钢等', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 416,1,  414, '材料不满足上述精装修范畴，但干净整洁', 'buildingHall', 5.00, 10.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '材料不满足上述精装修范畴，但干净整洁', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 417,1,  414, '墙面污秽，剥落，梯厅环境脏乱差', 'buildingHall', 0.00, 10.00000000, 3, 0, '', '', '2024-12-18 20:16:12', '墙面污秽，剥落，梯厅环境脏乱差', 1);


INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 418,1, 0, '楼盘品质-地下车库', 'buildingGarage', 0.00, 10.00000000, 7, 0, '', '', '2024-12-18 20:16:12', '楼盘品质-地下车库', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 419,1, 418, '地下车库有2层及以上', 'buildingGarage', 10.00, 10.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '地下车库有2层及以上', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 420,1, 418, '地下车库有1层', 'buildingGarage', 5.00, 10.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '地下车库有1层', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 421,1, 418, '无地下车库', 'buildingGarage', 0.00, 10.00000000, 3, 0, '', '', '2024-12-18 20:16:12', '无地下车库', 1);


INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 422,1, 0, '楼龄', 'buildingAge', 0.00, 15.00000000, 1, 0, '', '', '2024-12-18 20:16:12', '楼龄', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 423,1, 422, '5年以内', 'buildingAge', 10.00, 15.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '<5', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 424,1, 422, '5-10年', 'buildingAge', 9.00, 15.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '>=5,<10', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 425,1, 422, '10-15年', 'buildingAge', 7.00, 15.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '>=10,<15', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 426,1, 422, '15年以上', 'buildingAge', 6.00, 15.00000000, 4, 0, '', '', '2024-12-26 20:21:38', '>=15', 1);




INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 427,2, 0, '综合体品牌', 'buildingBrand', 0.00, 50.00000000, 1, 0, '', '', '2024-12-18 20:16:12', '综合体品牌', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 428,2, 427, '全国连锁品牌', 'buildingBrand', 10.00, 50.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '全国连锁品牌', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 429,2, 427, '区域连锁品牌', 'buildingBrand', 7.00, 50.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '区域连锁品牌', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 430,2, 427, '其他', 'buildingBrand', 5.00, 50.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '其他', 1);




INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (431,2, 0, '地理位置', 'buildingLocation', 0.00, 30.00000000, 2, 0, '', '', '2024-12-18 20:16:12', '地理位置', 1);
INSERT INTO `building_parameter` (`id`, `building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (432, 2, 431, '城市核心区', 'buildingLocation', 10.00, 30.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '城市核心区', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 433,2, 431, '城市非核心区', 'buildingLocation', 7.00, 30.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '城市非核心区', 1);



INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 434,2, 0, '点评评分', 'buildingRating', 0.00, 20.00000000, 3, 0, '', '', '2024-12-18 20:16:12', '点评评分', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 435,2, 434, '4.7分以上', 'buildingRating', 10.00, 20.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '>=4.7', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 436,2, 434, '4.5-4.7', 'buildingRating', 8.00, 20.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '>=4.5,<4.7', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 437,2, 434, '4-4.5', 'buildingRating', 7.00, 20.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '>=4,<4.5', 1);
INSERT INTO `building_parameter` ( `id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES ( 438,2, 434, '4分以下', 'buildingRating', 5.00, 20.00000000, 4, 0, '', '', '2024-12-26 20:21:38', '<4', 1);



INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (439,3, 0, '楼龄', 'buildingAge', 0.00, 15.00000000, 1, 0, '', '', '2024-12-18 20:16:12', '楼龄', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (440,3, 439, '5年以内', 'buildingAge', 10.00, 15.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '<5', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (441,3, 439, '5-10年', 'buildingAge', 9.00, 15.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '>=5,<10', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (442,3, 439, '10-15年', 'buildingAge', 7.00, 15.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '>=10,<15', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (443,3, 439, '15年以上', 'buildingAge', 6.00, 15.00000000, 4, 0, '', '', '2024-12-26 20:21:38', '>=15', 1);


INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (444,3, 0, '地理位置', 'buildingLocation', 0.00, 20.00000000, 2, 0, '', '', '2024-12-18 20:16:12', '地理位置', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (445,3, 444, '城市核心区', 'buildingLocation', 10.00, 20.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '城市核心区', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (446,3, 444, '城市非核心区', 'buildingLocation', 7.00, 20.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '城市非核心区', 1);



INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (447,3, 0, '楼层数', 'buildingNumber', 0.00, 20.00000000, 3, 0, '', '', '2024-12-18 20:16:12', '楼层数', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (448,3, 447, '30层（含）以上', 'buildingNumber', 10.00, 20.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '>=30', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (449,3, 447, '20（含）~30层', 'buildingNumber', 9.00, 20.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '>=20,<30', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (450,3, 447, '15（含）~20层', 'buildingNumber', 8.00, 20.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '>=15,<20', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (451,3, 447, '15层以下', 'buildingNumber', 6.00, 20.00000000, 4, 0, '', '', '2024-12-26 20:21:38', '<15', 1);



INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (452,3, 0, '入驻率', 'buildingSettled', 0.00, 15.00000000, 4, 0, '', '', '2024-12-18 20:16:12', '入驻率', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (453,3, 452, '80%或以上', 'buildingSettled', 10.00, 15.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '>=0.8', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (454,3, 452, '70%-80%', 'buildingSettled', 8.00, 15.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '>=0.7,<0.8', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (455,3, 452, '50%-70%', 'buildingSettled', 7.00, 15.00000000, 3, 0, '', '', '2024-12-26 20:21:38', '>=0.5,<0.7', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (456,3, 452, '低于50%', 'buildingSettled', 5.00, 15.00000000, 4, 0, '', '', '2024-12-26 20:21:38', '<0.5', 1);


INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (457,3, 0, '楼盘品质-外观造型', 'buildingExterior', 0.00, 15.00000000, 5, 0, '', '', '2024-12-18 20:16:12', '楼盘品质-外观造型', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (458,3, 457, '外墙材料为铝合金、优质石材、高档陶瓷、玻璃幕墙', 'buildingExterior', 10.00, 15.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '外墙材料为铝合金、优质石材、高档陶瓷、玻璃幕墙', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (459,3, 457, '外墙材料为涂料，砖，马赛克等且无脱落，裂痕', 'buildingExterior', 7.00, 15.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '外墙材料为涂料，砖，马赛克等且无脱落，裂痕', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (460,3, 457, '外墙有脱落，裂痕等情形', 'buildingExterior', 0.00, 15.00000000, 3, 0, '', '', '2024-12-18 20:16:12', '外墙有脱落，裂痕等情形', 1);


INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (461,3, 0, '楼盘品质-侯梯厅', 'buildingHall', 0.00, 15.00000000, 6, 0, '', '', '2024-12-18 20:16:12', '楼盘品质-侯梯厅', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (462,3, 461, '材料为石材、木材、玻璃装饰、陶瓷、不锈钢等', 'buildingHall', 10.00, 15.00000000, 1, 0, '', '', '2024-12-26 20:21:38', '材料为石材、木材、玻璃装饰、陶瓷、不锈钢等', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (463,3, 461, '材料不满足上述精装修范畴，但干净整洁', 'buildingHall', 5.00, 15.00000000, 2, 0, '', '', '2024-12-26 20:21:38', '材料不满足上述精装修范畴，但干净整洁', 1);
INSERT INTO `building_parameter` (`id`,`building_type`, `parent_id`, `parameter_name`, `parameter_code`, `parameter_score`, `weight_value`, `sort`, `deleted`, `create_by`, `update_by`, `update_time`, `parameter_rule`, `data_flag`)
VALUES (464,3, 461, '墙面污秽，剥落，梯厅环境脏乱差', 'buildingHall', 0.00, 15.00000000, 3, 0, '', '', '2024-12-18 20:16:12', '墙面污秽，剥落，梯厅环境脏乱差', 1);









