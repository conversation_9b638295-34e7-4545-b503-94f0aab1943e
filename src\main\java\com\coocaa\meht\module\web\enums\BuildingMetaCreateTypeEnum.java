package com.coocaa.meht.module.web.enums;

import lombok.Getter;

/**
 * 字典类型枚举
 */
@Getter
public enum BuildingMetaCreateTypeEnum {
    
    PERSONAL("0038-1", "人工提交"),
    SYSTEM("0038-2", "系统爬取");

    private final String code;
    private final String desc;

    BuildingMetaCreateTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static BuildingMetaCreateTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BuildingMetaCreateTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举
     */
    public static BuildingMetaCreateTypeEnum getByDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (BuildingMetaCreateTypeEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
    }
} 