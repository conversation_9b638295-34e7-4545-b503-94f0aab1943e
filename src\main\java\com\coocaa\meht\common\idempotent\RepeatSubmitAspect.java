package com.coocaa.meht.common.idempotent;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.IReturnCode;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.utils.JsonUtils;
import com.google.protobuf.ServiceException;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;

/**
 * 防止重复提交(参考美团GTIS防重系统)
 *
 * <AUTHOR>
 *
 */
@Aspect
@Component
public class RepeatSubmitAspect {

    /**
     * 防重提交 redis key
     */
    private static final String REPEAT_SUBMIT_KEY = "repeat_submit:";
    private static final String TOKEN_HEADER_NAME = "token";
    private static final ThreadLocal<String> KEY_CACHE = new ThreadLocal<>();

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Before("@annotation(repeatSubmit)")
    public void doBefore(JoinPoint point, RepeatSubmit repeatSubmit) throws Throwable {
        // 如果注解不为0 则使用注解数值
        long interval = repeatSubmit.timeUnit().toMillis(repeatSubmit.interval());

        if (interval < 1000) {
            throw new ServiceException("重复提交间隔时间不能小于'1'秒");
        }
        HttpServletRequest request = ServletUtils.getRequest();
        String nowParams = argsArrayToString(point.getArgs());

        // 请求地址（作为存放cache的key值）
        assert request != null;
        String url = request.getRequestURI();
        // token
        String token = StringUtils.trimToEmpty(request.getHeader(TOKEN_HEADER_NAME));
        // 唯一标识指定key + md5( url + token + 消息头).substring(0, 8)
        String submitKey = SecureUtil.md5(url + token + nowParams).substring(0, 8);
        String cacheRepeatKey = REPEAT_SUBMIT_KEY + submitKey;
        if (Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(cacheRepeatKey, "", Duration.ofMillis(interval)))) {
            KEY_CACHE.set(cacheRepeatKey);
        } else {
            String message = repeatSubmit.message();
            throw new ServerException(message);
        }
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(repeatSubmit)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, RepeatSubmit repeatSubmit, Object jsonResult) {
        if (jsonResult instanceof Result<?> r) {
            try {
                // 成功则不删除redis数据 保证在有效时间内无法重复提交
                if (Objects.equals(r.getCode(), Result.ok().getCode())) {
                    return;
                }
                redisTemplate.delete(KEY_CACHE.get());
                return;
            } finally {
                KEY_CACHE.remove();
            }
        }
        if (jsonResult instanceof ResultTemplate<?> r) {
            try {
                // 成功则不删除redis数据 保证在有效时间内无法重复提交
                if (Objects.equals(r.getCode(), IReturnCode.Default.SUCCESS.getErrCode())) {
                    return;
                }
                redisTemplate.delete(KEY_CACHE.get());
            } finally {
                KEY_CACHE.remove();
            }
        }
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "@annotation(repeatSubmit)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, RepeatSubmit repeatSubmit, Exception e) {
        redisTemplate.delete(KEY_CACHE.get());
        KEY_CACHE.remove();
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray) {
        StringJoiner params = new StringJoiner(" ");
        if (ArrayUtil.isEmpty(paramsArray)) {
            return params.toString();
        }
        for (Object o : paramsArray) {
            if (ObjectUtil.isNotNull(o) && !isFilterObject(o)) {
                params.add(JsonUtils.toJson(o));
            }
        }
        return params.toString();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return MultipartFile.class.isAssignableFrom(clazz.getComponentType());
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Object value : collection) {
                return value instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Object value : map.values()) {
                return value instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }

}
