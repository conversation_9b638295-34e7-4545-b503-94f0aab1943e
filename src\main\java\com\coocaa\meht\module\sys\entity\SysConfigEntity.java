package com.coocaa.meht.module.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 参数管理
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("sys_config")
public class SysConfigEntity extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 名称
     */
    @TableField("`name`")
    private String name;
    /**
     * 键
     */
    @TableField("`key`")
    private String key;
    /**
     * 值
     */
    @TableField("`value`")
    private String value;
    /**
     * 状态：0启用，1禁用
     */
    @TableField("`status`")
    private Integer status;
    /**
     * 顺序
     */
    private Integer sort;
    /**
     * 备注
     */
    private String remark;
}