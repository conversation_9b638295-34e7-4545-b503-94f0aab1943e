package com.coocaa.meht.module.web.dto;


import com.coocaa.meht.module.sys.entity.SysFileEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 评论数据传输对象
 * @since 2025-04-29
 */
@Data
public class CommentDTO {
    
    /**
     * 评论ID
     */
    private Integer id;
    
    /**
     * 业务类型（1楼宇、2价格申请）
     */
    private Integer businessType;
    
    /**
     * 业务ID
     */
    private String businessId;
    
    /**
     * 评论内容
     */
    private String content;
    
    /**
     * 通知用户列表
     */
    private List<String> notifiedUsers;
    
    /**
     * 附件ID列表
     */
    private List<SysFileEntity> attachments;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建人姓名
     */
    private String createByName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
} 