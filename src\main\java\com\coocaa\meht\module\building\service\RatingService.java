package com.coocaa.meht.module.building.service;

import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.module.building.dto.RatingPageDTO;
import com.coocaa.meht.module.building.handler.AiRatingHandler;
import com.coocaa.meht.module.building.vo.RatingPageVO;
import com.coocaa.meht.module.building.vo.RatingVO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.dto.req.BuildingLevelReq;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;

/**
 * 楼宇评级业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13
 */
public interface RatingService {


    /**
     * 评级编辑
     *
     * @param param 评级参数
     * @return
     */
    BuildingRatingEntity edit(RatingApplyDto param);

    /**
     * 评级草稿
     *
     * @param param 评级参数
     * @return
     */
    BuildingRatingEntity draft(RatingApplyDto param);

    /**
     * 评级列表
     *
     * @param param 评级参数
     * @return
     */
    PageResult<RatingPageVO> page(RatingPageDTO param);

    /**
     * 更新楼宇评级等级
     * @param req
     */
    void updateLevel(BuildingLevelReq req);

    /**
     * 楼宇评级详情
     * @param businessKey
     * @param type
     * @return
     */
    RatingVO info(String businessKey, Integer type, String ratingVersion);

    /**
     * 删除草稿
     * @param buildingNo
     */
    void deleteDraft(String buildingNo);

    /**
     * 评级审核通过
     * @param businessKey
     */
    void handleApproved(String businessKey);

    /**
     * 评级审核拒绝
     * @param businessKey
     */
    void handleRejected(String businessKey);

    /**
     * 评级审核撤销
     * @param businessKey
     */
    void handleRevoke(String businessKey);

    /**
     * 完善评级中检查
     * @param buildingNo
     * @return
     */
    Boolean checkComplete(String buildingNo);

    /**
     * 撤回未发起审批流数据
     * @param type
     * @param bizCode
     */
    void revokeUnsubmitted(AiRatingHandler.Type type, String bizCode);

}
