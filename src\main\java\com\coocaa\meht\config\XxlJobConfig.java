package com.coocaa.meht.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class XxlJobConfig {

    @Value("${xxlJobAdminAddresses}")
    private String adminAddresses;

    @Value("${xxlJobExecutorAppName}")
    private String appName;

    @Value("${xxlJobExecutorIp}")
    private String ip;

    @Value("${xxlJobExecutorPort}")
    private int port;

    @Value("${xxlJobExecutorLogpath}")
    private String logPath;

    @Value("${xxlJobExecutorLogretentiondays}")
    private int logRetentionDays;
    @Value("${xxlJobAccessToken}")
    private String xxlJobToken;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(appName);
        xxlJobSpringExecutor.setIp(ip);
        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
        xxlJobSpringExecutor.setAccessToken(xxlJobToken);
        return xxlJobSpringExecutor;
    }

}
