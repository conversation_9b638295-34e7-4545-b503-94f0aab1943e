package com.coocaa.meht.module.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 角色
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("sys_role")
public class SysRoleEntity extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField("`code`")
    private String code;
    /**
     * 角色名称
     */
    @TableField("`name`")
    private String name;
    /**
     * 状态：0启用，1禁用
     */
    @TableField("`status`")
    private Integer status;
    /**
     * 数据权限
     */
    private String authority;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标识
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;
}