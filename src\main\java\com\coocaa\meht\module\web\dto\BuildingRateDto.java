package com.coocaa.meht.module.web.dto;

import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by fengke on 2024/11/22.
 */

@Data
public class BuildingRateDto {

    private String buildingNo;
    private String buildingName;

    private String buildingTypeName;

    private String mapCity;

    private String mapAddress;

    private BigDecimal buildingScore;

    private String projectLevel;

    private String projectAiLevel;

    private AddressDto mapConvertAddress;

    private AddressDto scoreDetailAddress;

    private AddressDto mobileConverAddress;

    /**
     * 评级申请列表
     */
    private List<PriceApplySimpleDto> priceApplies;


}
