package com.coocaa.meht;

import com.coocaa.meht.module.web.service.impl.CrmDataHandlerServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
public class DemoApplicationTests {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private CrmDataHandlerServiceImpl crmDataHandlerService;
    @Test
    public void contextLoads() {
    }


    //@Resource
    //private ArkService arkService;

    @Test
    public void homeResponse() {
//		System.out.println("\n----- standard request -----");
//		final List<ChatMessage> messages = new ArrayList<>();
//		final ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build();
//		final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("常见的十字花科植物有哪些？").build();
//		messages.add(systemMessage);
//		messages.add(userMessage);
//
//		ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
//				.model("<YOUR_ENDPOINT_ID>")
//				.messages(messages)
//				.build();
//
//		arkService.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));
//
//		// shutdown service
//		arkService.shutdownExecutor();
    }
}
