package com.coocaa.meht.converter;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 对字典翻译
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-02
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CodeNameHelper {
    private final FeignAuthorityRpc authorityRpc;

    /**
     * 翻译城市区县名字
     */
    public Map<Integer, String> getCityMapping(Collection<Integer> cityIds) {
        return getIdNameMapping(cityIds, authorityRpc::listCityByIds);
    }

    /**
     * 翻译用户名字
     */
    public Map<Integer, String> getUserMapping(Collection<Integer> userIds) {
        return getIdNameMapping(userIds, authorityRpc::listUserByIds);
    }

    /**
     * 翻译行业名字
     */
    public Map<String, String> getIndustryMapping(Collection<String> codes) {
        return getCodeNameMapping(codes, authorityRpc::listIndustryByCodes);
    }


    /**
     * 通过字典翻译名字
     */
    public Map<String, String> getDictMapping(Collection<String> codes) {
        return getCodeNameMapping(codes, authorityRpc::listDictByCodes);
    }


    /**
     * 调用权限系统翻译数据
     */
    private Map<Integer, String> getIdNameMapping(Collection<Integer> ids,
                                                  Function<Collection<Integer>, ResultTemplate<List<CodeNameVO>>> function) {
        if (CollectionUtils.isEmpty(ids) || Objects.isNull(function)) {
            return Collections.emptyMap();
        }

        // 去掉空ID
        Set<Integer> uniqueIds = ids.stream().filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(uniqueIds)) {
            return Collections.emptyMap();
        }

        return Optional.ofNullable(function.apply(uniqueIds))
                .map(ResultTemplate::getData)
                .map(items -> items.stream().collect(Collectors.toMap(CodeNameVO::getId, CodeNameVO::getName)))
                .orElse(Collections.emptyMap());
    }

    /**
     * 调用权限系统翻译数据
     */
    private Map<String, String> getCodeNameMapping(Collection<String> codes,
                                                   Function<Collection<String>, ResultTemplate<List<CodeNameVO>>> function) {
        if (CollectionUtils.isEmpty(codes) || Objects.isNull(function)) {
            return Collections.emptyMap();
        }

        // 去掉空编码
        Set<String> uniqueCodes = codes.stream().filter(StringUtils::isNotBlank).
                collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Collections.emptyMap();
        }

        return Optional.ofNullable(function.apply(uniqueCodes))
                .map(ResultTemplate::getData)
                .map(items -> items.stream().collect(Collectors.toMap(CodeNameVO::getCode, CodeNameVO::getName)))
                .orElse(Collections.emptyMap());
    }
}
