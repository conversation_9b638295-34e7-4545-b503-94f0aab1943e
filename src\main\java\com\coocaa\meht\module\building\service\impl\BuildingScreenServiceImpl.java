package com.coocaa.meht.module.building.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.converter.CodeNameHelper;
import com.coocaa.meht.module.building.convert.BuildingScreenConvert;
import com.coocaa.meht.module.building.dao.BuildingScreenMapper;
import com.coocaa.meht.module.building.entity.BuildingScreenEntity;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.building.vo.BuildingScreenVO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingScreenServiceImpl extends ServiceImpl<BuildingScreenMapper, BuildingScreenEntity> implements BuildingScreenService {

    private final LargeScreenProperties largeScreenProperties;

    private final CodeNameHelper codeNameHelper;

    @Override
    public void saveOrUpdate(String buildingNo, String submitCoefficient, RatingApplyDto param) {
        BuildingScreenEntity existEntity = lambdaQuery()
                .eq(BuildingScreenEntity::getBuildingRatingNo, buildingNo)
                .last("limit 1")
                .one();

        BuildingScreenEntity screenEntity = creteScreenEntity(buildingNo, submitCoefficient, param);

        if (Objects.nonNull(existEntity)) {
            screenEntity.setId(existEntity.getId());
            updateById(screenEntity);
        } else {
            save(screenEntity);
        }
    }

    public BuildingScreenEntity creteScreenEntity(String buildingNo, String submitCoefficient, RatingApplyDto param) {
        BuildingScreenEntity screenEntity = new BuildingScreenEntity();
        screenEntity
                .setBuildingRatingNo(buildingNo)
                .setSubmitCoefficient(StrUtil.isNotBlank(submitCoefficient) ? new BigDecimal(submitCoefficient) : null)
                .setSpec(param.getSpec())
                .setTotalBuildingCount(param.getTotalBuildingCount())
                .setCompanyCount(param.getCompanyCount())
                .setElevatorCount(param.getElevatorCount())
                .setBuildingSpacing(param.getBuildingSpacing())
                .setBuildingCeilingHeight(param.getBuildingCeilingHeight())
                .setSpecialDesc(param.getSpecialDesc());
        return screenEntity;
    }

    @Override
    public BuildingScreenVO getBuildingScreenByNo(String buildingNo) {
        BuildingScreenEntity entity = this.lambdaQuery()
                .eq(BuildingScreenEntity::getBuildingRatingNo, buildingNo)
                .one();
        if (Objects.isNull(entity)) {
            return new BuildingScreenVO();
        }
        return BuildingScreenConvert.INSTANCE.toBuildingScreenVO(entity);

    }

    @Override
    public Boolean isLargeScreen(BuildingScreenVO vo) {
        if (Objects.isNull(vo) || StrUtil.isBlank(vo.getSpec())) {
            return false;
        }
        return isLargeScreen(vo.getSpec());
    }

    public Boolean isLargeScreen(String spec) {
        if (StrUtil.isBlank(spec)) {
            return false;
        }
        List<String> codeList = JSON.parseArray(spec, String.class);
        List<String> largeDeviceKey = largeScreenProperties.getLargeDictKey();
        return CollUtil.isNotEmpty(CollectionUtil.intersection(largeDeviceKey, codeList));
    }

    @Override
    public Boolean isLargeScreen(BuildingRatingEntity ratingEntity) {
        if (Objects.isNull(ratingEntity)) {
            return false;
        }

        BuildingScreenEntity screenEntity = lambdaQuery()
                .eq(BuildingScreenEntity::getBuildingRatingNo, ratingEntity.getBuildingNo())
                .last("limit 1")
                .one();

        if (Objects.isNull(screenEntity)) {
            return false;
        }

        return isLargeScreen(screenEntity.getSpec());
    }

}
