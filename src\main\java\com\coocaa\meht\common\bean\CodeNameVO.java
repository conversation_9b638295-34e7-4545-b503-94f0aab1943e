package com.coocaa.meht.common.bean;

import com.coocaa.meht.module.web.enums.UserTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CodeNameVO {
    private Integer id;
    @Schema(description = "编码")
    private String code;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "工号")
    private String wno;

    /**
     * 用户类型 [1:内部用户, 2:外部代理商]
     */
    private Integer type;

    public boolean isInner() {
        return UserTypeEnum.INNER.getType().equals(type);
    }
}
