package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.entity.PointPicPriceSnapshotEntity;

import java.util.List;

/**
 * <p>
 * 点位价格图片快照表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface IPointPicPriceSnapshotService extends IService<PointPicPriceSnapshotEntity> {

    List<PointPicPriceSnapshotEntity> listByPointIds(List<Integer> pointIds);
} 