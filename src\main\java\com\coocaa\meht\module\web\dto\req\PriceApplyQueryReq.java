package com.coocaa.meht.module.web.dto.req;

import com.coocaa.meht.common.PageReq;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PriceApplyQueryReq extends PageReq {
    /**
     * 查询关键字 楼宇名称
     */
    @Schema(description = "查询关键字 楼宇名称")
    private String buildingName;

    /**
     * 状态 [0:草稿 1:待审核 2:已审核 3:审核不通过]
     */
    @Schema(description = "状态列表[0:草稿 1:待审核 2:已审核 3:审核不通过]")
    private List<Integer> status;

    /**
     * 申请人工号（创建人）
     */
    @Schema(description = "申请人工号（创建人）")
    private List<String> createBy;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(type = "LocalDateTime", description = "开始时间:yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(type = "LocalDateTime", description = "结束时间:yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
