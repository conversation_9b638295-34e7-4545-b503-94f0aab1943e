package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.web.dto.BuildingStatusChangeLogWithRatingDto;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.coocaa.meht.module.web.vo.kanban.StatusChangeVO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @file BuildingStatusChangeLogDao
 * @date 2025/1/2 14:07
 * @description 楼宇/商机状态变更记录
 */
public interface BuildingStatusChangeLogMapper extends BaseMapper<BuildingStatusChangeLogEntity> {
    /**
     * 根据status查询log记录
     */
    List<BuildingStatusChangeLogWithRatingDto> selectByStatus(@Param("type") String type,
                                                              @Param("status") String status,
                                                              @Param("timeRangeMap") Map<String, LocalDateTime> timeRangeMap,
                                                              @Param("projectLevelList") List<String> projectLevelList);

    /**
     * 查询评级申请涉及的log记录
     */
    List<BuildingStatusChangeLogWithRatingDto> selectOfRatingApplicationRecords(@Param("type") String type,
                                                                                @Param("status") String status,
                                                                                @Param("timeRangeMap") Map<String, LocalDateTime> timeRangeMap);

    List<StatusChangeVO> getBuildingRatingStatusChangeList(@Param("param") StatusChangeVO param);
    List<StatusChangeVO> getProjectStatusChangeList(@Param("param") StatusChangeVO param);
}
