package com.coocaa.meht.utils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @ClassName AesUtil.java
 * @Description 对称加解密
 * @createTime 2022年03月28日 12:00:00
 */
public class AesUtils {
    /**
     * 加密秘钥 默认设置一个 官网的获取秘钥的方式（如上注释代码）每次启动都会变化
     */
    private static final byte[] KEY = {13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13};
    private static final SymmetricCrypto AES = new SymmetricCrypto(SymmetricAlgorithm.AES, KEY);

    /**
     * 加密为16进制
     *
     * @param content
     * @return
     */
    public static String encryptHex(String content) {
        return StringUtils.isBlank(content) ? null : AES.encryptHex(content);
    }

    /**
     * 解密为字符串
     *
     * @param encryptHex
     * @return
     */
    public static String decryptStr(String encryptHex) {
        return StringUtils.isBlank(encryptHex) ? null : AES.decryptStr(encryptHex, CharsetUtil.CHARSET_UTF_8);
    }
}
