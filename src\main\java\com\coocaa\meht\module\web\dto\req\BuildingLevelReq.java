package com.coocaa.meht.module.web.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 楼宇等级请求类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
@Data
public class BuildingLevelReq {

    /**
     * 楼宇ID
     */
    @NotNull(message = "楼宇ID不能为空")
    @Schema(description = "楼宇ID", type = "Integer")
    private Integer id;

    /**
     * 楼宇编号
     */
    @Schema(description = "楼宇编号", type = "String", example = "1")
    @NotBlank(message = "楼宇编号不能为空")
    private String buildingNo;

    /**
     * 楼宇等级
     */
    @Schema(description = "楼宇等级", type = "String", example = "A")
    @NotBlank(message = "楼宇等级不能为空")
    private String level;

    /**
     * 描述说明
     */
    @Schema(description = "描述", type = "String")
    private String description;

    /**
     * 附件地址
     */
    @Schema(description = "附件地址")
    private List<String> attachmentUrls;

}
