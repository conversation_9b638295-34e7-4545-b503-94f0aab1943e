package com.coocaa.meht.module.web.service.property;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.module.web.dto.property.PropertyCompanyParam;
import com.coocaa.meht.module.web.entity.PropertyCompanyEntity;
import com.coocaa.meht.module.web.vo.CompanyInfoVO;
import com.coocaa.meht.module.web.vo.property.PropertyCompanyVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface IPropertyCompanyService extends IService<PropertyCompanyEntity> {

    /**
     * 新增物业公司
     * @param param
     * @return
     */
    Integer saveCompany(PropertyCompanyParam param);


    /**
     * 物业列表
     * @param name
     * @return
     */
    List<PropertyCompanyVO> getCompany(String name);

    /**
     * 天眼查公司信息
     * @param word
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<CompanyInfoVO> getCompanyInfoList(String word, Integer pageNum, Integer pageSize);

    /**
     * 物业联系人信息
     * @param projectCode
     * @return
     */
    PropertyCompanyVO getPerson(String projectCode);

    /**
     * 获取行业末级列表
     * @return
     */
    List<CodeNameVO> getSecondIndustry();
}
