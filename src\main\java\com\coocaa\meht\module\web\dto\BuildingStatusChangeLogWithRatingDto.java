package com.coocaa.meht.module.web.dto;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @file BuildingStatusChangeLogWithRatingDto
 * @date 2025/1/6 15:00
 * @description 楼宇状态变更日志与评分信息
 */

@Data
public class BuildingStatusChangeLogWithRatingDto {
    /**
     * ID
     */
    private Long id;

    /**
     * 数据类型
     */
    private String type = "";

    /**
     * 数据类型子类型
     */
    private Integer subType = 0;

    /**
     * 业务ID (楼宇,客户,商机,...)
     */
    private Long bizId = 0L;

    /**
     * 业务编码 (楼宇,客户,商机,...)
     */
    private String bizCode = "";

    /**
     * 业务状态(字典xx)
     */
    private String status = "";

    /**
     * 状态变更时间
     */
    private LocalDateTime changeTime = null;

    /**
     * 状态变更操作人
     */
    private Long operator = 0L;

    /**
     * 状态变更操作人工号
     */
    private String operatorWno = "";

    /**
     * 状态变更操作人姓名
     */
    private String operatorName = "";

    /**
     * 补充内容
     */
    private String content;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Byte deleteFlag = 0;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    // ====================================
    /**
     * 楼宇级别
     */
    private String projectLevel = "";

    /**
     * 所在区域编码
     */
    private String mapCity = "";
}
