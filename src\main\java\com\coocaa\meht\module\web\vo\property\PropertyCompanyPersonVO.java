package com.coocaa.meht.module.web.vo.property;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
public class PropertyCompanyPersonVO {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 物业公司id
     */
    @Schema(description = "物业公司id", type = "Int", example = "1")
    private Integer companyId;

    /**
     * 角色
     */
    @Schema(description = "联系人角色", type = "String", example = "项目负责人")
    private String role;

    /**
     * 姓名
     */
    @Schema(description = "联系人姓名", type = "String", example = "张三")
    private String name;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码", type = "String", example = "183****4368")
    private String phone;

    /**
     * 电子邮箱
     */
    @Schema(description = "电子邮箱", type = "String", example = "1")
    private String email;
}
