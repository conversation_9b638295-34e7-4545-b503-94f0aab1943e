package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.BuildingPropertyCompanyParam;
import com.coocaa.meht.module.web.entity.BuildingPropertyCompanyEntity;
import com.coocaa.meht.module.web.vo.BuildingPropertyCompanyVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-06
 */
public interface IBuildingPropertyCompanyService extends IService<BuildingPropertyCompanyEntity> {

    /**
     * 根据项目code获取物业信息
     * @param code
     * @param buildingNo
     * @return
     */
    List<BuildingPropertyCompanyVO> getDetail(String code, String buildingNo);

    /**
     * 物业公司关联商机
     * @param param
     * @return
     */
    Integer saveBuildingProperty(BuildingPropertyCompanyParam param);
}
