<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.coocaa.meht.module.web.dao.BuildingRatingDao">

    <select id="selectBuildingRatingUninvolvedLogRecord"
            resultType="com.coocaa.meht.module.web.entity.BuildingRatingEntity">
        SELECT id, building_no, building_status, map_city, project_level, update_time
        FROM building_rating
        WHERE building_no NOT IN (SELECT DISTINCT biz_code
        FROM building_status_change_log
        WHERE type = #{type}
        AND `status` = #{status}
        AND delete_flag = 0)
        <if test="projectLevelList != null and projectLevelList.size() > 0">
            AND project_level IN
            <foreach collection="projectLevelList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="timeRangeMap!=null and timeRangeMap.size() > 0">
            AND update_time BETWEEN #{timeRangeMap.start} AND #{timeRangeMap.end}
        </if>

    </select>
    <select id="selectOfRatingApplicationRecordsInvolvedLogRecord"
            resultType="com.coocaa.meht.module.web.entity.BuildingRatingEntity">
        SELECT id, building_no, building_status, map_city, approve_time
        FROM building_rating
        WHERE building_no NOT IN (SELECT DISTINCT biz_code
        FROM building_status_change_log
        WHERE type = #{type}
        AND `status` = #{status}
        AND delete_flag = 0)
        <if test="timeRangeMap!=null and timeRangeMap.size() > 0">
            AND approve_time BETWEEN #{timeRangeMap.start} AND #{timeRangeMap.end}
        </if>
    </select>

    <select id="findBuildingMeta" resultType="com.coocaa.meht.module.web.entity.BuildingRatingEntity">
        SELECT br.* FROM `building_rating` br LEFT JOIN building_meta bm on  br.=bm.building_name WHERE br.status = 0 and br.building_status = 1 and bm.building_meta_no is null;
    </select>

    <select id="approveList" resultType="com.coocaa.meht.module.web.entity.BuildingRatingEntity">
        SELECT DISTINCT
        br.id,
        br.building_no,
        br.building_status,
        br.STATUS,
        br.building_type,
        br.building_name,
        br.map_no,
        br.map_province,
        br.map_city,
        br.map_region,
        br.map_address,
        br.map_latitude,
        br.map_longitude,
        br.map_ad_code,
        br.authentication_start,
        br.authentication_end,
        br.authentication_period,
        br.freeze_start,
        br.freeze_end,
        br.freeze_period,
        br.building_desc,
        br.submit_user,
        br.submit_time,
        br.building_score,
        br.project_level,
        br.first_floor_exclusive,
        br.first_floor_share,
        br.negative_first_floor,
        br.negative_two_floor,
        br.two_floor_above,
        br.third_floor_below,
        br.approve_user,
        br.approve_time,
        br.approve_desc,
        br.reject_user,
        br.reject_time,
        br.reject_desc,
        br.building_exterior_pic,
        br.building_lobby_pic,
        br.building_elevator_pic,
        br.building_gate_pic,
        br.building_installation_pic,
        br.building_hall_pic,
        br.building_lobby_env_pic,
        br.deleted,
        br.create_by,
        br.create_time,
        br.update_by,
        br.update_time,
        br.crm_push_status,
        br.customer_id,
        br.crm_flow_id,
        br.crm_flow_name,
        br.project_ai_level,
        br.building_ai_score,
        br.sign_status,
        br.target_point_count,
        br.top_level
        FROM
        building_rating br
        LEFT JOIN screen_approve_record sar on br.building_no = sar.natural_key
                                                   and sar.scene_type = 1  and  sar.approve_user = #{approveUser}
                                                   and sar.approve_level != 0 and sar.approve_time is not null
        WHERE br.deleted = 0
        and ( br.`status` in (1,2,3,4)
        <if test="name != null and name != ''">
            and br.building_name like concat('%',#{name},'%')
        </if>
        <if test="approveUser != null and approveUser != ''">
            and ( br.approve_user = #{approveUser} or sar.approve_user = #{approveUser})
        </if> )
        or
        ( br.`status` = 0
        <if test="name != null and name != ''">
            and br.building_name like concat('%',#{name},'%')
        </if>
        <if test="approveUser != null and approveUser != ''">
            and sar.approve_user = #{approveUser}
        </if> )
        ORDER BY br.approve_time DESC , br.reject_time DESC
    </select>

    <select id="listHighSeaCustomer" resultType="com.coocaa.meht.module.web.vo.HighSeaCustomerVO">
        SELECT
        br.`id`,
        br.building_name,
        br.building_type,
        br.map_address,
        br.enter_sea_time
        from building_rating br
        <where>
            and br.high_sea_flag = 1
            and br.`status` in (0, 1, 2)
            <if test="condition.buildingName != null and condition.buildingName!=''">
                and br.building_name like concat('%',#{condition.buildingName},'%')
            </if>
            <if test='condition.cities != null and condition.cities.size() > 0'>
                and br.map_city in
                <foreach collection='condition.cities' item='city' open='(' separator=',' close=')'>
                    #{city}
                </foreach>
            </if>
        </where>
        order by br.enter_sea_time desc, br.id desc
    </select>

    <select id="getHighSeaCities" resultType="java.lang.String">
        select
        temp.map_city
        from(
            select count(map_city) num, map_city
            from building_rating
            where status in (0, 1, 2) and high_sea_flag = 1
            group by map_city
            order by num desc
        ) temp
    </select>

    <select id="ratingPage" resultType="com.coocaa.meht.module.building.vo.RatingPageVO">
        select
        id,
        building_no,
        1 as type,
        map_province,
        map_city,
        map_region,
        map_address,
        building_name,
        building_type,
        COALESCE(NULLIF(project_review_level, ''), project_level) as projectLevel,
        status,
        submit_time,
        create_by,
        update_time,
        rating_version,
        submit_user
        from building_rating
        <where>
            and deleted = 0
            and (status != 5 or submit_user = #{submitUser})
            <if test="buildingName != null and buildingName!=''">
                and building_name like concat('%',#{buildingName},'%')
            </if>
            <if test='statuses != null and statuses.size() > 0'>
                and status in
                <foreach collection='statuses' item='status' open='(' separator=',' close=')'>
                    #{status}
                </foreach>
            </if>
            <if test='cities != null and cities.size() > 0'>
                and map_city in
                <foreach collection='cities' item='city' open='(' separator=',' close=')'>
                    #{city}
                </foreach>
            </if>
            <if test='userCodes != null and userCodes.size() > 0'>
                and create_by in
                <foreach collection='userCodes' item='useCode' open='(' separator=',' close=')'>
                    #{useCode}
                </foreach>
            </if>
            <if test="startTime != null and startTime != ''">
                and submit_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and submit_time &lt;= #{endTime}
            </if>
        </where>

        union all

        select
        cr.id,
        cr.complete_rating_no as buildingNo,
        2 as type,
        cr.map_province,
        cr.map_city,
        cr.map_region,
        cr.map_address,
        cr.building_name,
        cr.building_type,
        COALESCE(NULLIF(cr.project_review_level, ''), cr.project_level) as projectLevel,
        cr.status,
        cr.submit_time,
        cr.create_by,
        cr.update_time,
        cr.`version` as ratingVersion,
        cr.submit_user
        from complete_rating cr
        <where>
            and cr.delete_flag = 0
            and (cr.status != 5 or cr.submit_user = #{submitUser})
            <if test="buildingName != null and buildingName!=''">
                and cr.building_name like concat('%',#{buildingName},'%')
            </if>
            <if test='statuses != null and statuses.size() > 0'>
                and cr.status in
                <foreach collection='statuses' item='status' open='(' separator=',' close=')'>
                    #{status}
                </foreach>
            </if>
            <if test='cities != null and cities.size() > 0'>
                and cr.map_city in
                <foreach collection='cities' item='city' open='(' separator=',' close=')'>
                    #{city}
                </foreach>
            </if>
            <if test='userCodes != null and userCodes.size() > 0'>
                and cr.create_by in
                <foreach collection='userCodes' item='useCode' open='(' separator=',' close=')'>
                    #{useCode}
                </foreach>
            </if>
            <if test="startTime != null and startTime != ''">
                and cr.submit_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and cr.submit_time &lt;= #{endTime}
            </if>
        </where>
        order by update_time desc
    </select>

</mapper>