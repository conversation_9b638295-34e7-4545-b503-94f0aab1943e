package com.coocaa.meht.module.web.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("building_city_rent")
public class BuildingCityRentEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 城市编码
     */
    private String adCode;

    /**
     * 租金
     */
    private BigDecimal officeRent;

    /**
     * 日租金
     */
    private BigDecimal officeRentDaily;

    /**
     * 楼宇类型
     */
    private Integer buildingType;
}
