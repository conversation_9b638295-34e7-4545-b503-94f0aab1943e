<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.web.dao.PriceApplyDao">

    <select id="selectIncentivePrice" resultType="com.coocaa.meht.module.web.vo.PriceApplyDevicePointJoinVO">
        SELECT b.apply_code, c.incentive_price, a.point_code
        FROM price_apply_device_point a
                 JOIN price_apply b ON a.apply_id = b.id
                 JOIN price_apply_device c ON a.price_apply_device_id = c.id
        WHERE a.point_code IN
        <foreach item="item" index="index" collection="pointCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectIncentivePriceByApplyCode" resultType="com.coocaa.meht.module.dataimport.pojo.PriceApplyPointDTO">
        SELECT
            a.apply_id applyId,
            a.price_apply_device_id deviceId,
            a.point_code pointCode,
            b.device_size pointSize
        FROM
            price_apply_device_point a
                LEFT JOIN point b ON a.point_code = b.`code`

        where
            b.device_size != ''
    </select>
</mapper>
