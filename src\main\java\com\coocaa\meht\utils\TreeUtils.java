package com.coocaa.meht.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 树形结构工具
 */
public class TreeUtils {

    /**
     * 任意列表 转 树
     *
     * @param list 注意里面的子节点(如：childNode)字段不能为空
     * @return
     * <AUTHOR>
     */
    public static <T> List<T> toTree(List<T> list, Function<T, ?> getId,
                                     Function<T, ?> getParentId,
                                     Function<T, List<T>> getChildNode) {
        List<T> tree = new ArrayList<>();
        Map<String, List<T>> mapList = list.stream().collect(Collectors.groupingBy(k -> Converts.toStr(getId.apply(k))));
        for (T item : list) {
            String parentId = Converts.toStr(getParentId.apply(item));
            if (parentId == null || parentId.length() == 0) {
                tree.add(item);
            } else {
                List<T> parent = mapList.get(parentId);
                if (parent == null || parent.isEmpty()) {
                    tree.add(item);
                } else {
                    for (T t : parent) {
                        getChildNode.apply(t).add(item);
                    }
                }
            }
        }
        return tree;
    }

}