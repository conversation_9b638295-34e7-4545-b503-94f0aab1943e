package com.coocaa.meht.job;

import com.coocaa.meht.module.web.service.ttc.ITtcService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * 创视ttc指标定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class TtcJob {
    private final ITtcService ttcService;

    /**
     * 创视ttc指标定时任务
     */
    @XxlJob("ttcSync")
    public void syncTtcData() {
        XxlJobHelper.log("创视ttc指标定时任务开始");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("创视ttc指标定时任务");
        try {
            ttcService.syncTtcData(null, null);
            String message = String.format("创视ttc指标定时任务, 耗时: %dms", stopWatch.getTotalTimeMillis());
            log.info(message);
            XxlJobHelper.log(message);
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            String errorMessage = "创视ttc指标定时任务失败";
            log.error(errorMessage, e);
            XxlJobHelper.handleFail(errorMessage + ": " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            stopWatch.stop();
        }
    }
}
