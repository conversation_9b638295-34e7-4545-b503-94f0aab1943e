<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.web.dao.BusinessOpportunityMapper">


    <select id="getPriceApplyBusiness"
            resultType="com.coocaa.meht.module.web.dto.BusinessProjectDto">
        SELECT
        bo.`code` as businessCode,
        bo.`name` as businessName,
        br.building_no as buildingNo,
        br.building_name as buildingName,
        COALESCE(NULLIF(br.project_review_level, ''), br.project_level) as projectLevel,
        br.map_province as mapProvince,
        br.map_city as mapCity,
        br.building_type as buildingType,
        map_region as mapRegion
        FROM
        business_opportunity bo
        LEFT JOIN building_rating br ON bo.building_no = br.building_no
        <where>
            <if test="status !=null and status.size() > 0">
                and bo.status in
                <foreach collection="status" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="name !=null and name !=''">
                and bo.name like concat('%',#{name},'%')
            </if>
            <if test="allUser == false">
                and bo.submit_user = #{userCode}
            </if>
        </where>

        order by COALESCE(NULLIF(br.project_review_level, ''), br.project_level) desc, bo.create_time ASC
        limit 10

    </select>

    <select id="getBusinessOpportunityWithRatingDtoList"
            resultType="com.coocaa.meht.module.web.dto.BusinessOpportunityWithRatingDto">
        SELECT
        bo.id,
        bo.`code`,
        bo.`name`,
        bo.`status`,
        bo.`create_time` as createTime,
        br.building_no as buildingNo,
        br.map_city as mapCity
        FROM
        business_opportunity bo
        JOIN building_rating br ON bo.building_no = br.building_no
        WHERE br.deleted = 0
        <if test="timeRangeMap!=null and timeRangeMap.size() > 0">
            AND bo.create_time BETWEEN #{timeRangeMap.start} AND #{timeRangeMap.end}
        </if>
    </select>

    <select id="getEarliestChangeTime" resultType="java.time.LocalDateTime">
        select
        bl.change_time
        from business_opportunity bo
        join building_status_change_log bl on bl.type = '0042-4' and bl.biz_id = bo.id
        where
        bo.building_no = #{buildingNo}
        and bl.`status` = #{status}
        and bl.delete_flag = 0
        order by bl.change_time asc
        limit 1;
    </select>

    <select id="list" resultType="com.coocaa.meht.module.web.entity.BusinessOpportunityEntity">
        SELECT
        bo.id,
        bo.`code`,
        bo.`name`,
        bo.building_no,
        bo.`status`,
        bo.submit_user
        from business_opportunity bo
        join building_rating br on bo.building_no = br.building_no
        <where>
            and bo.submit_user != ''
            <if test="name != null and name != ''">
                and bo.name like concat('%',#{name},'%')
            </if>
            <if test="statuses != null and statuses.size() > 0">
                and bo.status in
                <foreach collection="statuses" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test='cities != null and cities.size() > 0'>
                and br.map_city in
                <foreach collection='cities' item='city' open='(' separator=',' close=')'>
                    #{city}
                </foreach>
            </if>
            <if test='userCodes != null and userCodes.size() > 0'>
                and bo.submit_user in
                <foreach collection='userCodes' item='useCode' open='(' separator=',' close=')'>
                    #{useCode}
                </foreach>
            </if>
            <if test="excludeUserCode != null and excludeUserCode != ''">
                and bo.submit_user != #{excludeUserCode}
            </if>
        </where>
        order by bo.create_time desc
    </select>


</mapper>