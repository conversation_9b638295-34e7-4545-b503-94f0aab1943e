package com.coocaa.meht.utils;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Pattern;

@Log4j2
public class Converts {
    private final static Pattern HUMP_PATTERN = Pattern.compile("([a-z])([A-Z])");

    /**
     * 转换为Integer<br>
     *
     * @param obj
     * @return
     */
    public static Integer toInt(Object obj) {
        return toInt(obj, null);
    }

    /**
     * 转换为int<br>
     *
     * @param obj
     * @param def
     * @return
     */
    public static Integer toInt(Object obj, Integer def) {
        if (obj == null) {
            return def;
        }
        if (obj instanceof Integer) {
            return (Integer) obj;
        }
        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        }
        try {
            return Integer.valueOf(obj.toString());
        } catch (Exception e) {
            return def;
        }
    }

    /**
     * 转换为long<br>
     *
     * @param obj
     * @return
     */
    public static Long toLong(Object obj) {
        return toLong(obj, null);
    }

    /**
     * 转换为long<br>
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static Long toLong(Object obj, Long defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        if (obj instanceof Long) {
            return (Long) obj;
        }
        if (obj instanceof Number) {
            return ((Number) obj).longValue();
        }
        try {
            return Long.valueOf(obj.toString());
        } catch (Exception e) {
            try {
                return new BigDecimal(obj.toString()).longValue();
            } catch (Exception e2) {
                return defaultValue;
            }
        }
    }

    /**
     * 转换为double<br>
     *
     * @param obj
     * @return 结果
     */
    public static Double toDouble(Object obj) {
        return toDouble(obj, null);
    }

    /**
     * 转换为double<br>
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static Double toDouble(Object obj, Double defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        if (obj instanceof Double) {
            return (Double) obj;
        }
        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        }
        try {
            return Double.valueOf(obj.toString());
        } catch (Exception e) {
            try {
                return new BigDecimal(obj.toString()).doubleValue();
            } catch (Exception e2) {
                return defaultValue;
            }
        }
    }

    /**
     * 转换为Float<br>
     *
     * @param obj
     * @return 结果
     */
    public static Float toFloat(Object obj) {
        return toFloat(obj, null);
    }

    /**
     * 转换为Float<br>
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static Float toFloat(Object obj, Float defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        if (obj instanceof Float) {
            return (Float) obj;
        }
        if (obj instanceof Number) {
            return ((Number) obj).floatValue();
        }
        try {
            return Float.valueOf(obj.toString());
        } catch (Exception e) {
            try {
                return new BigDecimal(obj.toString()).floatValue();
            } catch (Exception e2) {
                return defaultValue;
            }
        }
    }

    /**
     * 转换为Number<br>
     *
     * @param obj
     * @return
     */
    public static Number toNumber(Object obj) {
        return toNumber(obj, null);
    }

    /**
     * 转换为Number<br>
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static Number toNumber(Object obj, Number defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        if (obj instanceof Number) {
            return (Number) obj;
        }
        try {
            return NumberFormat.getInstance().parse(obj.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 转换为boolean<br>
     *
     * @param obj
     * @return
     */
    public static Boolean toBoolean(Object obj) {
        return toBoolean(obj, null);
    }

    /**
     * 转换为boolean<br>
     * 支持：(true/false), (yes/no), (1/0)<br>
     *
     * @param obj
     * @param def
     * @return
     */
    public static Boolean toBoolean(Object obj, Boolean def) {
        if (obj == null) {
            return def;
        }
        if (obj instanceof Boolean) {
            return (Boolean) obj;
        }
        switch (obj.toString()) {
            case "true":
            case "yes":
            case "1":
                return true;
            case "false":
            case "no":
            case "0":
                return false;
            default:
                return def;
        }
    }

    /**
     * 转换为字符串<br>
     *
     * @param obj
     * @return
     */
    public static String toStr(Object obj) {
        return toStr(obj, null);
    }

    /**
     * 转换为字符串<br>
     *
     * @param map
     * @param key
     * @return
     */
    public static String toStr(Map<?, ?> map, String key) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        return toStr(map.get(key), null);
    }

    /**
     * 转换为字符串<br>
     *
     * @param map
     * @param key
     * @param def
     * @return
     */
    public static String toStr(Map<?, ?> map, String key, String def) {
        if (map == null || map.isEmpty()) {
            return def;
        }
        return toStr(map.get(key), def);
    }

    /**
     * 转换为字符串<br>
     *
     * @param obj
     * @param def 默认值
     * @return
     */
    public static String toStr(Object obj, String def) {
        return null == obj ? def : obj instanceof String ? (String) obj : obj.toString();
    }

    /**
     * 转换为字符<br>
     *
     * @param obj
     * @return
     */
    public static Character toChar(Object obj) {
        return toChar(obj, null);
    }

    /**
     * 转换为字符<br>
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static Character toChar(Object obj, Character defaultValue) {
        if (null == obj) {
            return defaultValue;
        }
        if (obj instanceof Character) {
            return (Character) obj;
        }
        String str = obj.toString();
        return str.isEmpty() ? defaultValue : str.charAt(0);
    }

    /**
     * 转换为Integer数组<br>
     * 转换失败的值为null
     *
     * @param str
     * @param split 分隔符
     * @return
     */
    public static Integer[] toIntArray(String str, String split) {
        if (StringUtils.isEmpty(str)) {
            return new Integer[]{};
        }
        String[] arr = str.split(split);
        Integer[] intArr = new Integer[arr.length];
        for (int i = 0; i < arr.length; i++) {
            Integer v = toInt(arr[i], null);
            intArr[i] = v;
        }
        return intArr;
    }

    /**
     * @param obj
     * @param func
     * @param <T>
     * @return
     */
    public static <T> List<T> toList(Object obj, Function<Object, T> func) {
        if (obj instanceof Iterable) {
            Iterable<?> it = (Iterable<?>) obj;
            List<T> list = new ArrayList<>();
            it.forEach(ele -> {
                T apply = func.apply(ele);
                if (Objects.nonNull(apply))
                    list.add(apply);
            });
            return list;
        }
        if (obj != null && obj.getClass().isArray()) {
            List<T> lsit2 = new ArrayList<>();
            int length = Array.getLength(obj);
            for (int i = 0; i < length; i++) {
                T apply = func.apply(Array.get(obj, i));
                if (Objects.nonNull(apply)) {
                    lsit2.add(apply);
                }
            }
            return lsit2;
        }
        return Collections.emptyList();
    }

    /**
     * 转换为BigInteger<br>
     *
     * @param obj
     * @return
     */
    public static BigInteger toBigInteger(Object obj) {
        return toBigInteger(obj, null);
    }

    /**
     * 转换为BigInteger<br>
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static BigInteger toBigInteger(Object obj, BigInteger defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        if (obj instanceof BigInteger) {
            return (BigInteger) obj;
        }
        try {
            return new BigInteger(obj.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 转换为BigDecimal<br>
     *
     * @param obj
     * @return 结果
     */
    public static BigDecimal toBigDecimal(Object obj) {
        return toBigDecimal(obj, null);
    }

    /**
     * 转换为BigDecimal<br>
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static BigDecimal toBigDecimal(Object obj, BigDecimal defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        if (obj instanceof BigDecimal) {
            return (BigDecimal) obj;
        }
        try {
            return new BigDecimal(obj.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 转换为Enum对象<br>
     *
     * @param enumClass Enum 的 Class
     * @param obj
     * @return
     */
    public static <E extends Enum<E>> E toEnum(Class<E> enumClass, Object obj) {
        return toEnum(enumClass, obj, null);
    }

    /**
     * 转换为Enum对象<br>
     *
     * @param enumClass    Enum 的 Class
     * @param obj
     * @param defaultValue
     * @return
     */
    public static <E extends Enum<E>> E toEnum(Class<E> enumClass, Object obj, E defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        if (enumClass.isAssignableFrom(obj.getClass())) {
            return (E) obj;
        }
        try {
            return Enum.valueOf(enumClass, obj.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * byte 转 字符串<br>
     *
     * @param bytes
     * @return
     */
    public static String byteToStr(byte[] bytes) {
        return byteToStr(bytes, StandardCharsets.UTF_8);
    }

    /**
     * byte 转 字符串<br>
     *
     * @param bytes
     * @param charset
     * @return
     */
    public static String byteToStr(byte[] bytes, String charset) {
        return byteToStr(bytes, StringUtils.isEmpty(charset) ? StandardCharsets.UTF_8 : Charset.forName(charset));
    }

    /**
     * byte 转 字符串<br>
     *
     * @param bytes
     * @param charset
     * @return
     */
    public static String byteToStr(byte[] bytes, Charset charset) {
        return bytes == null ? null : null == charset ? new String(bytes) : new String(bytes, charset);
    }

    /**
     * 数字金额大写转换<br>
     *
     * @param n
     * @return
     */
    public static String digitUppercase(double n) {
        String[] fraction = {"角", "分"};
        String[] digit = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
        String[][] unit = {{"元", "万", "亿"}, {"", "拾", "佰", "仟"}};
        String head = n < 0 ? "负" : "";
        n = Math.abs(n);
        String s = "";
        for (int i = 0; i < fraction.length; i++) {
            s += (digit[(int) (Math.floor(n * 10 * Math.pow(10, i)) % 10)] + fraction[i])
                    .replaceAll("(零.)+", "");
        }
        if (s.length() < 1) {
            s = "整";
        }
        int integerPart = (int) Math.floor(n);
        for (int i = 0; i < unit[0].length && integerPart > 0; i++) {
            String p = "";
            for (int j = 0; j < unit[1].length && n > 0; j++) {
                p = digit[integerPart % 10] + unit[1][j] + p;
                integerPart = integerPart / 10;
            }
            s = p.replaceAll("(零.)*零$", "").replaceAll("^$", "零") + unit[0][i] + s;
        }
        return head + s.replaceAll("(零.)*零元", "元")
                .replaceFirst("(零.)+", "")
                .replaceAll("(零.)+", "零")
                .replaceAll("^整$", "零元整");
    }

    /**
     * 驼峰 转 下划线<br>
     *
     * @param str
     * @return
     */
    public static String humpToUnderline(String str) {
        return HUMP_PATTERN.matcher(str).replaceAll("$1_$2").toLowerCase();
    }

    /**
     * bean 转 map <br>
     *
     * @param bean
     * @return
     */
    public static Map<String, Object> beanToMap(Object bean) {
        return beanToMap(bean, null);
    }

    /**
     * bean 转 map <br>
     *
     * @param bean
     * @param ignore 忽略的字段
     * @return
     */
    public static Map<String, Object> beanToMap(Object bean, String... ignore) {
        Class<?> clazz = bean.getClass();
        List<Field> allFields = new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()));
        Class<?> clazzSuper = clazz.getSuperclass();
        while (clazzSuper != Object.class) {
            allFields.addAll(Arrays.asList(clazzSuper.getDeclaredFields()));
            clazzSuper = clazzSuper.getSuperclass();
        }
        Map<String, Object> map = new HashMap<>();
        if (allFields.isEmpty()) {
            return map;
        }
        HashSet<String> ignoreSet = new HashSet<>();
        if (ignore != null && ignore.length > 0) {
            ignoreSet.addAll(Arrays.asList(ignore));
        }
        for (Field f : allFields) {
            if (!Modifier.isStatic(f.getModifiers())) {
                f.setAccessible(true);
                String fieldname = f.getName();
                if (ignoreSet.isEmpty() || !ignoreSet.contains(fieldname)) {
                    try {
                        Object valObj = f.get(bean);
                        if (valObj != null) {
                            map.put(fieldname, valObj);
                        }
                    } catch (Exception e) {
                        log.error("获取字段值异常, fieldName=" + fieldname, e);
                    }
                }
            }
        }
        return map;
    }

    /**
     * Map 转换 Map<String, Map> 比较差异<br>
     *
     * @param oldMap 旧数据
     * @param newMap 新数据
     * @return
     */
    public static Map<String, Map<String, Object>> compare(Map<String, Object> oldMap, Map<String, Object> newMap) {
        Map<String, Object> oldctt = new LinkedHashMap<>(), newctt = new LinkedHashMap<>();
        Map<String, Map<String, Object>> oldNewMap = new HashMap<>();
        if (CollectionUtils.isEmpty(oldMap)) {
            oldNewMap.put("old", oldctt);
            oldNewMap.put("new", CollectionUtils.isEmpty(newMap) ? newctt : newMap);
            return oldNewMap;
        }
        if (CollectionUtils.isEmpty(newMap)) {
            oldNewMap.put("old", CollectionUtils.isEmpty(oldMap) ? oldctt : oldMap);
            oldNewMap.put("new", newctt);
            return oldNewMap;
        }
        for (String k : newMap.keySet()) {
            Object oldOjb = oldMap.get(k), newOjb = newMap.get(k);
            if (!Objects.equals(oldOjb, newOjb)) {
                if (oldOjb instanceof String || newOjb instanceof String) {
                    if (toStr(oldOjb, "").trim().equals(toStr(newOjb, "").trim())) {
                        continue;
                    }
                }
                oldctt.put(k, oldOjb);
                newctt.put(k, newOjb);
            }
        }
        oldNewMap.put("old", oldctt);
        oldNewMap.put("new", newctt);
        return oldNewMap;
    }

}
