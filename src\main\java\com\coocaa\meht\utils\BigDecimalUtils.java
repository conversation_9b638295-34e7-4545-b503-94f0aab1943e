package com.coocaa.meht.utils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * 数字操作工具
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-14
 **/
public final class BigDecimalUtils {
    /**
     * 相加 +
     *
     * @param numbers 被加数/加数
     * @return 多个数之和
     */
    public static BigDecimal add(BigDecimal... numbers) {
        if (ArrayUtils.isEmpty(numbers)) {
            return BigDecimal.ZERO;
        }
        return Arrays.stream(numbers).map(BigDecimalUtils::toZeroIfNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 相加 +
     *
     * @param numbers 被加数/加数
     * @return 多个数之和
     */
    public static BigDecimal add(Collection<BigDecimal> numbers) {
        if (CollectionUtils.isEmpty(numbers)) {
            return BigDecimal.ZERO;
        }
        return numbers.stream().map(BigDecimalUtils::toZeroIfNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 相减 -
     *
     * @param numbers 被减数/减数
     * @return 第一个数减去后面所有数
     */
    public static BigDecimal sub(BigDecimal... numbers) {
        if (ArrayUtils.isEmpty(numbers)) {
            return BigDecimal.ZERO;
        }

        // 用第一个数减去后面的所有数
        BigDecimal result = toZeroIfNull(numbers[0]);
        for (int i = 1; i < numbers.length; i++) {
            result = result.subtract(toZeroIfNull(numbers[i]));
        }
        return result;
    }

    /**
     * 相乘 ×
     *
     * @param a 被乘数
     * @param b 乘数
     * @return 两数之积
     */
    public static BigDecimal multiply(BigDecimal a, BigDecimal b) {
        return toZeroIfNull(a).multiply(toZeroIfNull(b));
    }

    /**
     * 相除 ÷
     *
     * @param a 被除数
     * @param b 除数
     * @return 两数之商
     */
    public static BigDecimal divide(BigDecimal a, BigDecimal b) {
        b = toZeroIfNull(b);
        return eq(BigDecimal.ZERO, b) ? BigDecimal.ZERO : toZeroIfNull(a).divide(b);
    }

    /**
     * 相除 ÷
     *
     * @param a            被除数
     * @param b            除数
     * @param scale        小数位
     * @param roundingMode 四舍五入
     * @return 两数之商
     */
    public static BigDecimal divide(BigDecimal a, BigDecimal b, int scale, RoundingMode roundingMode) {
        b = toZeroIfNull(b);
        return eq(BigDecimal.ZERO, b) ? BigDecimal.ZERO : toZeroIfNull(a).divide(b, scale, roundingMode);
    }

    /**
     * 等于 =
     *
     * @param a 比较的数之一
     * @param b 比较的数之二
     * @return true: 两数相等
     */
    public static boolean eq(BigDecimal a, BigDecimal b) {
        return compare(a, b, r -> r == 0);
    }

    /**
     * 不等于 !=
     *
     * @param a 比较的数之一
     * @param b 比较的数之二
     * @return true: 两数不相等
     */
    public static boolean neq(BigDecimal a, BigDecimal b) {
        return compare(a, b, r -> r != 0);
    }


    /**
     * 大于 >
     *
     * @param a 比较的数之一
     * @param b 比较的数之二
     * @return true: a > b
     */
    public static boolean gt(BigDecimal a, BigDecimal b) {
        return compare(a, b, r -> r > 0);
    }

    /**
     * 小于 <
     *
     * @param a 比较的数之一
     * @param b 比较的数之二
     * @return true: a < b
     */
    public static boolean lt(BigDecimal a, BigDecimal b) {
        return compare(a, b, r -> r < 0);
    }


    /**
     * 大于等于 >=
     *
     * @param a 比较的数之一
     * @param b 比较的数之二
     * @return a >= b
     */
    public static boolean ge(BigDecimal a, BigDecimal b) {
        return compare(a, b, r -> r >= 0);
    }

    /**
     * 小于等于 <=
     *
     * @param a 比较的数之一
     * @param b 比较的数之二
     * @return a <= b
     */
    public static boolean le(BigDecimal a, BigDecimal b) {
        return compare(a, b, r -> r <= 0);
    }


    /**
     * 将空对象转换成0
     *
     * @param number 需要处理的数字
     * @return 如果对象为空则转换成0, 否则保持原数
     */
    public static BigDecimal toZeroIfNull(BigDecimal number) {
        return Optional.ofNullable(number).orElse(BigDecimal.ZERO);
    }

    /**
     * 比较两个数的大小，返回较大的数
     *
     * @param a 需要比较的两个数 1
     * @param b 需要比较的两个数 2
     * @return 较大的数
     */
    public static BigDecimal max(BigDecimal a, BigDecimal b) {
        return gt(a, b) ? a : b;
    }

    /**
     * 比较两个数的大小，返回较小的数
     *
     * @param a 比较的数之一
     * @param b 比较的数之二
     * @return 较小的数
     */
    public static BigDecimal min(BigDecimal a, BigDecimal b) {
        return lt(a, b) ? a : b;
    }

    /**
     * 去掉小数点后面多余的零，并平铺的方式输出数字串 (非科学计数)
     *
     * @param number 需要处理的数字
     * @return 原值输出为字符串
     */
    public static String toString(BigDecimal number) {
        if (Objects.isNull(number)) {
            return "0";
        }
        return number.stripTrailingZeros().toPlainString();
    }


    /**
     * 对两数进行比较
     *
     * @param a        比较的数之一
     * @param b        比较的数之二
     * @param function 对比较结果进行判断
     * @return 判断结果
     */
    private static boolean compare(BigDecimal a, BigDecimal b, Function<Integer, Boolean> function) {
        return function.apply(toZeroIfNull(a).compareTo(toZeroIfNull(b)));
    }
}

