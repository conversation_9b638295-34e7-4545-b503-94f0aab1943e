package com.coocaa.meht.common.bean.proxy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @file CsBrandPointQueryParam
 * @date 2025/1/21 10:07
 * @description 创视 - 点位坐标查询参数
 */
@Data
public class CsBrandPointQueryParam {
    /**
     * token
     */
    @Schema(description = "token", hidden = true)
    private String token;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String city;

    /**
     * 百度坐标地址（经纬度之间逗号分隔）
     */
    @Schema(description = "百度坐标地址（经纬度之间逗号分隔）")
    private String address;

    /**
     * 距离n公里数（超过我方系统配置的最大距离，以我方系统配置的最大距离为准，目前系统配置的100公里）
     */
    @Schema(description = "距离n公里数（超过我方系统配置的最大距离，以我方系统配置的最大距离为准，目前系统配置的100公里）")
    private Integer distance;
}
