package com.coocaa.meht.config;


import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

// @Configuration
@Deprecated
public class RedissonConfiguration {

    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private String port;

    @Value("${spring.redis.password}")
    private String password;

    @Value("${spring.redis.database}")
    private Integer database;

    // @Bean
    public RedissonClient redissonClient() {
        String address = "redis://"+host+":"+port;
        Config config = new Config();
        config.useSingleServer().
                setAddress(address).
                setPassword(password).
                setDatabase(database);
        return Redisson.create(config);
    }

}
