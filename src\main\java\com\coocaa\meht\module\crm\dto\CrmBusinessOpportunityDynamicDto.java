package com.coocaa.meht.module.crm.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @file CrmBusinessOpportunityDynamicDto
 * @date 2024/12/30 19:42
 * @description 商机动态
 */

@Data
@Accessors(chain = true)
public class CrmBusinessOpportunityDynamicDto {
    @Data
     public static class CreateUser {
        /** userId */
        private String userId;

        /** realname */
        private String realname;

        /** status */
        private Integer status;
    }

    /** id */
    private String id;

    /** category */
    private String category;

    /** activityId */
    private String activityId;

    /** activityType */
    private Integer activityType;

    /** activityTypeId */
    private String activityTypeId;

    /** activityTypeName */
    private String activityTypeName;

    /** content */
    private Object content;

    /** createUser */
    private CreateUser createUser;

    /** 推进时间 */
    private LocalDateTime createTime;
}
