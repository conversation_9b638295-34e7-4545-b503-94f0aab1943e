package com.coocaa.meht;

import com.coocaa.meht.job.BusinessJob;
import com.coocaa.meht.utils.RsaExample;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @since 2024/12/25
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
classes = MehtApplication.class)
@ActiveProfiles("local")
public class BuildingMetaTest {
    @Autowired
    private BusinessJob businessJob;

    @Autowired
    private RsaExample rsaExample;

    @Test
    public void testResurrectedTimeout(){
        businessJob.resurrectedTimeout();
    }

    @Test
    public void getMoblie(){
        System.out.println(rsaExample.encryptByPrivate("***********"));
    }
}
