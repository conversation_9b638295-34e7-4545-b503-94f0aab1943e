package com.coocaa.meht.module.dataimport.pojo;

import com.coocaa.meht.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格申请
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10
 */
@Data
public class PriceApplyVO {
    /**
     * 楼宇编码
     */
    private String buildingNo;

    /**
     * 楼宇名称
     */
    private String buildingName;

    /**
     * 商机编码
     */
    private String businessCode;

    /**
     * 合同年限
     */
    private BigDecimal contractDuration;

    /**
     * 合同总金额(元)
     */
    private BigDecimal totalAmount;

    /**
     * 付款方式 [一次性、一年付、半年付、季度付、其他]
     */
    private String paymentType;

    /**
     * 是否有押金 [0:否, 1:是]
     */
    private Integer isDeposit;

    /**
     * 押金金额(元)
     */
    private BigDecimal depositAmount;

    /**
     * 用户工号
     */
    private String wno;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    /**
     * 终端设备列表
     */
    private List<PriceApplyDeviceVO> devices;
}
