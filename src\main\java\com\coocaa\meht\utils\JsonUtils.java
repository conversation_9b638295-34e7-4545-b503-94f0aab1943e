package com.coocaa.meht.utils;

import com.coocaa.meht.common.exception.JsonException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * JSON 工具类
 */
public class JsonUtils {
    private static final Logger log = LoggerFactory.getLogger(JsonUtils.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(Long.class, ToStringSerializer.instance);
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateUtils.DTF_DT));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateUtils.DTF_DATE));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateUtils.DTF_TIME));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateUtils.DTF_DT));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateUtils.DTF_DATE));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateUtils.DTF_TIME));
        objectMapper.setTimeZone(TimeZone.getDefault());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.setDateFormat(new SimpleDateFormat(DateUtils.DATE_TIME_PATTERN));
        objectMapper.registerModule(javaTimeModule).registerModule(new ParameterNamesModule());
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public static String toJson(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            throw new JsonException(String.format("序列化Json异常: %s", object), e);
        }
    }

    public static <T> T fromJson(String text, Class<T> clazz) {
        try {
            return objectMapper.readValue(text, clazz);
        } catch (Exception e) {
            throw new JsonException(String.format("反序列化Json异常: %s", text), e);
        }
    }

    public static <T> T fromJsonNon(String text, Class<T> clazz) {
        if (StringUtils.hasText(text)) {
            try {
                return objectMapper.readValue(text, clazz);
            } catch (Exception e) {
                log.error(String.format("反序列化Json异常: %s", text), e);
            }
        }
        return null;
    }

    public static <T> T fromJson(String text, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(text, typeReference);
        } catch (Exception e) {
            throw new JsonException(String.format("反序列化Json异常: %s", text), e);
        }
    }

    public static <T> T fromJson(String text, Class<?> parametrized, Class<?>... parameterClasses) {
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(parametrized, parameterClasses);
        try {
            return objectMapper.readValue(text, javaType);
        } catch (Exception e) {
            throw new JsonException(String.format("反序列化Json异常: %s", text), e);
        }
    }

    public static <T> List<T> fromArray(String text, Class<T> clazz) {
        if (!StringUtils.hasText(text)) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(text, objectMapper.getTypeFactory().constructCollectionType(ArrayList.class, clazz));
        } catch (Exception e) {
            throw new JsonException(String.format("反序列化Json异常: %s", text), e);
        }
    }

    public static Map<String, Object> fromMap(String text) {
        if (!StringUtils.hasText(text)) {
            return Collections.emptyMap();
        }
        try {
            JavaType javaType = objectMapper.getTypeFactory().constructMapType(LinkedHashMap.class, String.class, Object.class);
            return objectMapper.readValue(text, javaType);
        } catch (IOException e) {
            throw new JsonException(String.format("反序列化Json异常: %s", text), e);
        }
    }

    public static List<Map<String, Object>> fromListMap(String text) {
        if (!StringUtils.hasText(text)) {
            return Collections.emptyList();
        }
        try {
            JavaType javaType = objectMapper.getTypeFactory().constructMapType(LinkedHashMap.class, String.class, Object.class);
            return objectMapper.readValue(text, objectMapper.getTypeFactory().constructCollectionType(ArrayList.class, javaType));
        } catch (IOException e) {
            throw new JsonException(String.format("反序列化Json异常: %s", text), e);
        }
    }

}