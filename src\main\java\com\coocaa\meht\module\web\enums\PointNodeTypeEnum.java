package com.coocaa.meht.module.web.enums;

import lombok.Getter;

/**
 * 节点类型枚举
 */
@Getter
public enum PointNodeTypeEnum {
    
    BUILDING("building", "楼栋"),
    UNIT("unit", "单元"),
    FLOOR("floor", "楼层"),
    WAITING_HALL("waitingHall", "等候厅"),
    POINT("point", "点位");

    private final String code;
    private final String desc;

    PointNodeTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PointNodeTypeEnum getByCode(String code) {
        for (PointNodeTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 