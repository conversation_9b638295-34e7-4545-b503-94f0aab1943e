package com.coocaa.meht.converter;

import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/16
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingTypeConvert extends BaseConverter<Integer>{

    @Override
    protected ConvertType getConvertType() {
        return ConvertType.BUILDING_TYPE;
    }

    @Override
    protected Map<Integer, String> getNameMapping(Collection<Integer> keys) {
        BuildingRatingEntity.BuildingType[] values = BuildingRatingEntity.BuildingType.values();
        return Arrays.stream(values).collect(Collectors.toMap(BuildingRatingEntity.BuildingType::getValue,
                BuildingRatingEntity.BuildingType::getName));
    }

}
