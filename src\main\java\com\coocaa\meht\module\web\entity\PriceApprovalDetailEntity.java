package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 最终审批结果审批详情记录表
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Builder
@Data
@TableName("price_approval_detail")
public class PriceApprovalDetailEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 价格申请ID
     */
    private Integer priceApplyId;

    /**
     * 审批实例UUID，规则：合同编码-申请次数
     */
    private String approvalInstanceUuid;


    /**
     * 审批节点列表
     */
    private String nodeListString;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
