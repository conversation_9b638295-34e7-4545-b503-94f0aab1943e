package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.entity.MessageRecordEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-11-07 16:56
 */
public interface MessageRecordService extends IService<MessageRecordEntity> {

    /**
     * 发送申请消息
     *
     * @param targetUser       目标人
     * @param sendUser         发送人
     * @param applyForUserName 申请人名称
     * @param buildingName     楼宇名称
     * @param buildingArea     楼宇地区
     * @param buildingNo       楼宇编码
     */
    void sendApplyForMsg(String targetUser, String sendUser, String applyForUserName,
                         String buildingName, String buildingArea, String buildingNo);

    /**
     * 发送价格申请消息
     *
     * @param targetUser       目标人
     * @param applyForUserName 申请人名称
     * @param buildingName     楼宇名称
     * @param buildingArea     楼宇地区
     * @param applyId
     */
    void sendPriceApplyForMsg(String targetUser, String applyForUserName,
                              String buildingName, String buildingArea, String applyId);

    /**
     * 飞书已读
     *
     * @param messageIdList
     * @param readTime
     */
    void setFsRead(List<String> messageIdList, LocalDateTime readTime);

}
