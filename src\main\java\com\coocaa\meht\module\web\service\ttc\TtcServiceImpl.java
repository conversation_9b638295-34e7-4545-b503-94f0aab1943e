package com.coocaa.meht.module.web.service.ttc;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.rpc.FeignLogRpc;
import com.coocaa.meht.utils.HttpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class TtcServiceImpl implements ITtcService {

    @Value("${coocas.ttc.domain:https://dev-des-indr.coocaa.com}")
    private String domain;

    @Value("${coocas.ttc.app.name:CSKJ}")
    private String appName;


    private final BuildingRatingService buildingRatingService;
    private final FeignLogRpc feignLogRpc;


    /**
     * [
     * {
     * "atomicCode": "CIA-608512336147386368",
     * "atomicUnit": "个",
     * "dimensionsMap": {
     * "IMD-543598547493326848": "79",
     * "IMD-543598547535269888": "87"
     * },
     * "value": "54321",
     * "time": "2025-02-28",
     * "remark": "创视媒资点位可售数当月新增累计值"
     * },
     * {
     * "atomicCode": "CIA-608511774429417472",
     * "atomicUnit": "%",
     * "dimensionsMap": {
     * "IMD-543598547493326848": "79",
     * "IMD-543598547535269888": "87"
     * },
     * "value": "53",
     * "time": "2025-02-28",
     * "remark": "创视3A楼盘占比（累计值）"
     * }
     * ]
     */
    @Override
    public void syncTtcData(String startTimeStr, String endTimeStr) {

        if (StringUtils.isBlank(startTimeStr) || StringUtils.isBlank(endTimeStr)) {
            LocalDateTime endTime = LocalDateTime.now().minusDays(1l).with(LocalTime.MAX);
            LocalDateTime startTime = endTime.withDayOfMonth(1).with(LocalTime.MIN);
            startTimeStr = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        log.info("获取今日凌晨时间：{}", startTimeStr);
        log.info("获取本月第一天时间：{}", endTimeStr);

        JSONArray result = new JSONArray();

        JSONObject pointTtcItem = assemblePointTtcItem(startTimeStr, endTimeStr);
        JSONObject projectTtcItem = assembleProjectTtcItem(endTimeStr);
        result.add(pointTtcItem);
        result.add(projectTtcItem);
        String jsonBody = JSON.toJSONString(result);

        String url = domain + "/api/indicator-open/standard/indicatorStatic/batchAdd";
        log.info("提交TTC数据url：{}", url);
        log.info("提交TTC数据：{}", jsonBody);

        Map<String, String> header = new HashMap<>();
        header.put("Accept", "*/*");
        header.put("app", appName);
        header.put("Content-Type", "application/json; charset=utf-8;");
        String response = HttpUtils.post(url, header, jsonBody);
        log.info("提交TTC数据结果：{}", response);
    }

    private JSONObject assemblePointTtcItem(String startTimeStr, String endTimeStr) {

        JSONObject pointTtcItem = new JSONObject();
        //创视媒资点位可售点位数
        ResultTemplate<Integer> integerResultTemplate = feignLogRpc.countPointChangeLog(startTimeStr, endTimeStr);
        if (!integerResultTemplate.getSuccess()) {
            log.error("Ssp获取新增点位数失败失败");
            throw new IllegalStateException("Ssp获取新增点位数失败");
        }
        pointTtcItem.put("atomicCode", "CIA-608512336147386368");
        pointTtcItem.put("atomicUnit", "个");
        pointTtcItem.put("dimensionsMap", getDimensionsMap());
        pointTtcItem.put("value", integerResultTemplate.getData() + "");
        pointTtcItem.put("time", endTimeStr.split(" ")[0]);
        pointTtcItem.put("remark", "创视媒资点位可售数当月新增累计值");
        return pointTtcItem;
    }

    private JSONObject assembleProjectTtcItem(String endTimeStr) {
        JSONObject item = new JSONObject();
        // 获取已认证的AAA项目数量
        Long count = buildingRatingService.lambdaQuery().eq(BuildingRatingEntity::getBuildingStatus, 3)
                .eq(BuildingRatingEntity::getProjectLevel, "AAA").count();
        // 获取已认证的ProjectLevel不为空的项目数量
        Long allCount = buildingRatingService.lambdaQuery().eq(BuildingRatingEntity::getBuildingStatus, 3)
                .ne(BuildingRatingEntity::getProjectLevel, "").count();
        if (allCount == 0 || count == null) {
            throw new IllegalStateException("已认证的ProjectLevel不为空的项目数量为0");
        }
        item.put("atomicCode", "CIA-608511774429417472");
        item.put("atomicUnit", "%");
        item.put("dimensionsMap", getDimensionsMap());
        item.put("value", String.format("%.2f", (double) count / allCount));
        item.put("time", endTimeStr.split(" ")[0]);
        item.put("remark", "创视3A楼盘占比（累计值）");
        return item;
    }

    private JSONObject getDimensionsMap() {
        JSONObject dimensionsMap = new JSONObject();
        dimensionsMap.put("IMD-543598547493326848", "79");
        dimensionsMap.put("IMD-543598547535269888", "87");
        return dimensionsMap;
    }


}
