package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("point")
public class PointEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;
    @TableField(exist = false)
    private Integer waitingHallId;
    private String code;
    @TableField(exist = false)
    private String pointStatus;
    @TableField(exist = false)
    private String remark;
    private String deviceSize;
    private String createBy;
    private LocalDateTime createTime;
    private String updateBy;
    private LocalDateTime updateTime;
    private LocalDateTime expireTime;
    private String buildingRatingNo;
    @TableField(exist = false)
    private Integer city;
    @TableField(exist = false)
    private Integer number;
    private String businessCode;

} 