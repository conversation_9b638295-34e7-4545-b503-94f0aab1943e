package com.coocaa.meht.converter;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-05
 */
@Slf4j
public abstract class BaseConverter<K> implements Converter {
    /**
     * 获取映射值
     */
    protected abstract Map<K, String> getNameMapping(Collection<K> keys);

    /**
     * 获取转换类型
     */
    protected abstract ConvertType getConvertType();


    @Override
    public <T> void convert(Collection<T> values) {
        if (CollectionUtils.isEmpty(values)) {
            return;
        }

        T theVal = values.iterator().next();
        // 获取字段信息
        Map<Field, Field> fieldMap = getFieldMap(theVal);
        if (MapUtil.isEmpty(fieldMap)) {
            return;
        }

        // 获取需要转换的值
        Set<K> keys = getKeys(values, fieldMap);
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }

        // 获取映射值
        Map<K, String> nameMap = getNameMapping(keys);
        if (MapUtil.isEmpty(nameMap)) {
            return;
        }

        // 开始转换数据
        values.forEach(val -> convert(val, fieldMap, nameMap));
    }


    /**
     * 转换对应数据
     */
    private <T> void convert(T theVal, Map<Field, Field> fieldMap, Map<?, String> nameMap) {
        fieldMap.forEach((sourceField, targetField) -> {
            try {
                Object key = sourceField.get(theVal);
                if (Objects.isNull(key)) return;

                String val = nameMap.get(key);
                if (StringUtils.isBlank(val)) return;

                targetField.set(theVal, val);
            } catch (Exception ex) {
                log.warn("更新字段({})值失败", sourceField.getName());
            }
        });
    }

    /**
     * 获取需要转换的值
     */
    private <T> Set<K> getKeys(Collection<T> values, Map<Field, Field> fieldMap) {
        Set<K> keys = Sets.newHashSet();
        for (T val : values) {
            fieldMap.forEach((sourceField, targetField) -> {
                try {
                    keys.add((K) sourceField.get(val));
                } catch (Exception ex) {
                    log.warn("获取字段({})值失败", sourceField.getName());
                }
            });
        }
        return keys;
    }


    /**
     * 获取需要转换的字段
     */
    private <T> Map<Field, Field> getFieldMap(T theVal) {
        // 找出所有需要转换的字段
        ConvertType convertType = getConvertType();
        Map<Field, Field> fieldMap = Maps.newHashMap();
        for (Field field : ReflectUtils.getAllFields(theVal)) {
            Convert annotation = field.getAnnotation(Convert.class);
            if (Objects.isNull(annotation) || !Objects.equals(annotation.type(), convertType)) {
                continue;
            }

            String targetFieldName = annotation.targetFieldName();
            if (StringUtils.isBlank(targetFieldName)) {
                targetFieldName = field.getName() + "Name";
            }

            try {
                Field targetField = theVal.getClass().getDeclaredField(targetFieldName);
                targetField.setAccessible(true);

                field.setAccessible(true);
                fieldMap.put(field, targetField);
            } catch (Exception ex) {
                log.warn("获取字段({})信息失败", targetFieldName, ex);
            }
        }
        return fieldMap;
    }
}
