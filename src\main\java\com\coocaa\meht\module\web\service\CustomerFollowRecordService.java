package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.crm.dto.req.CrmFollowUpListReq;
import com.coocaa.meht.module.crm.vo.FollowUpVO;
import com.coocaa.meht.module.web.entity.CustomerFollowRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 客户跟进
 */
public interface CustomerFollowRecordService extends IService<CustomerFollowRecordEntity> {

    Page<FollowUpVO> getCustomerFollowRecord(CrmFollowUpListReq req);

    void recover(Set<String> businessCodes);

}
