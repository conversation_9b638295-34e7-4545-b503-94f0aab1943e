package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.PointPicContractSnapshotMapper;
import com.coocaa.meht.module.web.entity.PointPicContractSnapshotEntity;
import com.coocaa.meht.module.web.service.IPointPicContractSnapshotService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 点位合同图片快照表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
public class PointPicContractSnapshotServiceImpl extends ServiceImpl<PointPicContractSnapshotMapper, PointPicContractSnapshotEntity> implements IPointPicContractSnapshotService {

    @Override
    public List<PointPicContractSnapshotEntity> listByPointIds(List<Integer> pointIds) {
        if(CollectionUtil.isNotEmpty(pointIds)){
            return lambdaQuery().in(PointPicContractSnapshotEntity::getPointId,pointIds)
                    .list();
        }
        return new ArrayList<>();
    }
}