/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-16
 */
package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.annotation.RollBack;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 楼宇基因实体类
 * 用于存储楼宇的基本特征和属性信息，包括楼宇编号、规格、电梯数量等基础信息
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@TableName("building_gene")
public class BuildingGeneEntity implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 规格
     */
    @RollBack
    @Schema(description = "规格")
    private String spec;

    /**
     * 楼宇编号
     */
    @Schema(description = "楼宇编号")
    private String buildingRatingNo;

    /**
     * 月平均租金
     */
    @Schema(description = "月平均租金")
    private BigDecimal monthlyAvgPrice;

    /**
     * 房价
     */
    @Schema(description = "房价")
    private BigDecimal housePrice;

    /**
     * 竞媒信息
     */
    @Schema(description = "竞媒信息")
    private String competitiveMediaInfo;

    /**
     * 竞媒信息
     */
    @Schema(description = "竞媒信息")
    @TableField(exist = false)
    private List<String> competitiveMediaInfos;

    /**
     * 目标点位数量
     */
    @Schema(description = "目标点位数量")
    private Integer targetPointCount;

    /**
     * 最高层数
     */
    @Schema(description = "最高层数")
    private Integer maxFloorCount;

    /**
     * 楼龄
     */
    @Schema(description = "楼龄")
    private Integer buildingAge;

    /**
     * 总单元数量
     */
    @Schema(description = "总单元数量")
    private Integer totalBuildingCount;


    /**
     * 总楼栋数量
     */
    @RollBack
    @Schema(description = "总楼栋数量")
    private Integer totalUnitCount;

    /**
     * 总等候厅数量
     */
    @Schema(description = "总等候厅数量")
    private Integer totalWaitingCount;

    /**
     * 电梯数量
     */
    @RollBack
    @Schema(description = "电梯数量")
    private Integer elevatorCount;

    /**
     * 车位数量
     */
    @Schema(description = "车位数量")
    private Integer parkingCount;

    /**
     * 禁忌行业名称
     */
    @Schema(description = "禁忌行业名称")
    private String forbiddenIndustry;

    /**
     * 日人流量
     */
    @Schema(description = "日人流量")
    private Integer flowCount;

    /**
     * 访客数
     */
    @Schema(description = "访客数")
    private Integer visitCount;

    /**
     * 入驻企业数量
     */
    @RollBack
    @Schema(description = "入驻企业数量")
    private Integer companyCount;

    /**
     * 入住率
     */
    @Schema(description = "入住率")
    private BigDecimal occupancyRate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


    /**
     * 楼间距，表示楼宇之间的间距（单位：米）
     */
    @RollBack
    @Schema(description = "楼间距，表示楼宇之间的间距（单位：米）")
    private BigDecimal buildingSpacing;

    /**
     * 挑高，表示楼宇的层高（单位：米）
     */
    @RollBack
    @Schema(description = "挑高，表示楼宇的层高（单位：米）")
    private BigDecimal buildingCeilingHeight;

    /**
     * 提交系数，用于计算楼宇的得分
     */
    @RollBack
    @Schema(description = "提交系数，用于计算楼宇的得分")
    private BigDecimal submitCoefficient;

    /**
     * 复核系数，审核后确定的系数
     */
    @RollBack
    @Schema(description = "复核系数，审核后确定的系数")
    private BigDecimal finalCoefficient;


    /**
     * 特殊说明，用于记录楼宇的特殊情况或备注信息
     */
    @RollBack
    @Schema(description = "特殊说明，用于记录楼宇的特殊情况或备注信息")
    private String specialDesc;

    @Schema(description = "日租金")
    private BigDecimal dailyPrice;

    @Schema(description = "交付时间")
    private LocalDate deliveryDate;

    /**
     * 是否删除标记：0-未删除，1-已删除
     */
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 物业费
     */
    private BigDecimal propertyFee;

    /**
     * 最低楼层数
     */
    private Integer minFloorCount;

    /**
     * 覆盖人数
     */
    private Integer coverageCount;

}