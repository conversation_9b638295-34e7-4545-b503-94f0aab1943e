package com.coocaa.meht.rpc;

import cn.hutool.core.date.DatePattern;
import com.coocaa.ad.common.config.FeignConfig;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.module.web.vo.common.ConfigVO;
import com.coocaa.meht.module.web.vo.kanban.ContractPointStatisticsVO;
import com.coocaa.meht.module.web.vo.kanban.DataAccessVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanDeviceStatisticsVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanVO;
import com.coocaa.meht.rpc.dto.BusinessOpportunityStatusVO;
import com.coocaa.meht.rpc.dto.CityVO;
import com.coocaa.meht.rpc.dto.UserDataAccessV2DTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * 合同CMS交互接口
 */
@FeignClient(value = "cheese-cms-api", configuration = FeignConfig.class)
public interface FeignCmsRpc {
    //获取城市
    @GetMapping("/api/cms/venue/data/access/detail/{wno}")
    ResultTemplate<DataAccessVO> getUserDataAccessDetail(@PathVariable("wno") String empNo, @RequestHeader("wno") String wno);

    @GetMapping("/api/cms/venue/kanban/info/statistics-point")
    ResultTemplate<List<ContractPointStatisticsVO>> getPointStatusInfo(@RequestHeader("wno") String wno,
                                                                       @RequestParam("date") String date,
                                                                       @RequestParam("days") Integer days,
                                                                       @RequestParam("cityId") Integer cityId);

    /**
     * @Author：TanJie
     * @Date：2025-01-12 12:20
     * @Description：根据城市统计合同状态
     */
    @PostMapping("/api/cms/venue/kanban/contract")
    ResultTemplate<KanbanVO> getContractStatusInfo(
            @RequestHeader("wno") String wno,
            @RequestParam(name = "cityId", required = false, defaultValue = "0") List<Integer> cityIds);

    /**
     * @Author：TanJie
     * @Date：2025-01-17 21:32
     * @Description：获取设备统计数据
     */
    @GetMapping("/api/cms/venue/kanban/device/statistics")
    ResultTemplate<KanbanDeviceStatisticsVO> getDeviceStatisticsData(
            @RequestHeader("wno") String wno,
            @RequestParam(name = "accessType", required = false, defaultValue = "") String accessType,
            @RequestParam(name = "cityId", required = false, defaultValue = "0") List<Integer> cityIds,
            @RequestParam(name = "filterDate", required = false) @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate filterDate);


    /**
     * 获取当前用户数据权限详情
     */
    @GetMapping("/api/cms/venue/data/access/current")
    ResultTemplate<UserDataAccessV2DTO> getUserDataAccessV2();

    /**
     * 查看楼宇是否有合同
     */
    @PostMapping("/api/cms/venue/contract/has-contract")
    ResultTemplate<Boolean> hasContract(@RequestBody List<String> buildingCodes);

    /**
     * 楼宇小助手查看楼宇合同状态
     */
    @PostMapping("/api/cms/venue/contract/has-contract-status")
    ResultTemplate<String> hasContractStatus(@RequestBody List<String> buildingCodes);


    /**
     * 根据城市名称获取城市大区信息
     * /api/cms/nacos/region
     * @param cityName
     * @return
     */
    @Operation(summary = "根据城市名称获取城市大区信息")
    @GetMapping("/api/cms/nacos/region/{cityName}")
    ResultTemplate<String> getRegionInfoByCityName(@PathVariable(name = "cityName") String cityName);

    /**
     * 查询有履约中的合同的商机编号
     */
    @PostMapping("/api/cms/venue/contract/signed/business-codes")
    ResultTemplate<List<String>> isContractSigned(@RequestBody Set<String> businessCodes);

    @Operation(summary = "获取配置信息")
    @GetMapping("/api/cms/config/{code}")
    ResultTemplate<ConfigVO> getConfig(@PathVariable(name = "code") String code);

    /**
     * 获取城市负责人
     */
    @PostMapping("/api/cms/venue/city/business-head")
    ResultTemplate<List<CityVO>> getBusinessHead(@RequestBody Set<String> cityNames);

    /**
     * cms商机状态
     */
    @PostMapping("/api/cms/venue/contract/business-opportunity-status")
    ResultTemplate<List<BusinessOpportunityStatusVO>> getBusinessOpportunityStatus(@RequestBody Set<String> businessCodes);


}
