package com.coocaa.meht.utils;

/**
 * <AUTHOR>
 * @since 2024/11/1
 */
@Deprecated
public class TokenUtils {
    private static final ThreadLocal<String> localToken = new ThreadLocal<>();

    public static String getToken() {
        return localToken.get();
    }

    public static void setToken(String token) {
        localToken.set(token);
    }

    public static void remove() {
        localToken.remove();
    }
}
