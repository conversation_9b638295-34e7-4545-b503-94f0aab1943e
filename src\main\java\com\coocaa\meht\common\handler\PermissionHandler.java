package com.coocaa.meht.common.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.PermissionDTO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.crm.enums.SceneTypeEnum;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.dto.UserDataAccessV2DTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-27
 */
@Slf4j
@Component
public class PermissionHandler {

    @Resource
    private FeignCmsRpc feignCmsRpc;

    @Resource
    private FeignAuthorityRpc feignAuthorityRpc;

    public PermissionDTO getCmsPermission(SceneTypeEnum sceneType) {
        List<String> userCodes = new ArrayList<>();
        List<String> cities = new ArrayList<>();
        PermissionDTO permissionDTO = new PermissionDTO();
        ResultTemplate<UserDataAccessV2DTO> userDataAccessV2 = feignCmsRpc.getUserDataAccessV2();
        log.info("权限数据userDataAccessV2:{}", JSON.toJSONString(userDataAccessV2));
        if (Objects.isNull(userDataAccessV2.getData())) {
            userCodes.add(SecurityUser.getUserCode());
            permissionDTO.setUserCodes(userCodes);
            permissionDTO.setCities(cities);
            return permissionDTO;
        }
        UserDataAccessV2DTO data = userDataAccessV2.getData();
        List<Integer> cityIds = data.getCityIds();
        if (CollectionUtil.isNotEmpty(cityIds)) {
            ResultTemplate<List<CodeNameVO>> listResultTemplate = feignAuthorityRpc.listCityByIds(cityIds);
            log.info("城市权限数据listResultTemplate:{}", JSON.toJSONString(listResultTemplate));
            cities.addAll(listResultTemplate.getData().stream().map(CodeNameVO::getName).toList());
        }

        List<Integer> userIds = data.getUserIds();
        if (CollectionUtil.isNotEmpty(userIds)) {
            ResultTemplate<List<CodeNameVO>> listResultTemplate = feignAuthorityRpc.listUserByIds(userIds);
            log.info("用户权限数据listResultTemplate:{}", JSON.toJSONString(listResultTemplate));
            userCodes.addAll(listResultTemplate.getData().stream().map(CodeNameVO::getWno).toList());
        }

        if (SceneTypeEnum.ALL_CUSTOMERS.equals(sceneType)) {
            permissionDTO.setUserCodes(userCodes);
            permissionDTO.setCities(cities);
        } else if (SceneTypeEnum.MY_CUSTOMERS.equals(sceneType)) {
            userCodes.clear();
            userCodes.add(SecurityUser.getUserCode());
            permissionDTO.setUserCodes(userCodes);
            permissionDTO.setCities(cities);
        } else if (SceneTypeEnum.SUBORDINATE_CUSTOMERS.equals(sceneType)) {
            userCodes.remove(SecurityUser.getUserCode());
            permissionDTO.setUserCodes(userCodes);
            permissionDTO.setCities(cities);
        } else {
            permissionDTO.setUserCodes(userCodes);
            permissionDTO.setCities(cities);
        }

        log.info("权限数据PermissionDTO:{}", JSON.toJSONString(permissionDTO));
        return permissionDTO;
    }

    /**
     * 筛选符合权限范围的条件集合
     *
     * @param userCodes 负责人条件
     * @param cities    城市条件
     * @return
     */
    public PermissionDTO getCmsPermission(List<String> userCodes, List<String> cities) {
        PermissionDTO permissionDTO = new PermissionDTO();
        List<String> authorizedUserCodes = new ArrayList<>();
        List<String> authorizedCities = new ArrayList<>();

        ResultTemplate<UserDataAccessV2DTO> userDataAccessV2 = feignCmsRpc.getUserDataAccessV2();
        UserDataAccessV2DTO accessData = userDataAccessV2.getData();
        if (Objects.isNull(accessData)) {
            throw new ServerException("未配置数据访问权限");
        }

        List<Integer> cityIds = accessData.getCityIds();
        if (CollectionUtil.isNotEmpty(cityIds)) {
            ResultTemplate<List<CodeNameVO>> listResultTemplate = feignAuthorityRpc.listCityByIds(cityIds);
            authorizedCities.addAll(listResultTemplate.getData().stream().map(CodeNameVO::getName).toList());
            if (CollUtil.isNotEmpty(cities)) {
                authorizedCities.retainAll(cities);
                if (CollUtil.isEmpty(authorizedCities)) {
                    // 置空表示没有符合条件的城市可查询
                    authorizedCities = null;
                    log.info("符合条件的城市权限为空");
                }
            }
        } else {
            // cityIds为空，表示没有任何城市权限
            authorizedCities = null;
        }

        List<Integer> userIds = accessData.getUserIds();
        if (CollectionUtil.isNotEmpty(userIds)) {
            ResultTemplate<List<CodeNameVO>> listResultTemplate = feignAuthorityRpc.listUserByIds(userIds);
            authorizedUserCodes.addAll(listResultTemplate.getData().stream().map(CodeNameVO::getWno).toList());
            if (CollUtil.isNotEmpty(userCodes)) {
                authorizedUserCodes.retainAll(userCodes);
                if (CollUtil.isEmpty(authorizedUserCodes)) {
                    // 置空表示没有符合条件的用户可查询
                    authorizedUserCodes = null;
                    log.info("符合条件的用户权限为空");
                }
            }
        } else {
            // userIds为空，表示可以看全部用户数据
            if (CollUtil.isNotEmpty(userCodes)) {
                authorizedUserCodes.addAll(userCodes);
            }
        }

        permissionDTO.setUserCodes(authorizedUserCodes);
        permissionDTO.setCities(authorizedCities);
        return permissionDTO;
    }

}
