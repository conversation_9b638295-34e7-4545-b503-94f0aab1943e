package com.coocaa.meht.utils;

import cn.hutool.core.collection.CollUtil;
import com.coocaa.meht.common.bean.BigScreenCalculateRule;
import com.coocaa.meht.common.constants.RatingConstants;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.module.web.dto.BigScreenCalculateDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * 大屏点位系数计算器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-16
 */
@Component
public class LargeScreenCalculator {

    // 不合格系数
    private static final String COEFFICIENT_UNQUALIFIED = "0";

    @Autowired
    private LargeScreenProperties largeScreenProperties;

    public String calculate(BigScreenCalculateDTO param) {
        List<BigScreenCalculateRule> calculateRules = largeScreenProperties.getCalculateRule();
        if (CollUtil.isEmpty(calculateRules)) {
            throw new ServerException("大屏点位系数计算规则未配置");
        }

        calculateRules.sort(Comparator.comparingInt(BigScreenCalculateRule::getPriority));
        for (BigScreenCalculateRule rule : calculateRules) {
            if (meetsCriteria(param, rule)) {
                return rule.getCoefficient();
            }
        }

        return COEFFICIENT_UNQUALIFIED;
    }

    /**
     * 判断数据是否符合系数计算规则
     */
    private boolean meetsCriteria(BigScreenCalculateDTO param, BigScreenCalculateRule rule) {
                // 挑高
        return BigDecimalUtils.ge(param.getBuildingCeilingHeight(), rule.getHeight())
                // 楼层
                && Integer.parseInt(param.getBuildingNumberInput()) >= rule.getFloor()
                // 间距
                && BigDecimalUtils.ge(param.getBuildingSpacing(), rule.getSpacing())
                // 楼龄
                && Integer.parseInt(param.getBuildingAgeInput()) <= rule.getAge()
                // 核心区域
                && (Objects.isNull(rule.getCoreFlag())
                || (rule.getCoreFlag() ? RatingConstants.CORE_AREA_YES.equals(param.getBuildingLocationText())
                : RatingConstants.CORE_AREA_NO.equals(param.getBuildingLocationText())));
    }


}
