package com.coocaa.meht.common.exception;

import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;
import com.coocaa.meht.utils.ExceptionUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.nio.file.AccessDeniedException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 异常处理器
 */
@Slf4j
@RestControllerAdvice
public class ServerExceptionHandler {
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @ExceptionHandler(ServerException.class)
    public Result<Void> handleException(ServerException ex) {
        return new Result<>(ex.getCode(), ex.getMsg());
    }

    @ExceptionHandler(BindException.class)
    public Result<Void> bindException(BindException e) {
        if (e.hasErrors()) {
            String msg = e.getAllErrors().get(0).getDefaultMessage();
            if (msg != null && msg.length() < 50) {
                return Result.error(msg);
            }
        }
        return Result.error("参数数据类型错误");
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> methodArgumentNotValidException(MethodArgumentNotValidException e) {
        String msg = "参数验证失败";
        BindingResult bindingResult = e.getBindingResult();
        if (bindingResult.hasErrors()) {
            msg = bindingResult.getAllErrors().get(0).getDefaultMessage();
        }
        return Result.error(msg);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result<Void> notSupportedException(HttpRequestMethodNotSupportedException e) {
        return Result.error(ErrorCode.METHOD_ERROR);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public Result<Void> notSupportedException(AccessDeniedException e) {
        return Result.error(ErrorCode.NO_PERMISSION);
    }

    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception ex, HttpServletRequest request) {
        if (ex instanceof ServerException) {
            ServerException se = (ServerException) ex;
            return new Result<>(se.getCode(), se.getMsg());
        }
        String title = ex.getMessage();
        log.error(title, ex);
        Map<String, Object> map = new HashMap<>();
        LoginUser user = SecurityUser.getUserAnonymity();
        if (user != null) {
            map.put("user", user.getUserName() + "(" + user.getUserCode() + ")");
        }
        if (request != null) {
            map.put("path", request.getRequestURI());
        }
        map.put("title", title);
        map.put("errorLog", ExceptionUtils.getMessage(ex));
        applicationEventPublisher.publishEvent(new ExceptionUtils.ErrorEvent(map));
        return Result.error();
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(ApprovalBusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<Void> handleBusinessException(ApprovalBusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return Result.error(e.getMessage());
    }
    /**
     * 处理SQL异常
     */
    @ExceptionHandler(SQLException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<Void> handleSqlException(SQLException e) {
        log.warn("数据库异常: {}", e.getMessage());
        return Result.error(ErrorCode.INTERNAL_SERVER_ERROR);
    }


}