package com.coocaa.meht.module.web.dto.point;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
public class PointDTO {

    @Schema(description = "新增的时候不传，修改的时候传")
    private Integer pointId;

    @Schema(description = "点位编码")
    private String code;

    /*@Schema(description = "楼栋编码")
    @NotBlank(message = "楼栋编码不能为空")
    private String buildingRatingNo;*/

    @NotNull(message = "等候厅id不能为空")
    @Schema(description = "等候厅id")
    private Integer waitingHallId;

    @NotEmpty(message = "点位图片不能为空")
    @Schema(description = "点位图片列表")
    private List<String> images;

    @NotNull(message = "设备尺寸不能为空")
    @Schema(description = "设备尺寸字典0013")
    private String deviceSize;

    @Schema(description = "方案描述")
    private String description;

    @Schema(description = "商机编码")
    @NotBlank(message = "商机编码不能为空")
    private String businessCode;

    @Schema(description = "点位方案")
    private Integer pointPlanId;
}
