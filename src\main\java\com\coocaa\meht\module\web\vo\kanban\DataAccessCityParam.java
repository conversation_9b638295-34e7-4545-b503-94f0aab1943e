package com.coocaa.meht.module.web.vo.kanban;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataAccessCityParam {

    @NotNull(message = "城市Id")
    @Schema(description = "城市ID", type = "Int", example = "133")
    private Integer id;

    @NotBlank(message = "城市名称")
    @Schema(description = "城市名称", type = "String", example = "成都市")
    private String cityName;
}