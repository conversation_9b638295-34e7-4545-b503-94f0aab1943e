package com.coocaa.meht.rpc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Schema(name = "InnerApproveOpinionParam", description = "站内审批意见")
public class InnerApproveOpinionParam {

    @Schema(description = "审批单编号")
    private String instanceCode;

    @Schema(description = "任务ID")
    private Integer taskId;

    @Schema(description = "备注")
    private String comment;
}
