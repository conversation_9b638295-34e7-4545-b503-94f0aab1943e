package com.coocaa.meht.module.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.web.dto.CommentCreateDTO;
import com.coocaa.meht.module.web.dto.CommentDTO;
import com.coocaa.meht.module.web.dto.CommentQueryDto;
import com.coocaa.meht.module.web.service.CommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 评论管理控制器
 * <p>
 * 提供评论的创建、查询、删除等功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-29
 */
@Slf4j
@RestController
@RequestMapping("/comment")
@RequiredArgsConstructor
@Tag(name = "评论管理", description = "评论相关接口")
public class CommentController {

    private final CommentService commentService;

    /**
     * 创建评论
     *
     * @param request 评论创建请求参数
     * @return 新创建的评论ID
     */
    @PostMapping
    @Operation(summary = "创建评论", description = "创建新的评论信息")
    public Result<Integer> createComment(@RequestBody @Validated CommentCreateDTO request) {
        log.info("创建评论请求: 业务类型={}, 业务ID={}", request.getBusinessType(), request.getBusinessId());
        String userCode = UserThreadLocal.getUser().getWno();
        if (StringUtils.isBlank(userCode)) {
            log.error("创建评论失败: 用户未登录");
            throw new ServerException("用户未登录或登录已过期");
        }

        try {
            Integer commentId = commentService.createComment(request, userCode);
            log.info("创建评论成功: commentId={}", commentId);
            return Result.ok(commentId);
        } catch (Exception e) {
            log.error("创建评论异常: {}", e.getMessage(), e);
            return Result.error("创建评论失败: " + e.getMessage());
        }
    }

    /**
     * 删除评论
     *
     * @param commentId 评论ID
     * @return 删除结果
     */
    @DeleteMapping("/{commentId}")
    @Operation(summary = "删除评论", description = "根据评论ID删除评论信息")
    public Result<Boolean> deleteComment(
            @Parameter(description = "评论ID", required = true)
            @PathVariable Integer commentId) {
        log.info("删除评论请求: commentId={}", commentId);

        if (commentId == null || commentId <= 0) {
            log.error("删除评论失败: 评论ID无效, commentId={}", commentId);
            return Result.error("评论ID无效");
        }

        String userCode = UserThreadLocal.getUser().getWno();
        if (StringUtils.isBlank(userCode)) {
            log.error("删除评论失败: 用户未登录");
            throw new ServerException("用户未登录或登录已过期");
        }
        try {
            Boolean result = commentService.deleteComment(commentId, userCode);
            if (result) {
                log.info("删除评论成功: commentId={}", commentId);
                return Result.ok(true);
            } else {
                log.warn("删除评论失败: commentId={}, 可能是权限不足或评论不存在", commentId);
                return Result.error("删除失败，可能是权限不足或评论不存在");
            }
        } catch (Exception e) {
            log.error("删除评论异常: commentId={}, 错误={}", commentId, e.getMessage(), e);
            return Result.error("删除评论失败: " + e.getMessage());
        }
    }

    /**
     * 获取评论详情
     *
     * @param commentId 评论ID
     * @return 评论详情
     */
    @GetMapping("/{commentId}")
    @Operation(summary = "获取评论详情", description = "根据评论ID获取评论详细信息")
    public Result<CommentDTO> getCommentDetail(
            @Parameter(description = "评论ID", required = true)
            @PathVariable Integer commentId) {
        log.info("获取评论详情请求: commentId={}", commentId);

        if (commentId == null || commentId <= 0) {
            log.error("获取评论详情失败: 评论ID无效, commentId={}", commentId);
            return Result.error("评论ID无效");
        }

        try {
            CommentDTO comment = commentService.getCommentDetail(commentId);
            if (comment == null) {
                log.warn("获取评论详情失败: 评论不存在, commentId={}", commentId);
                return Result.error("评论不存在");
            }
            log.info("获取评论详情成功: commentId={}", commentId);
            return Result.ok(comment);
        } catch (Exception e) {
            log.error("获取评论详情异常: commentId={}, 错误={}", commentId, e.getMessage(), e);
            return Result.error("获取评论详情失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询评论列表
     *
     * @param request 查询参数
     * @return 评论列表分页数据
     */
    @GetMapping
    @Operation(summary = "分页查询评论列表", description = "根据业务类型和业务ID分页查询评论列表")
    public Result<Page<CommentDTO>> pageComments(
            @Parameter(description = "查询参数", required = true)
            @Validated CommentQueryDto request) {
        log.info("分页查询评论列表请求: 业务类型={}, 业务ID={}, 页码={}, 每页数量={}",
                request.getBusinessType(), request.getBusinessId(), request.getPage(), request.getLimit());

        try {
            Page<CommentDTO> commentPage = commentService.pageComments(request);
            log.info("分页查询评论列表成功: 业务类型={}, 业务ID={}, 总数={}",
                    request.getBusinessType(), request.getBusinessId(), commentPage.getTotal());
            return Result.ok(commentPage);
        } catch (Exception e) {
            log.error("分页查询评论列表异常: 业务类型={}, 业务ID={}, 错误={}",
                    request.getBusinessType(), request.getBusinessId(), e.getMessage(), e);
            return Result.error("分页查询评论列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取评论数量
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 评论数量
     */
    @GetMapping("/count")
    @Operation(summary = "获取评论数量", description = "根据业务类型和业务ID获取评论数量")
    public Result<Long> countComments(
            @Parameter(description = "业务类型（1楼宇、2价格申请）", required = true)
            @RequestParam("businessType") Integer businessType,
            @Parameter(description = "业务ID", required = true)
            @RequestParam("businessId") String businessId) {
        log.info("获取评论数量请求: 业务类型={}, 业务ID={}", businessType, businessId);

        if (businessType == null) {
            log.error("获取评论数量失败: 业务类型为空");
            return Result.error("业务类型不能为空");
        }

        if (StringUtils.isBlank(businessId)) {
            log.error("获取评论数量失败: 业务ID为空");
            return Result.error("业务ID不能为空");
        }

        try {
            Long count = commentService.countComments(businessType, businessId);
            log.info("获取评论数量成功: 业务类型={}, 业务ID={}, 数量={}", businessType, businessId, count);
            return Result.ok(count);
        } catch (Exception e) {
            log.error("获取评论数量异常: 业务类型={}, 业务ID={}, 错误={}", businessType, businessId, e.getMessage(), e);
            return Result.error("获取评论数量失败: " + e.getMessage());
        }
    }
}