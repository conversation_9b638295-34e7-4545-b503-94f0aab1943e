package com.coocaa.meht.module.building.convert;

import com.coocaa.meht.module.building.entity.BuildingScreenEntity;
import com.coocaa.meht.module.building.entity.CompleteBuildingScreenEntity;
import com.coocaa.meht.module.building.vo.BuildingScreenVO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
@Mapper
public interface BuildingScreenConvert {
    BuildingScreenConvert INSTANCE = Mappers.getMapper(BuildingScreenConvert.class);

    BuildingScreenVO toBuildingScreenVO(BuildingScreenEntity entity);

    BuildingScreenVO toBuildingScreenVOByApplyDto(RatingApplyDto ratingApplyDto);

    @Mapping(target = "buildingRatingNo", source = "completeRatingNo")
    BuildingScreenVO toBuildingScreenVOByEntity(CompleteBuildingScreenEntity entity);

    BuildingScreenEntity toBuildingScreenEntity(CompleteBuildingScreenEntity entity);


}
