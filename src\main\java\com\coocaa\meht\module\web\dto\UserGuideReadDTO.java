package com.coocaa.meht.module.web.dto;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户引导已读请求DTO
 * @since 2025-06-11
 */
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 用户引导已读请求DTO
 */
@Data
public class UserGuideReadDTO {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 功能编码
     */
    @NotBlank(message = "功能编码不能为空")
    private String featureCode;
} 