package com.coocaa.meht.rpc.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-02
 */
@Data
public class UserBatchMessageParam {

    /**
     * 应用类型编码，字典0124下，不能为空
     */
    private String appCode;

    /**
     * 模块类型编码，字典0125下，不能为空
     */
    private String moduleCode;

    /**
     * 发送用户ID,不传默认是当前登陆人，定时任务默认0
     */
    private Integer sendUserId;

    /**
     * 批量消息
     */
    private List<UserMessageContentParam> contentParams;

}
