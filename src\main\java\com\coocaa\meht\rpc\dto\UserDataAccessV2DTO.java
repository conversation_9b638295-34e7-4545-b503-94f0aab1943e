package com.coocaa.meht.rpc.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 用户数据权限
 */
@Data
@Accessors(chain = true)
public class UserDataAccessV2DTO {

    /**
     * 权限类型
     */
    private String accessType;
    /**
     * 代理商ID集合
     */
    private List<Integer> agentIds;

    /**
     * 用户ID集合
     */
    private List<Integer> userIds;

    /**
     * 城市ID集合
     */
    private List<Integer> cityIds;
}
