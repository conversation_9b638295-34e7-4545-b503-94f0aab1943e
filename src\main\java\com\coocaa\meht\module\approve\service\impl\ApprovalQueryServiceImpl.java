package com.coocaa.meht.module.approve.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.module.approve.adapter.ApprovalAdapter;
import com.coocaa.meht.module.approve.config.ApprovalRuleConfig;
import com.coocaa.meht.module.approve.convert.ScreenApproveRecordConvert;
import com.coocaa.meht.module.approve.dao.ScreenApprovalInstanceMapper;
import com.coocaa.meht.module.approve.dto.ApprovalDetailVO;
import com.coocaa.meht.module.approve.dto.ScreenApproveRecordDTO;
import com.coocaa.meht.module.approve.dto.TaskDealCountParam;
import com.coocaa.meht.module.approve.dto.TaskDealCountVO;
import com.coocaa.meht.module.approve.dto.TodoListQueryDTO;
import com.coocaa.meht.module.approve.dto.TodoTaskDTO;
import com.coocaa.meht.module.approve.entity.ScreenApprovalInstanceEntity;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.module.approve.enums.ApproveActionEnum;
import com.coocaa.meht.module.approve.enums.ApproveCancelReason;
import com.coocaa.meht.module.approve.enums.ApproveStatusEnum;
import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;
import com.coocaa.meht.module.approve.service.ApprovalQueryService;
import com.coocaa.meht.module.web.dao.PriceApplyDao;
import com.coocaa.meht.module.web.dao.ScreenApproveRecordMapper;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.coocaa.meht.module.web.entity.ScreenApproveRecordEntity;
import com.coocaa.meht.rpc.FeignInnerApprovalRpc;
import com.coocaa.meht.rpc.vo.InnerApproveNodeVO;
import com.coocaa.meht.rpc.vo.InnerInstanceTaskVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.Objects.requireNonNull;

/**
 * 审批查询服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApprovalQueryServiceImpl implements ApprovalQueryService {

    private final ApprovalAdapter approvalAdapter;
    private final ScreenApprovalInstanceMapper instanceMapper;
    private final ScreenApproveRecordMapper recordMapper;
    private final ApprovalRuleConfig approvalRuleConfig;
    private final UserCacheHelper userCacheHelper;
    private final FeignInnerApprovalRpc innerApprovalRpc;
    private final PriceApplyDao priceApplyDao;

    @Override
    public Result<IPage<TodoTaskDTO>> queryTodoList(PageRequestVO<TodoListQueryDTO> queryDTO) {
        log.info("查询待办列表, query: {}", queryDTO);
        if (CollUtil.isNotEmpty(approvalRuleConfig.getAdminUser())
                && approvalRuleConfig.getAdminUser().contains(UserThreadLocal.getUser().getWno())) {
            // 查看所有代办
            queryDTO.getQuery().setApproveUser(null);
        }
        Page<TodoTaskDTO> pg = new Page<>(queryDTO.getCurrentPage(), queryDTO.getPageSize());
        pg.setOptimizeCountSql(false);
        IPage<TodoTaskDTO> page = recordMapper.selectTaskPage(pg, queryDTO.getQuery(), "todo");
        fillTodoTask(page.getRecords());
        return Result.ok(page);
    }

    @Override
    public Result<IPage<TodoTaskDTO>> queryDoneList(PageRequestVO<TodoListQueryDTO> queryDTO) {
        log.info("查询已办列表, query: {}", queryDTO);
        if (CollUtil.isNotEmpty(approvalRuleConfig.getAdminUser())
                && approvalRuleConfig.getAdminUser().contains(UserThreadLocal.getUser().getWno())) {
            // 查看所有代办
            queryDTO.getQuery().setApproveUser(null);
        }
        Page<TodoTaskDTO> pg = new Page<>(queryDTO.getCurrentPage(), queryDTO.getPageSize());
        pg.setOptimizeCountSql(false);
        IPage<TodoTaskDTO> page = recordMapper.selectTaskPage(pg, queryDTO.getQuery(), "done");
        fillTodoTask(page.getRecords());
        return Result.ok(page);
    }


    @Override
    public Result<ApprovalDetailVO> queryDetail(String instanceCode) {
        log.info("查询审批详情, instanceCode: {}", instanceCode);

        if (StringUtils.isEmpty(instanceCode)) {
            return Result.error("审批实例编码不能为空");
        }

        try {
            ScreenApprovalInstanceEntity instance = instanceMapper.selectByInstanceCode(instanceCode);
            if (instance == null) {
                return Result.error("未找到审批实例");
            }

            ApprovalDetailVO detailVO = approvalAdapter.getDetail(instanceCode);
            if (detailVO == null) {
                return Result.error("获取审批详情失败");
            }

            List<InnerApproveNodeVO> nodes = approvalAdapter.getNodes(instanceCode);
            detailVO.setNodes(nodes);

            return Result.ok(detailVO);
        } catch (ApprovalBusinessException e) {
            log.warn("查询审批详情业务异常: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询审批详情未知异常", e);
            return Result.error("系统异常，请稍后重试");
        }
    }

    @Override
    public Result<List<ScreenApproveRecordDTO>> queryLocalNodes(String businessKey) {
        log.info("查询本地节点: businessKey={}", businessKey);
        List<ScreenApproveRecordEntity> recordEntities = recordMapper.selectRecordByBusinessCode(businessKey, null);
        List<ScreenApproveRecordDTO> dtoList = ScreenApproveRecordConvert.INSTANCE.toDtoList(recordEntities);
        // 过滤未处理节点
        filterNode(dtoList);
        // 找到当前节点位置
        findFinishNode(dtoList);
        // 填充数据
        fillApproveRecords(dtoList);
        return Result.ok(dtoList);
    }


    @Override
    public Result<ApprovalDetailVO> queryDetailByBusinessKey(String businessKey, String approveType, String version) {
        log.info("根据业务键查询审批详情: businessKey={}, sceneType={}, version={}", businessKey, approveType, version);

        if (StringUtils.isEmpty(businessKey)) {
            return Result.error("业务唯一标识不能为空");
        }

        if (approveType == null) {
            return Result.error("场景类型不能为空");
        }

        try {
            ScreenApprovalInstanceEntity instance = instanceMapper.selectByBusinessKey(businessKey, approveType,
                    version, List.of());

            if (instance == null) {
                return Result.error("未找到审批实例");
            }

            return queryDetail(instance.getInstanceCode());
        } catch (Exception e) {
            log.error("根据业务键查询审批详情异常", e);
            return Result.error("查询审批详情失败");
        }
    }

    @Override
    public Result<List<TaskDealCountVO>> getTaskDealCount(TaskDealCountParam param) {
        log.info("从本地数据库查询用户待办统计: {}", param);

        // 1. 确定要查询的用户
        String approveUser = UserThreadLocal.getUser().getWno();
        if (CollUtil.isNotEmpty(approvalRuleConfig.getAdminUser()) && approvalRuleConfig.getAdminUser().contains(approveUser)) {
            log.info("当前用户为管理员, 查询所有用户的待办统计。");
            approveUser = null;
        }

        // 2. 从本地数据库按【审批类型】统计【待办】数量
        List<TaskDealCountVO> counts = recordMapper.countTodoTasksByApprovalType(approveUser);

        // 3. 为统计结果赋予可读的名称
        if (CollUtil.isNotEmpty(counts)) {
            counts.forEach(item -> {
                item.setName(ApprovalTypeEnum.getDescByCode(item.getType()));
                item.setCode(approvalRuleConfig.getRuleCode(item.getType()));
            });
        }

        return Result.ok(counts);
    }

    @Override
    public InnerInstanceTaskVO getCurrentTask(String instanceCode) {
        log.info("查询当前任务: instanceCode={}", instanceCode);
        InnerInstanceTaskVO taskVO = null;
        try {
            taskVO = RpcUtils.unBox(innerApprovalRpc.queryTask(instanceCode));
        } catch (Exception e) {
            return new InnerInstanceTaskVO();
        }
        return taskVO;
    }

    @Override
    public Boolean judgeNextApproveUser(String instanceCode) {
        try {
            if (StringUtils.isBlank(instanceCode)) {
                // 没有审批实例  返回false
                return Boolean.FALSE;
            }

            // 获取下一节点
            InnerInstanceTaskVO currentTask = this.getCurrentTask(instanceCode);

            // 获取当前登录人id
            Integer userId = UserThreadLocal.getUserId();
            if (null == userId) {
                return false;
            }

            return userId.equals(currentTask.getUserId());
        } catch (Exception e) {
            log.error("判断下一个审批人是否是当前登录人失败：{}", e.getMessage());
            return Boolean.FALSE;
        }
    }

    @Override
    public InnerInstanceTaskVO getCurrentTask(String businessKey, String version) {
        log.info("查询当前任务: businessKey={}, version={}", businessKey, version);
        ScreenApprovalInstanceEntity instance = instanceMapper.selectByBusinessKey(businessKey, "", version, List.of());
        if (instance == null) {
            return null;
        }
        return this.getCurrentTask(instance.getInstanceCode());
    }

    @Override
    public Object getDetailParams(String instanceCode, Integer type) {
        ScreenApprovalInstanceEntity screenApprovalInstanceEntity = instanceMapper.selectByInstanceCode(instanceCode);
        if (screenApprovalInstanceEntity == null) {
            throw new ApprovalBusinessException("未找到审批实例");
        }

        ApprovalDetailVO detail = approvalAdapter.getDetail(instanceCode);
        if (detail == null) {
            throw new ApprovalBusinessException("未找到审批实例");
        }
        log.info("获取流程详情详情: {}", JSON.toJSONString(detail));
        Integer currentUserId = UserThreadLocal.getUserId();

        switch (type) {
            case 1, 3 -> {
                List<InnerApproveNodeVO> nodes = approvalAdapter.getNodes(instanceCode);
                if (CollUtil.isEmpty(nodes)) {
                    throw new ApprovalBusinessException("未找到审批节点");
                }
                // 使用Set，contains查询效率更高(O(1))
                Set<Integer> auditIdSet = nodes.stream()
                        .map(InnerApproveNodeVO::getUserId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                if (type == 1) {
                    // 校验审核人
                    if (!auditIdSet.contains(currentUserId)) {
                        throw new ApprovalBusinessException("您不是当前单据的审核人");
                    }
                } else {
                    // type == 3
                    // 提交人或审核人能看到详情
                    String creatorWno = screenApprovalInstanceEntity.getCreateBy();
                    String currentUserWno = UserThreadLocal.getUser().getWno();
                    if (!(currentUserWno.equals(creatorWno) || auditIdSet.contains(currentUserId))) {
                        throw new ApprovalBusinessException("您没有查看当前单据的权限");
                    }
                }
            }
            case 2 -> {
                // 校验抄送人
                if (Objects.isNull(detail.getCcReviewer()) || detail.getCcReviewer() == 0) {
                    throw new ApprovalBusinessException("您不是当前单据的抄送人");
                }
            }
            default -> {
            }
        }

        if (ApprovalTypeEnum.BUILDING_APPROVAL.getCode().equals(screenApprovalInstanceEntity.getApprovalType())) {
            return new HashMap<String, Object>() {{
                put("buildingNo", screenApprovalInstanceEntity.getBizCode());
                put("ratingVersion", screenApprovalInstanceEntity.getVersion());
                put("type", 1);
            }};
        } else if (ApprovalTypeEnum.COMPLETE_BUILDING_APPROVAL.getCode().equals(screenApprovalInstanceEntity.getApprovalType())) {
            return new HashMap<String, Object>() {{
                put("buildingNo", screenApprovalInstanceEntity.getBizCode());
                put("ratingVersion", screenApprovalInstanceEntity.getVersion());
                put("type", 2);
            }};
        } else if (ApprovalTypeEnum.PRICE_APPROVAL.getCode().equals(screenApprovalInstanceEntity.getApprovalType())) {
            LambdaQueryWrapper<PriceApplyEntity> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(PriceApplyEntity::getApplyCode, screenApprovalInstanceEntity.getBizCode());
            PriceApplyEntity priceApplyEntity = priceApplyDao.selectOne(queryWrapper);
            requireNonNull(priceApplyEntity, "价格申请数据不存在");
            return new HashMap<String, Object>() {{
                put("applyId", priceApplyEntity.getId());
                put("version", priceApplyEntity.getVersion());
            }};
        }
        return null;
    }


    @Override
    public InnerApproveNodeVO getPendingNode(String instanceCode) {
        List<InnerApproveNodeVO> nodes = RpcUtils.unBox(innerApprovalRpc.queryNodes(instanceCode));
        if (CollUtil.isEmpty(nodes)) {
            log.warn("从审批中心未获取到任何节点信息, instanceCode: {}", instanceCode);
            throw new ApprovalBusinessException("未找到任何审批节点");
        }
        return nodes.stream()
                .filter(node -> node != null && ApproveStatusEnum.PENDING.getCode().equals(node.getNodeStatus()))
                .findFirst()
                .orElseThrow(() -> {
                    log.error("在审批实例中未找到待处理(PENDING)的审批节点, instanceCode: {}", instanceCode);
                    return new ApprovalBusinessException("未找到待处理的审批节点");
                });
    }

    @Override
    public Boolean isTaskExist(String businessKey, String version, ApprovalTypeEnum type) {
        return instanceMapper.exists(Wrappers.<ScreenApprovalInstanceEntity>lambdaQuery()
                .eq(ScreenApprovalInstanceEntity::getBizCode, businessKey)
                .eq(ScreenApprovalInstanceEntity::getVersion, version)
                .eq(ScreenApprovalInstanceEntity::getApprovalType, type.getCode()));
    }

    private void fillTodoTask(List<TodoTaskDTO> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        // 批量获取提交人信息
        List<String> submitUsers = records.stream()
                .map(TodoTaskDTO::getSubmitUser)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        Map<String, CachedUser> userMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(submitUsers)) {
            userMap = userCacheHelper.getUserByWnos(submitUsers);
        }

        // 遍历记录并翻译字段
        for (TodoTaskDTO record : records) {
            // 翻译审批类型
            if (StringUtils.isNotBlank(record.getApprovalType())) {
                record.setTypeName(ApprovalTypeEnum.getDescByCode(record.getApprovalType()));
            }

            // 翻译审批结果
            if (StringUtils.isNotBlank(record.getApproveResult())) {
                record.setApproveResultName(ApproveActionEnum.getDescByCode(record.getApproveResult()));
            }

            // 翻译提交人姓名
            if (StringUtils.isNotBlank(record.getSubmitUser())) {
                record.setSubmitUserName(userMap.get(record.getSubmitUser()) != null
                        ? userMap.get(record.getSubmitUser()).getName()
                        : record.getSubmitUser());
            }
        }
    }

    /**
     * 删除approveTime是null 并且nodeStatus=0139-3的数据
     *
     * @param dtoList
     */
    private void filterNode(List<ScreenApproveRecordDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }

        // 步骤 1: 删除已取消且无审批时间的节点
        dtoList.removeIf(dto -> dto.getApproveTime() == null
                && ApproveStatusEnum.CANCELED.getCode().equals(dto.getNodeStatus()));

        // 步骤 2: 处理撤销节点的审批人交接逻辑
        // 如果一个节点被撤销，则它前面的一个节点的审批人应被设置为当前节点的审批人
        for (int i = dtoList.size() - 2; i >= 0; i--) {
            ScreenApproveRecordDTO current = dtoList.get(i);
            if (ApproveStatusEnum.CANCELED.getCode().equals(current.getNodeStatus())) {
                ScreenApproveRecordDTO pre = dtoList.get(i + 1);
                current.setApproveUser(pre.getApproveUser());
            }
        }

        // 3. 去掉除第一个节点外, approvalFlag=2 (结束) 的数据
        if (dtoList.size() > 1) {
            dtoList.subList(1, dtoList.size()).removeIf(dto -> Integer.valueOf(2).equals(dto.getApprovalFlag()));
        }
    }

    /**
     * 填充审批记录DTO列表，填充审批人姓名等信息。
     *
     * @param dtoList 待填充的DTO列表
     */
    private void fillApproveRecords(List<ScreenApproveRecordDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }

        // 批量获取审批人信息以优化性能
        List<String> userWnos = dtoList.stream()
                .map(ScreenApproveRecordDTO::getApproveUser)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .toList();

        Map<String, CachedUser> userMap = CollUtil.isNotEmpty(userWnos)
                ? userCacheHelper.getUserByWnos(userWnos)
                : Collections.emptyMap();

        dtoList.forEach(dto -> {
            if (StrUtil.isNotBlank(dto.getApproveUser())) {
                CachedUser user = userMap.get(dto.getApproveUser());
                dto.setApproveUserName(user != null ? user.getName() : dto.getApproveUser());
            }
            dto.setApprovalResultName(ApproveActionEnum.getDescByCode(dto.getApprovalResult()));
        });
    }

    /**
     * 在一组审批节点记录中，查找并标记当前所处的节点。
     *
     * @param dtoList 降序排列的审批节点列表
     */
    private void findFinishNode(List<ScreenApproveRecordDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }

        // 查找第一个状态为"待处理"的节点
        dtoList.stream()
                .filter(item -> ApproveStatusEnum.PENDING.getCode().equals(item.getNodeStatus())
                        && !ApproveCancelReason.APPLICANT_WITHDRAWN.getCode().equals(item.getCancelReason()))
                .findFirst()
                // 如果找到了，则将其标记为最新节点
                .ifPresentOrElse(
                        item -> item.setLatestNode(true),
                        // 如果没找到（例如，流程已结束或全部是其他状态），则默认将第一个节点标记为最新
                        () -> dtoList.get(0).setLatestNode(true)
                );
    }

    private TaskDealCountVO createDefaultTaskDealCountVO(Integer code) {
        TaskDealCountVO vo = new TaskDealCountVO();
        vo.setCode(code);
        vo.setCount(0);
        return vo;
    }
}