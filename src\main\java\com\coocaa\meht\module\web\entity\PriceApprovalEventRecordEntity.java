package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 事件记录表
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Data
@TableName("price_approval_event_record")
public class PriceApprovalEventRecordEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * UUID
     */
    private String uuid;

    /**
     * 令牌
     */
    private String token;

    /**
     * 时间戳
     */
    private String ts;

    /**
     * 类型
     */
    private String type;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 审批码
     */
    private String approvalCode;

    /**
     * 自定义键
     */
    private String customKey;

    /**
     * 定义键
     */
    private String defKey;

    /**
     * 生成类型
     */
    private String generateType;

    /**
     * 实例代码
     */
    private String instanceCode;

    /**
     * 开放ID
     */
    private String openId;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 租户键
     */
    private String tenantKey;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 定义代码
     */
    private String definitionCode;

    /**
     * 定义名称
     */
    private String definitionName;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 事件
     */
    private String event;

    /**
     * 实例操作时间
     */
    private String instanceOperateTime;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
