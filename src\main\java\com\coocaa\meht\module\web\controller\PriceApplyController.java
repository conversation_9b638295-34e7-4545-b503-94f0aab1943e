package com.coocaa.meht.module.web.controller;

import cn.hutool.core.collection.CollUtil;
import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.module.web.dto.PriceApplyDetailDto;
import com.coocaa.meht.module.web.dto.PriceApplyDto;
import com.coocaa.meht.module.web.dto.PriceApplyListCmsDto;
import com.coocaa.meht.module.web.dto.PriceApplyListDto;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.dto.point.ProjectPointVO;
import com.coocaa.meht.module.web.dto.req.PointByApplyNumberReq;
import com.coocaa.meht.module.web.dto.req.PriceApplyQueryCmsReq;
import com.coocaa.meht.module.web.dto.req.PriceApplyQueryReq;
import com.coocaa.meht.module.web.service.PriceApplyService;
import com.coocaa.meht.module.web.vo.PriceApplyDevicePointJoinVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 价格申请
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Tag(name = "价格申请")
@Slf4j
@RestController
@RequestMapping("/price/apply")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PriceApplyController {
    private final PriceApplyService priceApplyService;

    /**
     * 价格申请-列表
     */
    @Operation(summary = "价格申请分页列表查询")
    @PostMapping("/page")
    @Reqlog(value = "价格申请-列表", type = Reqlog.LogType.SELECT)
    public Result<PageResult<PriceApplyListDto>> list(@RequestBody PriceApplyQueryReq req) {
        return Result.ok(priceApplyService.listPriceApplyList(req));
    }

    /**
     * 价格申请-详情
     */
    @GetMapping("/{applyId}")
    @Anonymous
    @Reqlog(value = "价格申请-详情", type = Reqlog.LogType.INSERT)
    public Result<PriceApplyDetailDto> getDetail(@PathVariable(name = "applyId") Integer applyId,
                                                 @RequestParam(required = false)String version) {
        return Result.ok(priceApplyService.getPriceApplyDetail(applyId, version));
    }

    /**
     * 价格申请
     */
    @PostMapping
    @Reqlog(value = "价格申请", type = Reqlog.LogType.INSERT)
    public Result<Boolean> applyOrUpdate(@RequestBody PriceApplyDto applyDto) {
        return Result.ok(priceApplyService.apply(applyDto));
    }

    /**
     * 价格申请-审批
     */
//    @PostMapping("/{applyId}/approve")
//    @Reqlog(value = "价格申请-审批", type = Reqlog.LogType.INSERT)
//    public Result<Boolean> approve(@PathVariable(name = "applyId") Integer applyId,
//                                   @RequestBody PriceApplyApproveDto approveDto) {
//        approveDto.setApplyId(applyId);
//        return Result.ok(priceApplyService.approve(approveDto));
//    }


    /**
     * 价格申请-列表
     */
    @Anonymous
    @PostMapping("/cms")
    @Reqlog(value = "价格申请-列表cms", type = Reqlog.LogType.SELECT)
    public Result<PageResult<PriceApplyListCmsDto>> list(@RequestBody PriceApplyQueryCmsReq req) {
        return Result.ok(priceApplyService.listPriceApplyCms(req));
    }

    /**
     * 计算价格申请总金额
     */
    @Anonymous
    @PostMapping("/amount")
    @Reqlog(value = "价格申请-计算总金额-4cms", type = Reqlog.LogType.SELECT)
    public Result<BigDecimal> calculateTotalAmount(@RequestBody List<String> applyCodes) {
        return Result.ok(priceApplyService.calculateTotalAmount(applyCodes));
    }

    /**
     * 价格申请-点位列表
     */
    @GetMapping("/point/{buildingNo}/{businessCode}")
    @Reqlog(value = "价格申请-点位列表", type = Reqlog.LogType.SELECT)
    @Anonymous
    public Result<List<CodeNameVO>> pointList(@PathVariable("buildingNo") String buildingNo, @PathVariable("businessCode") String businessCode) {
        List<CodeNameVO> priceApplyDevicePointList = priceApplyService.getPriceApplyDevicePointList(buildingNo, businessCode);
        return Result.ok(priceApplyDevicePointList);
    }


    /**
     * 价格申请-h5详情安装信息
     */
    @GetMapping("/installation/information/{priceApplyDeviceId}")
    @Reqlog(value = "价格申请-h5详情安装信息", type = Reqlog.LogType.SELECT)
    @Anonymous
    public Result<ProjectPointVO> installationInformation(@PathVariable("priceApplyDeviceId") Integer priceApplyDeviceId,
                                                          @RequestParam(name = "version", required = false)String version,
                                                          @RequestParam(name = "applyCode", required = false)String applyCode) {
        ProjectPointVO installationInformation = priceApplyService.getInstallationInformation(priceApplyDeviceId, version, applyCode);
        return Result.ok(installationInformation);
    }

    /**
     * 价格申请-通过申请编码查看点位信息
     */
    @PostMapping("/points")
    @Reqlog(value = "价格申请-通过申请编码查看点位信息", type = Reqlog.LogType.SELECT)
    @Anonymous
    public Result<ProjectPointVO> pointByApplyNumber(@RequestBody PointByApplyNumberReq req) {
        ProjectPointVO projectPointVO = priceApplyService.pointByApplyNumber(req);
        return Result.ok(projectPointVO);
    }

    /**
     * 商机下点位
     */
    @GetMapping("/business/points")
    @Reqlog(value = "商机下点位", type = Reqlog.LogType.SELECT)
    public Result<List<PointDetail>> pointByBusinessCode(@RequestParam String businessCode) {
        List<PointDetail> pointDetailList = priceApplyService.pointByBusinessCode(businessCode);
        return Result.ok(pointDetailList);
    }

    /**
     * 商机下点位
     */
    @GetMapping("/business/ssp/points")
    @Reqlog(value = "商机下ssp点位", type = Reqlog.LogType.SELECT)
    public Result<List<PointDetail>> sspPointByBusinessCode(@RequestParam String businessCode) {
        List<PointDetail> pointDetailList = priceApplyService.sspPointByBusinessCode(businessCode);
        return Result.ok(pointDetailList);
    }

    /**
     * 价格申请-校验楼宇状态
     */
    @Operation(summary = "价格申请-校验楼宇状态")
    @GetMapping("/verify/building/status/{buildingNo}")
    @Reqlog(value = "价格申请-通过申请编码查看点位信息", type = Reqlog.LogType.SELECT)
    public Result<Boolean> checkBuildingStatus(@PathVariable String buildingNo) {
        boolean checkBuildingStatus = priceApplyService.checkBuildingStatus(buildingNo);
        return Result.ok(checkBuildingStatus);
    }

    /**
     * 价格申请-校验楼宇基因是否完整
     */
    @Operation(summary = "价格申请-校验楼宇基因是否完整")
    @GetMapping("/verify/building/gene/{buildingNo}")
    @Reqlog(value = "价格申请-校验楼宇基因是否完整", type = Reqlog.LogType.SELECT)
    public Result<Boolean> checkBuildingGene(@PathVariable String buildingNo) {
        List<String> res = priceApplyService.checkBuildingGene(buildingNo);
        if (res == null) {
            return new Result<>("未填写楼宇基因信息", false);
        }
        return new Result<>(CollUtil.isNotEmpty(res) ? String.join(",", res) : "", CollUtil.isEmpty(res));
    }

    /**
     * 更新价格申请分组大屏标记
     */
    @Anonymous
    @PutMapping("/device/{deviceId}/large-screen-flag")
    public Result<Boolean> updateDeviceLargeScreenFlag(@PathVariable(name = "deviceId") Integer deviceId,
                                                       @RequestParam(name = "largeScreen", required = false) Integer largeScreen,
                                                       @RequestParam(name = "coreArea", required = false) Integer coreArea) {
        return Result.ok(priceApplyService.updateDeviceLargeScreenFlag(deviceId, largeScreen, coreArea));
    }

    /**
     * 批量更新价格申请分组大屏标记
     */
    @Anonymous
    @PutMapping("/device/batch/large-screen-flag")
    public Result<Boolean> batchUpdateDeviceLargeScreenFlag() {
        return Result.ok(priceApplyService.batchUpdateLargeScreenFlag());
    }

    /** 批量更新“是否核心区域”标记 */
    @Anonymous
    @PutMapping("/device/batch/core-area-flag")
    public Result<Boolean> batchUpdateCoreAreaFlag() {
        return Result.ok(priceApplyService.batchUpdateCoreAreaFlag());
    }

    /** 根据点位code获取设备激励金数据 */
    @Anonymous
    @PostMapping("/device/incentive-price")
    public Result<List<PriceApplyDevicePointJoinVO>> getIncentivePrice(@RequestBody @NotEmpty(message = "点位code列表不能为空") Collection<String> pointCodes) {
        return Result.ok(priceApplyService.getIncentivePrice(pointCodes));
    }

    /**
     * 价格申请草稿保存
     */
    @Operation(summary = "价格申请草稿保存")
    @Reqlog(value = "价格申请草稿保存", type = Reqlog.LogType.INSERT)
    @PostMapping("/draft")
    public Result<Boolean> priceApplyDraft(@RequestBody PriceApplyDto applyDto) {
        return Result.ok(priceApplyService.priceApplyDraft(applyDto));
    }

    /**
     * 价格申请草稿删除
     */
    @Operation(summary = "价格申请草稿删除")
    @Reqlog(value = "价格申请草稿删除", type = Reqlog.LogType.DELETE)
    @DeleteMapping("/draft/{id}")
    public Result<Boolean> priceApplyDraftDelete(@PathVariable(name = "id") Integer id) {
        return Result.ok(priceApplyService.priceApplyDraftDelete(id));
    }

    /**
     * 价格申请个人草稿查询
     */
    @Operation(summary = "价格申请个人草稿查询")
    @Reqlog(value = "价格申请个人草稿查询", type = Reqlog.LogType.SELECT)
    @GetMapping("/{businessCode}/draft")
    public Result<PriceApplyDetailDto> getPriceApplyDraft(@PathVariable(name = "businessCode")String businessCode) {
        return Result.ok(priceApplyService.getPriceApplyDraft(businessCode));
    }
}
