package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.web.dto.BuildingParameterDto;
import com.coocaa.meht.module.web.dto.BuildingTypesDto;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BuildingParameterDao extends BaseMapper<BuildingParameterEntity> {

    List<BuildingTypesDto>  getBuildingType(@Param("dataFlag")Integer dataFlag);

    List<BuildingParameterDto>  getBuilding(@Param("buildingType") Long buildingType,@Param("dataFlag") Integer dataFlag);
    List<BuildingParameterDto>  getBuildingByParentId(@Param("parentId")Long parentId, @Param("buildingType")Integer buildingType,@Param("dataFlag") Integer dataFlag);
}
