package com.coocaa.meht.aop;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Date 2023-12-07 21:24
 */
@Documented
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Reqlog {

    String value();

    LogType type();

    enum LogType {
        LOGIN(0), //登录
        SELECT(1),//查看
        INSERT(2),//新增
        UPDATE(3),//修改
        DELETE(4);//删除
        private final int key;

        LogType(int key) {
            this.key = key;
        }

        public int getKey() {
            return key;
        }
    }
}
