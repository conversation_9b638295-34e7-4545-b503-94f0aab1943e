package com.coocaa.meht.module.web.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/8
 * @description 跟进类型
 */
@Getter
@AllArgsConstructor
public enum FollowTypeEnum {
    INTERVIEW( "面访"),
    PHONE( "电话");



    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 获取的描述
     */
    public static String getByName(String name) {
        for (FollowTypeEnum value : FollowTypeEnum.values()){
            if (value.name().equals(name)){
                return value.getDesc();
            }
        }
        return null;
    }
}
