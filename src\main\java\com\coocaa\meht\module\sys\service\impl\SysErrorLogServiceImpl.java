package com.coocaa.meht.module.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.api.fs.FsApiService;
import com.coocaa.meht.module.api.fs.FsMessageResp;
import com.coocaa.meht.module.api.fs.FsResult;
import com.coocaa.meht.module.sys.dao.SysErrorLogDao;
import com.coocaa.meht.module.sys.entity.SysErrorLogEntity;
import com.coocaa.meht.module.sys.service.SysConfigService;
import com.coocaa.meht.module.sys.service.SysErrorLogService;
import com.coocaa.meht.utils.Converts;
import com.coocaa.meht.utils.DateUtils;
import com.coocaa.meht.utils.ExceptionUtils;
import com.coocaa.meht.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 异常日志
 *
 * <AUTHOR>
 * @Date 2023-12-08 10:18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysErrorLogServiceImpl extends ServiceImpl<SysErrorLogDao, SysErrorLogEntity> implements SysErrorLogService {

    private final FsApiService fsApiService;
    private final SysConfigService sysConfigService;

    @EventListener(value = ExceptionUtils.ErrorEvent.class)
    public void onEventListener(ExceptionUtils.ErrorEvent event) {
        if (event != null) {
            Map<?, ?> map = (Map<?, ?>) event.getSource();
            SysErrorLogEntity entity = new SysErrorLogEntity();
            String title = Converts.toStr(map.get("title"), "");
            String errorLog = Converts.toStr(map.get("errorLog"), "");
            if (StringUtils.isBlank(title) && StringUtils.isBlank(errorLog)) {
                return;
            }
            entity.setTitle(title);
            Map<String, Object> logMap = new HashMap<>();
            logMap.put("text", errorLog);
            entity.setErrorLog(JsonUtils.toJson(logMap));
            entity.setCreateTime(DateUtils.toDateTime(event.getTimestamp()));
            this.save(entity);
            /*if (errorLog.contains("code\":230013")
                    || errorLog.contains("java.io.IOException: Broken pipe")
                    || errorLog.contains("{\"code\":230099,\"msg\":\"Failed to create card content, " +
                    "ext=path: message->card; err_msg: the request contains invalid image keys")) {
                return;
            }*/
            String user = Converts.toStr(map.get("user"));
            String path = Converts.toStr(map.get("path"));
            StringBuilder sb = new StringBuilder();
            if (StringUtils.isNotBlank(user)) {
                sb.append("<b>").append(user).append("</b>").append(" ")
                        .append("<i>").append(path).append("</i>").append("\n");
            } else if (StringUtils.isNotBlank(path)) {
                sb.append("<i>").append(path).append("</i>").append("\n");
            }
            if (StringUtils.isNotBlank(title)) {
                sb.append(title).append("\n");
            }
            sb.append(errorLog);
            String fsUserIds = sysConfigService.getVal("ExceptionUserFsIds", "608819368,606471541,CC0735");
            for (String fsId : fsUserIds.split(",")) {
                try {
                    fsApiService.sendTextMsgAsync(fsId, sb.toString());
                } catch (Exception e) {
                    log.error("发送飞书异常:", e);
                    this.save(new SysErrorLogEntity().setTitle("发送飞书消息异常")
                            .setErrorLog(ExceptionUtils.getMessage(e)));
                }

            }
        }
    }

}
