package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.common.CommonConstants;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.PointPicVo;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.RpcCommonService;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.converter.ConverterFactory;
import com.coocaa.meht.cos.ObjectUtils;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.building.vo.BuildingScreenVO;
import com.coocaa.meht.module.web.dao.PointMapper;
import com.coocaa.meht.module.web.dao.PointOldMapper;
import com.coocaa.meht.module.web.dto.convert.PointConvert;
import com.coocaa.meht.module.web.dto.point.ImagePoint;
import com.coocaa.meht.module.web.dto.point.PointDTO;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.dto.point.PointDetailParam;
import com.coocaa.meht.module.web.dto.point.PointVO;
import com.coocaa.meht.module.web.dto.point.ProjectPointCountVO;
import com.coocaa.meht.module.web.dto.point.ProjectPointVO;
import com.coocaa.meht.module.web.dto.point.WaitingHallPointVO;
import com.coocaa.meht.module.web.dto.point.WaitingHallVO;
import com.coocaa.meht.module.web.dto.point.WorkOrderPointQueryDTO;
import com.coocaa.meht.module.web.dto.req.PointByApplyNumberReq;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaImgRelationEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.PointEntity;
import com.coocaa.meht.module.web.entity.PointOldEntity;
import com.coocaa.meht.module.web.entity.PointPicEntity;
import com.coocaa.meht.module.web.entity.PointPlanEntity;
import com.coocaa.meht.module.web.entity.WaitingHallBusinessEntity;
import com.coocaa.meht.module.web.entity.WaitingHallEntity;
import com.coocaa.meht.module.web.enums.BuildingMetaImgTypeEnum;
import com.coocaa.meht.module.web.enums.PointPlanStatusEnum;
import com.coocaa.meht.module.web.service.BuildingGeneService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.IBuildingMetaImgRelationService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.service.PointPicService;
import com.coocaa.meht.module.web.service.PointPlanService;
import com.coocaa.meht.module.web.service.PointService;
import com.coocaa.meht.module.web.service.PriceApplyService;
import com.coocaa.meht.module.web.service.WaitingHallBusinessService;
import com.coocaa.meht.module.web.service.WaitingHallService;
import com.coocaa.meht.rpc.FeignSspRpc;
import com.coocaa.meht.rpc.dto.SspPointAddParam;
import com.coocaa.meht.rpc.dto.UpdatePointParam;
import com.coocaa.meht.utils.RsaExample;
import com.qcloud.cos.transfer.Download;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.sl.usermodel.PictureData;
import org.apache.poi.sl.usermodel.TableCell;
import org.apache.poi.sl.usermodel.TextParagraph;
import org.apache.poi.sl.usermodel.VerticalAlignment;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFBackground;
import org.apache.poi.xslf.usermodel.XSLFPictureData;
import org.apache.poi.xslf.usermodel.XSLFPictureShape;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFSlideMaster;
import org.apache.poi.xslf.usermodel.XSLFTable;
import org.apache.poi.xslf.usermodel.XSLFTableCell;
import org.apache.poi.xslf.usermodel.XSLFTableRow;
import org.apache.poi.xslf.usermodel.XSLFTextBox;
import org.apache.poi.xslf.usermodel.XSLFTextParagraph;
import org.apache.poi.xslf.usermodel.XSLFTextRun;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PointServiceImpl extends ServiceImpl<PointMapper, PointEntity> implements PointService {

    public static final int POINT_CODE_START = 100000;
    @Autowired
    private PointConvert pointConvert;
    @Autowired
    private FeignSspRpc feignSspRpc;
    @Autowired
    private WaitingHallService waitingHallService;
    @Autowired
    private BuildingRatingService buildingRatingService;
    @Autowired
    private RpcCommonService rpcCommonService;
    @Autowired
    private PointMapper pointMapper;
    @Autowired
    private PointOldMapper pointOldMapper;
    @Autowired
    private PointPlanService pointPlanService;
    @Autowired
    private PointPicService pointPicService;
    @Autowired
    private ConverterFactory converterFactory;
    @Autowired
    private WaitingHallBusinessService waitingHallBusinessService;
    @Autowired
    private BusinessOpportunityService businessOpportunityService;
    @Autowired
    private PriceApplyService priceApplyService;
    @Resource
    private RsaExample rsaExample;
    @Resource
    private BuildingGeneService buildingGeneService;

    @Resource
    private LargeScreenProperties largeScreenProperties;

    @Resource
    private IBuildingMetaService metaService;

    @Resource
    private IBuildingMetaImgRelationService metaImgRelationService;

    @Resource
    private BuildingScreenService buildingScreenService;

    private final static String[] PPT_TABLE_HEADERS = {"终端设备类型", "品牌+型号（或以派工单为准）", "安装区域", "安装位置", "安装数量"};

    // 点位PPT表格列宽映射，和PPT_TABLE_HEADERS的数量保持一致
    private final static Map<Integer, Integer> COLUMN_WIDTH_MAPPING = new HashMap<>(PPT_TABLE_HEADERS.length);

    // 点位PPT图片并发下载数
    @Value("${ppt.pic.concurrent.size:10}")
    private Integer pptPicConcurrentSize;

    @PostConstruct
    public void initWidth() {
        COLUMN_WIDTH_MAPPING.put(0, 100);
        COLUMN_WIDTH_MAPPING.put(1, 170);
        COLUMN_WIDTH_MAPPING.put(2, 180);
        COLUMN_WIDTH_MAPPING.put(3, 180);
        COLUMN_WIDTH_MAPPING.put(4, 70);
    }

    @Transactional
    @Override
    public void addPoint(PointDTO pointAddDTO) {
        PointPlanEntity pointPlanEntity = checkPointPlanStatus(pointAddDTO.getPointPlanId());
        BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(pointPlanEntity.getBuildingRatingNo());
        if (buildingRating == null) {
            throw new ServerException("未找到对应的楼宇信息");
        }
        checkRating(pointAddDTO, buildingRating, false);

        //调用ssp新增点位数据
        SspPointAddParam addParam = SspPointAddParam.builder()
                .remark(pointAddDTO.getDescription())
                .waitingHallId(pointAddDTO.getWaitingHallId())
                .images(pointAddDTO.getImages())
                .build();
        SspPointAddParam data = RpcUtils.unBox(feignSspRpc.addPoint(addParam));
        if (ObjectUtil.isNotEmpty(data)) {

            //新增H5的点位数据
            PointEntity pointEntity = pointConvert.toPointEntity(data);
            String userCode = SecurityUser.getUser().getUserCode();
            pointEntity.setCreateBy(userCode);
            pointEntity.setUpdateBy(userCode);
            pointEntity.setBuildingRatingNo(pointPlanEntity.getBuildingRatingNo());//楼宇编码需要记录
            pointEntity.setDeviceSize(pointAddDTO.getDeviceSize());
            pointEntity.setBusinessCode(pointPlanEntity.getBusinessCode());
            save(pointEntity);
        }
    }

    /**
     * 检查评级是否完整
     *
     * @param pointAddDTO
     * @param buildingRating
     */
    private void checkRating(PointDTO pointAddDTO, BuildingRatingEntity buildingRating, boolean isUpdate) {
        if (buildingRating == null) {
            String msg = String.format("无法%s大屏点位，大屏评级不存在！", isUpdate ? "编辑" : "新建");
            throw new ServerException(msg);
        }

        if (largeScreenProperties.getLargeDictKey().contains(pointAddDTO.getDeviceSize()) &&
                buildingRating.getLargeScreenRatingFlag().equals(0) &&
                !buildingScreenService.isLargeScreen(buildingRating)) {
            String msg = String.format("无法%s大屏点位，请在我的楼宇-完善评级完成大屏评级！", isUpdate ? "编辑" : "新建");
            throw new ServerException(msg);
        }
        if (buildingRating.getSmallScreenRatingFlag().equals(0) && CommonConstants.BUILDING_RATING_DES_FLAG.equals(buildingRating.getBuildingDesc())) {
            BuildingScreenVO screenVO = buildingScreenService.getBuildingScreenByNo(buildingRating.getBuildingNo());
            if (screenVO == null || StrUtil.isBlank(screenVO.getSpec())) {
                String msg = String.format("无法%s点位，请在我的楼宇-完善评级完成楼宇评级！", isUpdate ? "编辑" : "新建");
                throw new ServerException(msg);
            }
        }
    }

    private PointPlanEntity checkPointPlanStatus(Integer pointPlanId) {
        PointPlanEntity pointPlan = pointPlanService.getById(pointPlanId);
        if (pointPlan == null) {
            throw new ServerException("点位方案不存在");
        }
        String status = pointPlan.getStatus();
        if (PointPlanStatusEnum.REVIEW.getCode().equals(status)) {
            throw new ServerException("当前点位状态下,不允许改动点位数据.");
        }
        return pointPlan;
    }

    private PointPlanEntity checkUpdatePointPlanStatus(Integer pointPlanId, PointDTO pointAddDTO) {
        PointPlanEntity pointPlan = pointPlanService.getById(pointPlanId);
        if (pointPlan == null) {
            throw new ServerException("点位方案不存在");
        }
        String status = pointPlan.getStatus();
        if (PointPlanStatusEnum.REVIEW.getCode().equals(status)) {
            throw new ServerException("当前点位状态下,不允许改动点位数据.");
        }
        checkRating(pointAddDTO, buildingRatingService.getByBuildingNo(pointPlan.getBuildingRatingNo()), true);

        return pointPlan;
    }

    private void savePointPic(List<String> images, Integer pointId, String userCode) {
        if (CollectionUtils.isNotEmpty(images)) {
            List<PointPicEntity> pics = images.stream().map(e -> {
                PointPicEntity pointPicEntity = new PointPicEntity();
                pointPicEntity.setPointId(pointId);
                pointPicEntity.setPic(e);
                pointPicEntity.setCreateBy(userCode);
                return pointPicEntity;
            }).collect(Collectors.toList());
            pointPicService.saveBatch(pics);
        }
    }

    private CodeNameVO getCodeNameVO(PointDTO pointAddDTO) {
        Integer waitingHallId = pointAddDTO.getWaitingHallId();
        WaitingHallEntity wh = waitingHallService.getById(waitingHallId);
        String buildingRatingNo = wh.getBuildingRatingNo();
        BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(buildingRatingNo);
        String mapAdCode = buildingRating.getMapAdCode();
        return rpcCommonService.getCityByGbCode(mapAdCode);
    }

    private int getMaxNoByCity(Integer cityId) {
        QueryWrapper<PointEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(number) cityNumber");
        queryWrapper.eq("city", cityId);
        List<Map<String, Object>> maps = listMaps(queryWrapper);
        if (CollectionUtils.isEmpty(maps)) {
            return POINT_CODE_START;
        }
        Map<String, Object> map = maps.get(0);
        if (MapUtil.isEmpty(map)) {
            return POINT_CODE_START;
        }
        Object o = map.get("cityNumber");
        if (o == null) {
            return POINT_CODE_START;
        }
        return Integer.parseInt(o.toString());
    }

    @Override
    @Transactional
    public void updatePoint(PointDTO pointAddDTO) {
        checkUpdatePointPlanStatus(pointAddDTO.getPointPlanId(), pointAddDTO);
        UpdatePointParam updatePointParam = pointConvert.toUpdatePointParam(pointAddDTO);
        //调用ssp点位编辑
        RpcUtils.unBox(feignSspRpc.updatePoint(updatePointParam));
        //编辑H5中的点位设备大小
        PointEntity pointEntity = new PointEntity();
        pointEntity.setDeviceSize(pointAddDTO.getDeviceSize());
        updateByCode(pointAddDTO);
    }

    /**
     * 修改设备编码
     *
     * @param pointAddDTO
     */
    private void updateByCode(PointDTO pointAddDTO) {
        this.update(Wrappers.<PointEntity>lambdaUpdate().set(PointEntity::getDeviceSize, pointAddDTO.getDeviceSize())
                .set(PointEntity::getUpdateBy, SecurityUser.getUserCode())
                .set(PointEntity::getUpdateTime, LocalDateTime.now())
                .eq(PointEntity::getCode, pointAddDTO.getCode()));
    }

    @Override
    public ProjectPointVO listBuildingPoint(PointDetailParam param) {
        return getPlanPointVO(param);
    }

    public ProjectPointVO getPlanPointVO(PointDetailParam param) {
        Integer pointPlanId = param.getPointPlanId();
        PointPlanEntity pointPlan = pointPlanService.getById(pointPlanId);
        if (pointPlan == null) {
            throw new ServerException("点位方案不存在");
        }

        ProjectPointVO vo = getProjectPointVO(param);
        vo.setPointPlanStatus(pointPlan.getStatus());
        converterFactory.convert(Lists.newArrayList(vo));
        //top值
        String topLevel = buildingRatingService.topLevel(param.getBuildingNo());
        vo.setTopLevel(topLevel);
        return vo;
    }

    public ProjectPointVO getProjectPointVO(PointDetailParam param) {
        ProjectPointVO vo = new ProjectPointVO();
        String buildingNo = param.getBuildingNo();
        BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(buildingNo);
        String projectName = buildingRating.getBuildingName();
        vo.setBuildingNo(buildingNo);
        vo.setProjectName(projectName);
        vo.setProjectAddress(rsaExample.decryptByPrivate(buildingRating.getMapAddress()));

        //通过商机查询等候厅
        List<Integer> waitingHallIds = waitingHallBusinessService.findByBusinessCode(param.getBusinessCode());
        if (CollUtil.isEmpty(waitingHallIds)) {
            return vo;
        }

        //先通过商机查询本地商机下的点位列表
        List<PointDetail> localPoints = pointMapper.listByBusinessCodePoint(param.getBusinessCode());
        if (CollUtil.isEmpty(localPoints)) {
            return vo;
        }

        log.info("商机(code:{})下点位数据:{}", param.getBusinessCode(), JSON.toJSONString(localPoints));
        //从ssp查询点位列表详情
        log.info("ssp查询点位列表入参:{}", JSON.toJSONString(waitingHallIds));
        List<PointDetail> sspPoints = RpcUtils.unBox(feignSspRpc.findByPointList(waitingHallIds));
        log.info("ssp查询点位列表结果:{}", JSON.toJSONString(sspPoints));

        if (CollUtil.isEmpty(sspPoints)) {
            return vo;
        }

        // 过滤并处理ssp点位
        Map<String, List<PointDetail>> pointCodeMapping = localPoints.stream().collect(Collectors.groupingBy(PointDetail::getPointCode));
        List<PointDetail> validPoints = sspPoints.stream()
                .filter(sspPoint -> pointCodeMapping.containsKey(sspPoint.getPointCode()))
                .peek(sspPoint -> {
                    sspPoint.setBuildingRatingNo(param.getBuildingNo());
                    sspPoint.setBusinessCode(param.getBusinessCode());
                    List<PointDetail> points = pointCodeMapping.get(sspPoint.getPointCode());
                    if (CollectionUtils.isNotEmpty(points)) {
                        PointDetail pointDetail = points.get(0);
                        sspPoint.setDeviceSize(pointDetail.getDeviceSize());
                    }
                })
                .toList();
        converterFactory.convert(validPoints);
        vo.setPointDetails(validPoints);
        return vo;
    }

    @Override
    public ProjectPointVO listPriceApplyPoint(List<String> pointCodes, String buildingNo) {
        ProjectPointVO vo = new ProjectPointVO();
        if (CollectionUtils.isNotEmpty(pointCodes)) {
            ResultTemplate<List<PointDetail>> listResultTemplate = feignSspRpc.pointList(buildingNo);
            log.info("ssp返回的点位数据listResultTemplate:{}", listResultTemplate);
            List<PointDetail> list = listResultTemplate.getData().stream().filter(pointDetail -> pointCodes.contains(pointDetail.getPointCode())).toList();
            if (CollectionUtils.isEmpty(list)) return vo;

            Set<String> collect = list.stream().map(PointDetail::getPointCode).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(collect)) {
                new ServerException("与ssp的点位编码未匹配");
            }
            List<PointEntity> pointEntities = pointMapper.selectList(Wrappers.<PointEntity>lambdaQuery()
                    .in(PointEntity::getCode, collect));
            Map<String, String> pointMap = pointEntities.stream().collect(Collectors.toMap(PointEntity::getCode, PointEntity::getDeviceSize, (existing, replacement) -> {
                // 处理重复键的逻辑，这里选择保留第一个值
                return existing;
            }));
            list.forEach(s -> s.setDeviceSize(pointMap.get(s.getPointCode())));
            converterFactory.convert(list);
            log.info("ssp返回的点位数据convertlist:{}", list);
            vo.setPointDetails(list);
        }
        return vo;
    }


    @Override
    @Transactional
    public void deleteById(Integer pointId) {
        removeById(pointId);
        pointPicService.removeByPointId(pointId);
    }

    @Override
    @Transactional
    public void deleteByWaitingHallId(Integer waitingHallId) {
        // 1. 先查询该等候厅下的所有点位ID
        LambdaQueryWrapper<PointEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PointEntity::getWaitingHallId, waitingHallId);
        queryWrapper.select(PointEntity::getId);
        List<Integer> pointIds = list(queryWrapper)
                .stream()
                .map(PointEntity::getId)
                .collect(Collectors.toList());

        // 2. 如果有点位，则删除点位及其图片
        if (!CollectionUtils.isEmpty(pointIds)) {
            // 2.1 删除点位图片
            pointPicService.removeByPointIds(pointIds);
            // 2.2 删除点位
            remove(queryWrapper);
        }
    }

    @Override
    public void deleteBusinessCode(String businessCode) {
        LambdaQueryWrapper<PointEntity> queryWrapper = Wrappers.<PointEntity>lambdaQuery().eq(PointEntity::getBusinessCode, businessCode);
        remove(queryWrapper);
    }

    @Override
    @Transactional
    public void batchDeleteByWaitingHallIds(List<Integer> waitingHallIds) {
        if (CollectionUtils.isEmpty(waitingHallIds)) {
            return;
        }

        // 1. 先查询这些等候厅下的所有点位ID
        LambdaQueryWrapper<PointEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PointEntity::getWaitingHallId, waitingHallIds)
                .select(PointEntity::getId);

        List<Integer> pointIds = list(queryWrapper)
                .stream()
                .map(PointEntity::getId)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(pointIds)) {
            // 2. 批量删除点位图片
            pointPicService.removeByPointIds(pointIds);
            // 3. 批量删除点位
            remove(queryWrapper);
        }
    }


    @Override
    public List<String> getWaitingHallPointInformation(List<Integer> priceApplyDeviceIds) {
        return pointMapper.getWaitingHallPointInformation(priceApplyDeviceIds);
    }

    @Override
    public List<CodeNameVO> listPointToContract(String buildingNo, String businessCode) {

        ResultTemplate<List<PointDetail>> listResultTemplate = feignSspRpc.pointList(buildingNo);
        List<PointDetail> pointDetailDtos = listResultTemplate.getData();
        log.info("feignSspRpc点位数据：{},楼宇编码：{}", pointDetailDtos, buildingNo);
        List<PointEntity> pointEntities = pointMapper.selectList(Wrappers.<PointEntity>lambdaQuery()
                .eq(PointEntity::getBusinessCode, businessCode));
        Set<String> pointCodes = pointEntities.stream().map(PointEntity::getCode).collect(Collectors.toSet());
        List<PointDetail> collect = pointDetailDtos.stream().filter(e -> pointCodes.contains(e.getPointCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(pointDetailDtos)) {
            converterFactory.convert(collect);
            return collect.stream().map(e -> {
                //1栋 1单元 1层 大堂 1号电梯厅 点位1
                StringJoiner joiner = new StringJoiner("_");
                String v = joiner.add(e.getBuildingName())
                        .add(e.getUnitName())
                        .add(e.getFloorName())
                        .add(e.getWaitingHallTypeName())
                        .add(e.getWaitingHallName())
                        .add(e.getPointCode()).toString();
                return CodeNameVO.builder().name(v).code(e.getPointCode()).build();
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public String pptPoint(PointDetailParam param) {
        List<PointEntity> pointEntities = pointMapper.selectList(Wrappers.<PointEntity>lambdaQuery()
                .eq(PointEntity::getBusinessCode, param.getBusinessCode()));
        if (CollectionUtils.isEmpty(pointEntities)) {
            log.info("点位PPT导出，点位为空");
            return null;
        }

        //项目点位
        ProjectPointVO projectPointVO = getProjectPointVO(param);
        BusinessOpportunityEntity businessOpportunity = businessOpportunityService.getOne(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                .eq(BusinessOpportunityEntity::getCode, param.getBusinessCode()));
        if (businessOpportunity != null) {
            projectPointVO.setBusinessOpportunityName(businessOpportunity.getName());
        }

        //过滤点位编号为空的数据
        List<PointDetail> filteredPoints = projectPointVO.getPointDetails().stream().filter(e -> StringUtils.isNotBlank(e.getPointCode())).toList();
        if (CollUtil.isEmpty(filteredPoints)) {
            log.info("点位PPT导出，过滤后点位为空");
            return null;
        }
        projectPointVO.setPointDetails(filteredPoints);

        return makePointPpt(projectPointVO);
    }

    @Override
    public String workOrderPpt(List<WorkOrderPointQueryDTO> param) {
        log.info("工单点位数据：{}", param);
        if (CollectionUtils.isEmpty(param)) {
            return null;
        }
        converterFactory.convert(param);
        log.info("翻译后工单点位数据：{}", param);
        Map<String, WorkOrderPointQueryDTO> workOrderPointQueryDTOMap = param.stream().collect(Collectors.toMap(WorkOrderPointQueryDTO::getPointCode, e -> e, (v1, v2) -> v1));
        PointByApplyNumberReq pointByApplyNumberReq = new PointByApplyNumberReq();
        pointByApplyNumberReq.setBuildingNo(param.get(0).getBuildingNo());
        List<String> points = param.stream().map(WorkOrderPointQueryDTO::getPointCode).distinct().collect(Collectors.toList());
        pointByApplyNumberReq.setCodes(points);
        ProjectPointVO projectPointVO = priceApplyService.pointByApplyNumber(pointByApplyNumberReq);
        List<PointDetail> pointDetails = projectPointVO.getPointDetails();
        if (CollectionUtils.isEmpty(pointDetails)) {
            return null;
        }

        List<PointDetail> pointDetailList = pointDetails.stream().filter(e -> points.contains(e.getPointCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pointDetailList)) {
            throw new ServerException("点位与ssp点位不匹配");
        }
        pointDetailList.forEach(e -> {
            WorkOrderPointQueryDTO workOrderPointQueryDTO = workOrderPointQueryDTOMap.get(e.getPointCode());
            if (Objects.nonNull(workOrderPointQueryDTO)) {
                e.setBuildingName(workOrderPointQueryDTO.getBuildingName());
                e.setBuildingRatingNo(workOrderPointQueryDTO.getBuildingNo());
                e.setUnitName(workOrderPointQueryDTO.getUnitName());
                e.setFloorName(workOrderPointQueryDTO.getFloorName());
                e.setWaitingHallName(workOrderPointQueryDTO.getWaitingHallName());
                e.setWaitingHallType(workOrderPointQueryDTO.getWaitingHallType());
                e.setWaitingHallTypeName(workOrderPointQueryDTO.getWaitingHallTypeName());
                e.setDeviceSizeName(workOrderPointQueryDTO.getEquipmentSizeName());
            }

        });

        projectPointVO.setPointDetails(pointDetailList);
        Map<String, Long> groupByMap = param.stream().collect(Collectors.groupingBy(e -> e.getBuildingName() + "-"
                + e.getUnitName() + "-" + e.getFloorName() + "-" + e.getWaitingHallTypeName() + "-" + e.getWaitingHallName(), Collectors.counting()));
        log.info("工单点位统计信息map对象：{}", groupByMap);
        List<ProjectPointCountVO> projectPointCountVOS = new ArrayList<>();
        for (String s : groupByMap.keySet()) {
            ProjectPointCountVO projectPointCountVO = new ProjectPointCountVO();
            Long number = groupByMap.get(s);
            String[] split = s.split("-");
            for (int i = 0; i < split.length; i++) {
                if (i == 0) {
                    projectPointCountVO.setBuildingName(split[i]);
                } else if (i == 1) {
                    projectPointCountVO.setUnitName(split[i]);
                } else if (i == 2) {
                    projectPointCountVO.setFloorName(split[i]);
                } else if (i == 3) {
                    projectPointCountVO.setWaitingHallTypeName(split[i]);
                } else if (i == 4) {
                    projectPointCountVO.setWaitingHallName(split[i]);
                }
            }
            projectPointCountVO.setNumber(number.intValue());
            projectPointCountVOS.add(projectPointCountVO);
        }

        if (CollectionUtils.isEmpty(projectPointCountVOS) && CollectionUtils.isEmpty(projectPointVO.getPointDetails()) && Objects.isNull(projectPointVO)) {
            return null;
        }
        String makeppt = null;
        try {
            List<BuildingRatingEntity> list = buildingRatingService.lambdaQuery()
                    .eq(BuildingRatingEntity::getBuildingNo, param.get(0).getBuildingNo())
                    .list();
            if (ObjectUtil.isNotEmpty(list)) {
                projectPointVO.setProjectName(list.get(0).getBuildingName());
            }
            projectPointVO.setOrderCode(param.get(0).getOrderCode());
            makeppt = makeppt(projectPointVO, projectPointCountVOS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        //带你为url
        return makeppt;
    }

    @Override
    public void cleaningWaitingHallBusiness(String buildingRatingNo, String businessCode) {
        //先查询商机下等候厅
        List<Integer> byBusinessCode = waitingHallBusinessService.findByBusinessCode(businessCode);
        if (ObjectUtil.isNotEmpty(byBusinessCode)) {
            //删除原有数据，重新添加
            waitingHallBusinessService.deleteBusinessCode(businessCode);
        }
        LoginUser user = SecurityUser.getUser();
        //远程调用查询楼宇下的所有等候厅
        List<PointDetail> details = RpcUtils.unBox(feignSspRpc.pointList(buildingRatingNo));
        log.info("feignSspRpc点位数据：{}", details);
        if (ObjectUtil.isNotEmpty(details)) {
            Set<Integer> waitingIds = details.stream().filter(detail -> detail.getWaitingHallId() != null).map(detail -> detail.getWaitingHallId()).collect(Collectors.toSet());
            Set<String> pointCodes = details.stream().filter(detail -> StringUtils.isNotBlank(detail.getPointCode())).map(detail -> detail.getPointCode()).collect(Collectors.toSet());
            //数据组装
            if (ObjectUtil.isNotEmpty(waitingIds)) {
                List<WaitingHallBusinessEntity> entityList = waitingIds.stream().map(waiting -> {
                    return WaitingHallBusinessEntity.builder()
                            .waitingHallId(waiting).businessCode(businessCode)
                            .creator(user.getId().intValue()).build();
                }).collect(Collectors.toList());
                waitingHallBusinessService.saveBatch(entityList);
            }
            if (ObjectUtil.isNotEmpty(pointCodes)) {
                //先查询已有的点位进行更新商机的code
                LambdaQueryWrapper<PointEntity> wrapper = Wrappers.<PointEntity>lambdaQuery().in(PointEntity::getCode, pointCodes);
                List<PointEntity> entities = pointMapper.selectList(wrapper);
                if (ObjectUtil.isNotEmpty(entities)) {
                    Set<String> codes = entities.stream().map(PointEntity::getCode).collect(Collectors.toSet());
                    LambdaUpdateWrapper<PointEntity> updateWrapper = Wrappers.<PointEntity>lambdaUpdate()
                            .in(PointEntity::getCode, codes).set(PointEntity::getBusinessCode, businessCode).set(PointEntity::getUpdateBy, user.getUserCode());
                    pointMapper.update(updateWrapper);
                    pointCodes = pointCodes.stream().filter(point -> !codes.contains(point)).collect(Collectors.toSet());
                }
            }
            if (ObjectUtil.isNotEmpty(pointCodes)) {
                List<PointEntity> entityList = pointCodes.stream().map(point -> {
                    return PointEntity.builder()
                            .code(point).businessCode(businessCode)
                            .buildingRatingNo(buildingRatingNo)
                            .createBy(user.getUserCode())
                            .updateBy(user.getUserCode()).build();
                }).collect(Collectors.toList());
                pointMapper.insert(entityList);
            }
        }
    }

    @Override
    public void deleteByPointIds(List<String> pointCodes) {
        LambdaQueryWrapper<PointEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PointEntity::getCode, pointCodes);
        remove(queryWrapper);
    }

    public WaitingHallPointVO findWaitingHallAndPoint() {
        List<WaitingHallEntity> waitingHallList = waitingHallService.list();
        List<WaitingHallVO> waitingHallVOS = org.apache.commons.compress.utils.Lists.newArrayList();
        waitingHallList.stream().forEach(waitingHall -> {
            WaitingHallVO waitingHallVO = new WaitingHallVO();
            BeanUtils.copyProperties(waitingHall, waitingHallVO);
            waitingHallVOS.add(waitingHallVO);
        });
        List<PointOldEntity> pointList = pointOldMapper.selectList(Wrappers.<PointOldEntity>lambdaQuery());
        if (CollectionUtils.isEmpty(pointList)) {
            pointOldMapper.copyPointData();
        }
        pointList = pointOldMapper.selectList(Wrappers.<PointOldEntity>lambdaQuery());
        List<PointVO> pointVOS = org.apache.commons.compress.utils.Lists.newArrayList();
        pointList.stream().forEach(pointEntity -> {
            PointVO pointVO = new PointVO();
            BeanUtils.copyProperties(pointEntity, pointVO);
            pointVOS.add(pointVO);
        });
        return WaitingHallPointVO.builder().waitingHallVOS(waitingHallVOS).pointVOS(pointVOS).build();
    }

    @Override
    public List<PointPicVo> findPointPicList() {
        List<PointPicEntity> picEntities = pointPicService.list();
        List<PointPicVo> pointPicVos = picEntities.stream().map(e -> {
            PointPicVo pointPicVo = new PointPicVo();
            pointPicVo.setPointId(e.getPointId());
            pointPicVo.setPicUrl(e.getPic());
            return pointPicVo;
        }).collect(Collectors.toList());
        return pointPicVos;
    }

    @Override
    public List<String> getBui() {
        return pointMapper.getAllByBusinessCodeStrings();
    }


    /**
     * 点位信息生成ppt
     */
    public String makeppt(ProjectPointVO projectPointVO, List<ProjectPointCountVO> projectPointCountVOS) throws IOException {
        // 创建一个新的PPT文档
        XMLSlideShow ppt = new XMLSlideShow();
        ByteArrayOutputStream baos = null;
        try {
            //第一张幻灯片
            makeFirstSlide(ppt, projectPointVO);
            //第二张幻灯片
            makeSecondSlide(ppt, projectPointCountVOS);
            //点位幻灯片
            makePointSlide(ppt, projectPointVO);
            // 保存PPT文件
            baos = new ByteArrayOutputStream();
            ppt.write(baos);
            ppt.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 将ByteArrayOutputStream转换为InputStream
        InputStream inputStream = new ByteArrayInputStream(baos.toByteArray());
        // 上传文件（假设ObjectUtils.uploadFile方法接受InputStream）
        long timeStamp = System.currentTimeMillis();//文件时间戳
        if (StringUtils.isBlank(projectPointVO.getOrderCode())) {
            ObjectUtils.uploadFile(ObjectUtils.getCosFileName("meht", projectPointVO.getProjectName() + "点位方案_" + timeStamp + ".pptx"), inputStream);
            String pptUrl = ObjectUtils.getAccessUrl("meht", projectPointVO.getProjectName() + "点位方案_" + timeStamp + ".pptx");
            return pptUrl;
        } else if (StringUtils.isNotBlank(projectPointVO.getOrderCode())) {
            ObjectUtils.uploadFile(ObjectUtils.getCosFileName("meht", projectPointVO.getOrderCode() + "_" + projectPointVO.getProjectName() + "_" + timeStamp + ".pptx"), inputStream);
            String pptUrl = ObjectUtils.getAccessUrl("meht", projectPointVO.getOrderCode() + "_" + projectPointVO.getProjectName() + "_" + timeStamp + ".pptx");
            return pptUrl;
        }

        // 保存InputStream到本地文件
//        String filePath = "D:\\mydownload\\" + projectPointVO.getProjectName() + "点位方案" + ".pptx";
//        saveInputStreamToFile(inputStream, filePath);

        return null;

    }

    /**
     * 制作第一张幻灯片
     */
    public void makeFirstSlide(XMLSlideShow ppt, ProjectPointVO projectPointVO) {
        // 创建第一张幻灯片
        XSLFSlide slide = ppt.createSlide();
        XSLFTextBox textBox = slide.createTextBox();
        textBox.setText(projectPointVO.getProjectName() + "-点位方案");
        // 设置文本框的大小和位置，使其覆盖幻灯片的一部分
        textBox.setAnchor(new java.awt.Rectangle(50, 50, 600, 400)); // 792x516 是标准幻灯片大小

        // 获取文本段落并设置对齐方式
        List<XSLFTextParagraph> paragraphs = textBox.getTextParagraphs();
        textBox.setVerticalAlignment(VerticalAlignment.MIDDLE);
        XSLFTextParagraph paragraph = paragraphs.get(0);
        paragraph.setTextAlign(TextParagraph.TextAlign.CENTER); // 水平居中
        List<XSLFTextRun> textRuns = paragraph.getTextRuns();
        XSLFTextRun xslfTextRun = textRuns.get(0);
        xslfTextRun.setFontFamily("微软雅黑");
        xslfTextRun.setFontSize(40.0);
    }

    /**
     * 制作第二张幻灯片
     */
    public void makeSecondSlide(XMLSlideShow ppt, List<ProjectPointCountVO> projectPointCountVOS) {
        //创建第二张幻灯片
        XSLFSlide slide2 = ppt.createSlide();
        XSLFTextBox textBox1 = slide2.createTextBox();
        textBox1.setAnchor(new Rectangle(20, 50, 300, 100)); // 设置文本框的位置和大小
        // 设置文本内容并换行
        String text = "方案汇总\n点位数量总计：" + projectPointCountVOS.stream().mapToInt(ProjectPointCountVO::getNumber).sum();
        textBox1.setText(text);
        // 设置文本对齐方式
        List<XSLFTextParagraph> paragraphs2 = textBox1.getTextParagraphs();
        for (XSLFTextParagraph paragraph : paragraphs2) {
            paragraph.setTextAlign(TextParagraph.TextAlign.LEFT); // 左对齐
            for (XSLFTextRun run : paragraph.getTextRuns()) {
                run.setFontSize(20.0); // 设置字体大小为14
            }
        }

        //将projectPointCountVOS数据根据字段画表格并将数据填充到表格中生成到第二张幻灯片
        XSLFTable table = slide2.createTable(projectPointCountVOS.size() + 1, 6);
        // 设置表格的位置和大小
        table.setAnchor(new java.awt.Rectangle(20, 110, 600, 400));
        // 设置表格边框
        for (int i = 0; i < table.getRows().size(); i++) {
            XSLFTableRow xslfTableCells = table.getRows().get(i);//获取行
            if (i == 0) {//表头
                // 创建表头行
                String[] headers = {"序号", "楼栋", "单元", "楼层", "等候厅", "点位数量"};
                setCell(xslfTableCells, headers);
            } else {//数据行
                ProjectPointCountVO projectPointCountVO = projectPointCountVOS.get(i - 1);
                String[] cellText = {String.valueOf(i), projectPointCountVO.getBuildingName(), projectPointCountVO.getUnitName(), projectPointCountVO.getFloorName(),
                        projectPointCountVO.getWaitingHallName(), String.valueOf(projectPointCountVO.getNumber())};
                setCell(xslfTableCells, cellText);
            }

        }
    }

    /**
     * 数据列填充
     */
    public void setCell(XSLFTableRow row, String[] text) {
        for (int j = 0; j < row.getCells().size(); j++) {
            XSLFTableCell cell = row.getCells().get(j);
            cell.setBorderWidth(TableCell.BorderEdge.top, 1.0);
            cell.setBorderWidth(TableCell.BorderEdge.bottom, 1.0);
            cell.setBorderWidth(TableCell.BorderEdge.left, 1.0);
            cell.setBorderWidth(TableCell.BorderEdge.right, 1.0);
            cell.setBorderColor(TableCell.BorderEdge.top, Color.BLACK);
            cell.setBorderColor(TableCell.BorderEdge.bottom, Color.BLACK);
            cell.setBorderColor(TableCell.BorderEdge.left, Color.BLACK);
            cell.setBorderColor(TableCell.BorderEdge.right, Color.BLACK);
            cell.setText(text[j]);
            XSLFTextParagraph paragraph = cell.addNewTextParagraph();
            // 设置文本水平居中对齐
            paragraph.setTextAlign(TextParagraph.TextAlign.CENTER);
        }
    }

    /**
     * 制作点位幻灯片
     */
    public void makePointSlide(XMLSlideShow ppt, ProjectPointVO projectPointVO) {
        // 设置更大的数组长度限制
        IOUtils.setByteArrayMaxOverride(1000000000);
        //点位ppt生成
        List<PointDetail> pointDetails = projectPointVO.getPointDetails();
        for (PointDetail pointDetail : pointDetails) {
            XSLFSlide slidePoint = ppt.createSlide();
            XSLFTextBox textBoxPoint = slidePoint.createTextBox();
            textBoxPoint.setText(pointDetail.getBuildingName() + "-" + pointDetail.getUnitName() + "-" + pointDetail.getFloorName() + "-" + pointDetail.getWaitingHallName()
                    + "\n"
                    + pointDetail.getPointRemark() + " " + pointDetail.getDeviceSizeName() + "寸");
            textBoxPoint.setAnchor(new java.awt.Rectangle(50, 50, 600, 400));
            //插入图片
            int x = 50;
            int y = 150;
            int width = 150;
            int height = 100;
            for (String pic : pointDetail.getPointPics()) {
                if (pic != null && !pic.isEmpty()) {
                    try {
                        ImagePoint imageType = getImageType(pic);
                        File templateFile = File.createTempFile(UUID.fastUUID() + "_point_", imageType.getSuffix());
                        Download download = ObjectUtils.downloadFile(pic, templateFile);
                        download.waitForCompletion();
                        //将templateFile图片文件插入到ppt的幻灯片中
                        XSLFPictureData xslfPictureData = ppt.addPicture(templateFile, imageType.getPictureType());
                        XSLFPictureShape pictureShape = slidePoint.createPicture(xslfPictureData);
                        pictureShape.setAnchor(new Rectangle(x, y, width, height)); // 调整图片位置和大小
                        x = x + width + 5;
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }

                }
            }
        }


    }

    public void saveInputStreamToFile(InputStream inputStream, String filePath) throws IOException {
        File outputFile = new File(filePath);
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        } finally {
            inputStream.close();
        }
    }


    public ImagePoint getImageType(String imageUrl) {
        ImagePoint imagePoint = new ImagePoint();
        PictureData.PictureType picturetype = null;
        String suffix = "";
        if (imageUrl.toLowerCase().endsWith(".png")) {
            picturetype = PictureData.PictureType.PNG;
            suffix = ".png";
        } else if (imageUrl.toLowerCase().endsWith(".jpg")) {
            picturetype = PictureData.PictureType.JPEG;
            suffix = ".jpg";
        } else if (imageUrl.toLowerCase().endsWith(".jpeg")) {
            picturetype = PictureData.PictureType.JPEG;
            suffix = ".jpeg";
        }
        imagePoint.setPictureType(picturetype);
        imagePoint.setSuffix(suffix);
        return imagePoint;
    }

    /**
     * 生成点位PPT
     *
     * @param projectPointVO
     * @return cos服务器PPT文件下载链接
     */
    private String makePointPpt(ProjectPointVO projectPointVO) {
        // 创建PPT文档
        XMLSlideShow ppt = new XMLSlideShow();
        // 获取幻灯片母版
        XSLFSlideMaster master = ppt.getSlideMasters().get(0);
        // 在母版上设置背景色(淡橙色)
        XSLFBackground bg = master.getBackground();
        bg.setFillColor(new Color(254, 239, 230));

        // 制作封面
        makeCover(ppt, projectPointVO);

        // 设置更大的数组长度限制，避免图片过大被限制报错
        IOUtils.setByteArrayMaxOverride(1000000000);

        // 制作楼宇外观照
        makeAppearancePic(ppt, projectPointVO);

        // 制作表格
        makeTable(ppt, projectPointVO);

        // 制作点位图片
        makePictureConcurrent(ppt, projectPointVO);

        // 保存PPT文件
        ByteArrayOutputStream byteArrayOutputStream = null;
        InputStream inputStream = null;
        try {
            byteArrayOutputStream = new ByteArrayOutputStream();
            ppt.write(byteArrayOutputStream);
            ppt.close();

            inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

            // 上传文件（假设ObjectUtils.uploadFile方法接受InputStream）
            // 文件时间戳
            long timeStamp = System.currentTimeMillis();
            if (StringUtils.isBlank(projectPointVO.getOrderCode())) {
                ObjectUtils.uploadFile(ObjectUtils.getCosFileName("meht", projectPointVO.getProjectName() + "点位方案_" + timeStamp + ".pptx"), inputStream);
                return ObjectUtils.getAccessUrl("meht", projectPointVO.getProjectName() + "点位方案_" + timeStamp + ".pptx");
            } else if (StringUtils.isNotBlank(projectPointVO.getOrderCode())) {
                ObjectUtils.uploadFile(ObjectUtils.getCosFileName("meht", projectPointVO.getOrderCode() + "_" + projectPointVO.getProjectName() + "_" + timeStamp + ".pptx"), inputStream);
                return ObjectUtils.getAccessUrl("meht", projectPointVO.getOrderCode() + "_" + projectPointVO.getProjectName() + "_" + timeStamp + ".pptx");
            }
        } catch (IOException e) {
            log.error("导出异常", e);
            throw new RuntimeException("导出异常");
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            try {
                if (byteArrayOutputStream != null) {
                    byteArrayOutputStream.close();
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        return null;
    }

    private void makeAppearancePic(XMLSlideShow ppt, ProjectPointVO projectPointVO) {
        if (Objects.isNull(projectPointVO)) {
            return;
        }

        String buildingNo = projectPointVO.getBuildingNo();
        if (StrUtil.isBlank(buildingNo)) {
            return;
        }

        // 查询meta编号
        BuildingMetaEntity meta = metaService.lambdaQuery().select(BuildingMetaEntity::getBuildingMetaNo)
                .eq(BuildingMetaEntity::getBuildingRatingNo, buildingNo)
                .last("limit 1")
                .one();
        if (Objects.isNull(meta)) {
            log.info("meta数据不存在，buildingNo {}", buildingNo);
            return;
        }

        // 查询外观照
        List<BuildingMetaImgRelationEntity> pics = metaImgRelationService.lambdaQuery()
                .eq(BuildingMetaImgRelationEntity::getBuildingMetaNo, meta.getBuildingMetaNo())
                .eq(BuildingMetaImgRelationEntity::getImgType, BuildingMetaImgTypeEnum.EXTERIOR_PIC.getType())
                .list();
        if (CollUtil.isEmpty(pics)) {
            log.info("外观照不存在，buildingNo {}", buildingNo);
            return;
        }

        for (BuildingMetaImgRelationEntity pic : pics) {
            String imgUrl = pic.getImgUrl();
            if (StrUtil.isBlank(imgUrl)) {
                continue;
            }
            // 解析图片格式
            ImagePoint imageType = getImageType(imgUrl);
            if (Objects.isNull(imageType.getPictureType())) {
                log.info("图片格式错误，buildingNo {}，imgUrl {}", buildingNo, imgUrl);
                continue;
            }

            File file = null;
            try {
                // 下载图片
                file = File.createTempFile(UUID.fastUUID().toString(), imageType.getSuffix());
                Download download = ObjectUtils.downloadFile(imgUrl, file);
                download.waitForCompletion();

                // 创建一页ppt
                XSLFSlide slidePoint = ppt.createSlide();

                // 设置标题
                String title = String.format("项目名称：%s\n%s", projectPointVO.getProjectName(), projectPointVO.getProjectAddress());
                setTitle(title, slidePoint);

                // 写入图片
                XSLFPictureData pictureData = ppt.addPicture(file, imageType.getPictureType());
                XSLFPictureShape pictureShape = slidePoint.createPicture(pictureData);

                // 计算坐标尺寸
                double[] result = calculateCoordinates(pictureData, slidePoint);
                pictureShape.setAnchor(new Rectangle2D.Double(result[0], result[1], result[2], result[3]));
            } catch (Exception e) {
                log.error("写入外观照异常，buildingNo {}，imgUrl {}", buildingNo, imgUrl, e);
            } finally {
                if (Objects.nonNull(file) && file.exists()) {
                    file.delete();
                }
            }
        }
    }

    private void makePictureConcurrent(XMLSlideShow ppt, ProjectPointVO projectPointVO) {
        if (Objects.isNull(projectPointVO)) {
            return;
        }
        List<PointDetail> pointDetails = projectPointVO.getPointDetails();
        long start = System.currentTimeMillis();

        for (PointDetail pointDetail : pointDetails) {
            List<PointPicInfo> pointPicInfos = downloadPicture(pointDetail);

            try {
                doMakePicture(ppt, pointDetail, pointPicInfos);
            } catch (IOException e) {
                log.error("制作PPT图片异常", e);
                throw new RuntimeException("制作PPT图片异常");
            }
        }

        log.info("制作PPT图片耗时：{}", System.currentTimeMillis() - start);
    }

    private void doMakePicture(XMLSlideShow ppt, PointDetail pointDetail, List<PointPicInfo> pointPicInfos) throws IOException {
        if (CollUtil.isEmpty(pointPicInfos)) {
            XSLFSlide slidePoint = ppt.createSlide();
            // 没有图片的点位也需要设置标题占一页ppt
            setTitle(getPointTitle(pointDetail), slidePoint);
            return;
        }

        for (PointPicInfo pointPic : pointPicInfos) {
            XSLFSlide slidePoint = ppt.createSlide();
            // 设置标题
            setTitle(getPointTitle(pointDetail), slidePoint);

            try (FileInputStream fis = new FileInputStream(pointPic.getFile())) {
                // 使用流式处理方式添加图片
                XSLFPictureData pictureData = ppt.addPicture(fis, pointPic.getImagePoint().getPictureType());
                XSLFPictureShape pictureShape = slidePoint.createPicture(pictureData);

                // 计算坐标尺寸
                double[] result = calculateCoordinates(pictureData, slidePoint);

                pictureShape.setAnchor(new Rectangle2D.Double(result[0], result[1], result[2], result[3]));
            } finally {
                // 删除临时文件
                if (pointPic.getFile() != null && pointPic.getFile().exists()) {
                    pointPic.getFile().delete();
                }
            }
        }
    }

    private double[] calculateCoordinates(XSLFPictureData pictureData, XSLFSlide slidePoint) {
        // PPT尺寸
        double slideWidth = slidePoint.getSlideShow().getPageSize().getWidth();
        double slideHeight = slidePoint.getSlideShow().getPageSize().getHeight();
        // 预留标题空间
        slideHeight = slideHeight - 150;

        // 图片尺寸
        double imgWidth = pictureData.getImageDimension().getWidth();
        double imgHeight = pictureData.getImageDimension().getHeight();

        // 计算等比例缩放后的尺寸，取最小值，保证图片不会超出边界
        double scale = Math.min(
                // 按宽度缩放比例
                slideWidth / imgWidth,
                // 按高度比例缩放
                slideHeight / imgHeight
        );

        // 尺寸预留白边
        double scaledWidth = imgWidth * scale - 10;
        double scaledHeight = imgHeight * scale - 10;

        double x = (slideWidth - scaledWidth) / 2;
        // y坐标按预留空间下移部分距离
        double y = (slideHeight - scaledHeight) / 2 + 110;

        return new double[]{x, y, scaledWidth, scaledHeight};
    }

    private List<PointPicInfo> downloadPicture(PointDetail pointDetail) {
        if (CollUtil.isEmpty(pointDetail.getPointPics())) {
            return null;
        }

        // 转换为点位图片信息集合
        List<PointPicInfo> pointPicInfos = pointDetail.getPointPics().stream()
                .filter(StrUtil::isNotBlank)
                .map(pic -> PointPicInfo.builder()
                        .pointId(pointDetail.getPointId())
                        .picUrl(pic)
                        .imagePoint(getImageType(pic))
                        .build())
                .toList();

        try {
            List<List<PointPicInfo>> partitions = Lists.partition(pointPicInfos, pptPicConcurrentSize);
            for (List<PointPicInfo> subPartitions : partitions) {
                List<Download> downloads = new ArrayList<>(subPartitions.size());
                for (PointPicInfo pointPicInfo : subPartitions) {
                    File templateFile = File.createTempFile(UUID.fastUUID() + "_point_", pointPicInfo.getImagePoint().getSuffix());
                    log.info("点位（id:{}）下载图片({})", pointPicInfo.getPointId(), pointPicInfo.getPicUrl());
                    Download download = ObjectUtils.downloadFile(pointPicInfo.getPicUrl(), templateFile);
                    pointPicInfo.setFile(templateFile);
                    downloads.add(download);
                }

                for (Download download : downloads) {
                    download.waitForCompletion();
                }
            }

            return pointPicInfos;
        } catch (Exception e) {
            log.error("下载图片异常", e);
            throw new RuntimeException("下载图片异常");
        }
    }

    private void setTitle(String title, XSLFSlide slidePoint) {
        // 创建文本框
        XSLFTextBox textBox = slidePoint.createTextBox();
        textBox.setText(title);
        // 设置文本框位置尺寸
        textBox.setAnchor(new Rectangle(20, 20, 600, 400));
        // 获取文本段落
        List<XSLFTextParagraph> paragraphs = textBox.getTextParagraphs();
        paragraphs.forEach(
                paragraph -> paragraph.getTextRuns().forEach(
                        // 设置字体大小（单位：点pt）
                        textRun -> textRun.setFontSize(14.0)));
    }

    private static String getPointTitle(PointDetail pointDetail) {
        String title = String.format("等候厅：%s-%s-%s-%s\n尺寸：%s    点位编码：%s\n点位说明：%s", pointDetail.getBuildingName(),
                pointDetail.getUnitName(), pointDetail.getFloorName(), pointDetail.getWaitingHallName(),
                pointDetail.getDeviceSizeName(), pointDetail.getPointCode(), pointDetail.getPointRemark());
        return title;
    }

    private void makeTable(XMLSlideShow ppt, ProjectPointVO projectPointVO) {
        List<PointDetail> points = projectPointVO.getPointDetails();
        if (CollUtil.isEmpty(points)) {
            return;
        }

        // 按尺寸、楼栋、单元、楼层、等候厅分组
        Map<String, List<PointDetail>> groupedPointMap = points.stream().collect(Collectors.groupingBy(PointDetail::getGroupKey));
        List<PointDetail.Device> devices = new ArrayList<>(groupedPointMap.size());
        for (Map.Entry<String, List<PointDetail>> entry : groupedPointMap.entrySet()) {
            List<PointDetail> groupedPoints = entry.getValue();
            if (CollUtil.isEmpty(groupedPoints)) {
                continue;
            }

            PointDetail point = groupedPoints.get(0);
            PointDetail.Device device = new PointDetail.Device();
            device.setModel(point.getDeviceSizeName() + "(寸)");
            device.setArea(point.getInstallArea());
            device.setLocation(point.getInstallLocation());
            device.setCount(groupedPoints.size());
            devices.add(device);
        }
        if (CollUtil.isEmpty(devices)) {
            return;
        }

        // 按安装位置，安装区域，设备尺寸排序
        devices.sort(Comparator.comparing(PointDetail.Device::getLocation)
                .thenComparing(PointDetail.Device::getArea)
                .thenComparing(PointDetail.Device::getModel));

        // 总点位数
        int totalPointCount = devices.stream().mapToInt(PointDetail.Device::getCount).sum();

        // 每页表格最大行数
        int rowNum = 21;

        // 每页表格数据行数
        List<List<PointDetail.Device>> partitionDevices = partition(devices, rowNum - 3, rowNum - 1);

        int pageIndex = 0;
        for (List<PointDetail.Device> subDevices : partitionDevices) {
            XSLFSlide slide = ppt.createSlide();

            if (pageIndex == 0) {
                // 首页添加统计信息
                setStatistics(totalPointCount, slide);
            }

            // 添加表格
            // 实际行数
            int rowCount = pageIndex == 0 ? subDevices.size() + 3 : subDevices.size() + 1;
            XSLFTable table = slide.createTable(rowCount, PPT_TABLE_HEADERS.length);
            // 设置表格的位置和大小
            table.setAnchor(new java.awt.Rectangle(10, 70, 700, 400));
            // 设置列宽
            for (int i = 0; i < table.getNumberOfColumns(); i++) {
                table.setColumnWidth(i, COLUMN_WIDTH_MAPPING.get(i));
            }

            if (pageIndex == 0) {
                // 首页表格添加物业项目名称，项目地址
                // 合并后4列单元格
                table.mergeCells(0, 0, 1, 4);
                setPointCell(table.getRows().get(0), new String[]{"物业项目名称", projectPointVO.getProjectName()});
                table.mergeCells(1, 1, 1, 4);
                setPointCell(table.getRows().get(1), new String[]{"项目地址", projectPointVO.getProjectAddress()});

                // 设置文本左对齐
                setTextAlign(table.getCell(0, 1), TextParagraph.TextAlign.LEFT);
                setTextAlign(table.getCell(1, 1), TextParagraph.TextAlign.LEFT);
            }

            // 填充数据
            List<XSLFTableRow> rows = table.getRows();
            for (int ri = 0; ri < rows.size(); ri++) {
                XSLFTableRow row = rows.get(ri);

                if (pageIndex == 0) {
                    // 首页表格跳过前两行添加的物业项目名称，项目地址
                    if (ri == 2) {
                        // 填充表头
                        setPointCell(row, PPT_TABLE_HEADERS);
                    }
                    if (ri > 2) {
                        // 填充数据行
                        fillRowData(subDevices.get(ri - 3), row);
                    }
                } else {
                    // 后续页表格
                    if (ri == 0) {
                        // 填充表头
                        setPointCell(row, PPT_TABLE_HEADERS);
                    } else {
                        // 填充数据行
                        fillRowData(subDevices.get(ri - 1), row);
                    }
                }
            }

            pageIndex++;
        }
    }

    private void setTextAlign(XSLFTableCell cell, TextParagraph.TextAlign textAlign) {
        for (XSLFTextParagraph paragraph : cell.getTextParagraphs()) {
            paragraph.setTextAlign(textAlign);
        }
    }

    private void fillRowData(PointDetail.Device device, XSLFTableRow row) {
        String[] cellText = {device.getType(), device.getModel(), device.getArea(),
                device.getLocation(), String.valueOf(device.getCount())};
        setPointCell(row, cellText);
    }

    private <T> List<List<T>> partition(Collection<T> collection, int firstSize, int followSize) {
        if (CollUtil.isEmpty(collection)) {
            return Collections.emptyList();
        }

        // 处理第一组
        List<T> firstGroup = collection.stream()
                .limit(firstSize)
                .collect(Collectors.toList());

        // 剩余数据
        List<T> remaining = collection.stream()
                .skip(firstSize)
                .collect(Collectors.toList());
        List<List<T>> remainingGroups = Lists.partition(remaining, followSize);

        // 合并结果
        List<List<T>> result = new ArrayList<>(remainingGroups.size() + 1);
        result.add(firstGroup);
        result.addAll(remainingGroups);
        return result;
    }

    public void setPointCell(XSLFTableRow row, String[] texts) {
        for (int i = 0; i < texts.length; i++) {
            XSLFTableCell cell = row.getCells().get(i);
            cell.setBorderWidth(TableCell.BorderEdge.top, 1.0);
            cell.setBorderWidth(TableCell.BorderEdge.bottom, 1.0);
            cell.setBorderWidth(TableCell.BorderEdge.left, 1.0);
            cell.setBorderWidth(TableCell.BorderEdge.right, 1.0);
            cell.setBorderColor(TableCell.BorderEdge.top, Color.BLACK);
            cell.setBorderColor(TableCell.BorderEdge.bottom, Color.BLACK);
            cell.setBorderColor(TableCell.BorderEdge.left, Color.BLACK);
            cell.setBorderColor(TableCell.BorderEdge.right, Color.BLACK);
            cell.setText(texts[i]);
            List<XSLFTextParagraph> textParagraphs = cell.getTextParagraphs();
            for (XSLFTextParagraph paragraph : textParagraphs) {
                // 设置文本水平居中对齐
                paragraph.setTextAlign(TextParagraph.TextAlign.CENTER);
                // 设置字体大小
                paragraph.getTextRuns().forEach(textRun -> textRun.setFontSize(12.0));
            }
        }
    }

    private void setStatistics(int num, XSLFSlide slide) {
        XSLFTextBox textBox = slide.createTextBox();
        // 设置文本框的位置和大小
        textBox.setAnchor(new Rectangle(20, 20, 300, 100));
        // 设置文本内容并换行
        String text = "点位数量总计：" + num;
        textBox.setText(text);
        // 设置文本对齐方式
        List<XSLFTextParagraph> paragraphs = textBox.getTextParagraphs();
        for (XSLFTextParagraph paragraph : paragraphs) {
            // 左对齐
            paragraph.setTextAlign(TextParagraph.TextAlign.LEFT);
            for (XSLFTextRun run : paragraph.getTextRuns()) {
                // 设置字体大小为14
                run.setFontSize(14.0);
            }
        }
    }

    private void makeCover(XMLSlideShow ppt, ProjectPointVO projectPointVO) {
        if (Objects.isNull(projectPointVO)) {
            return;
        }

        // 创建第一张幻灯片
        XSLFSlide slide = ppt.createSlide();

        // 创建文本框
        XSLFTextBox textBox = slide.createTextBox();
        // 设置文本框的大小和位置，使其覆盖幻灯片的一部分
        textBox.setAnchor(new java.awt.Rectangle(50, 30, 600, 400));
        textBox.setVerticalAlignment(VerticalAlignment.MIDDLE);

        // 第一行文字
        XSLFTextParagraph paragraph = textBox.addNewTextParagraph();
        // 设置文本水平居中
        paragraph.setTextAlign(TextParagraph.TextAlign.CENTER);
        XSLFTextRun run1 = paragraph.addNewTextRun();
        run1.setText("点位方案\n");
        // 设置字体大小
        run1.setFontSize(50.0);
        // 设置字体
        run1.setFontFamily("微软雅黑");

        // 第二行文字
        XSLFTextParagraph paragraphContent = textBox.addNewTextParagraph();
        paragraphContent.setTextAlign(TextParagraph.TextAlign.LEFT);
        XSLFTextRun run2 = paragraphContent.addNewTextRun();
        run2.setText("商机名称：" + projectPointVO.getBusinessOpportunityName() + "\n");
        run2.setFontSize(20.0);
        run2.setFontFamily("微软雅黑");

        // 第三行文字
        XSLFTextRun run3 = paragraphContent.addNewTextRun();
        run3.setText("项目名称：" + projectPointVO.getProjectName() + "\n");
        run3.setFontSize(20.0);
        run3.setFontFamily("微软雅黑");

        // 第四行文字
        XSLFTextRun run4 = paragraphContent.addNewTextRun();
        run4.setText("项目地址：" + projectPointVO.getProjectAddress());
        run4.setFontSize(20.0);
        run4.setFontFamily("微软雅黑");
    }

    @Data
    @Builder
    static class PointPicInfo {

        private Integer pointId;

        private String picUrl;

        private File file;

        private ImagePoint imagePoint;

    }


}