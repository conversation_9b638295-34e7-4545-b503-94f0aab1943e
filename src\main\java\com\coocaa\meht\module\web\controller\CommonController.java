package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.RpcCommonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共数据
 * <AUTHOR>
 * @since 2024/12/13
 */

@Tag(name = "一些公共的调用")
@RestController
@RequestMapping("/common")
public class CommonController {
    @Autowired
    private RpcCommonService rpcCommonService;

    @Operation(summary = "根据字典父code查询字典下拉列表")
    @GetMapping("/dict/select/{code}")
    public Result<List<CodeNameVO>> dictSelect(@PathVariable("code") String code) {
        return Result.ok(rpcCommonService.listDictSelect(code));
    }
}
