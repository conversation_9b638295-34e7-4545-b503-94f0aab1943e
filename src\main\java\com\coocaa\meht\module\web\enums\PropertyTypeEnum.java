package com.coocaa.meht.module.web.enums;

import lombok.Getter;

@Getter
public enum PropertyTypeEnum {
    OFFICE("写字楼", "0002-1"),
    MIXED("综合体", "0002-2"),
    GLOBE("商住楼", "0002-3"),
    RESIDENCE("住宅", "0002-4"),
    PARK("产业园区", "0002-5");

    private String desc;

    private String code;

    PropertyTypeEnum(String desc, String code) {
        this.desc = desc;
        this.code = code;
    }
}
