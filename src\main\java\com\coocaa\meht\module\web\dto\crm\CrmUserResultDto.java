package com.coocaa.meht.module.web.dto.crm;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CrmUserResultDto extends CrmResultDto {

    private ResultDto data;




    @Data
    @Accessors(chain = true)
    public static class ResultDto {

        private List<userDto> list;

    }

    @Data
    @Accessors(chain = true)
    public static class userDto {


        private String realname;

        private String mobile;

        private String userId;

    }
}
