package com.coocaa.meht.module.approve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 任务处理统计视图对象
 *
 * <AUTHOR>
 * @description 任务处理统计视图对象
 * @since 2025-06-17
 */

@Data
@Schema(description = "任务处理统计视图对象")
public class TaskDealCountVO {

    @Schema(description = "审批规则编号")
    private Integer code;

    @Schema(description = "审批规则类型 字典0164")
    private String type;

    @Schema(description = "审批规则名称")
    private String name;

    @Schema(description = "待办数量")
    private Integer count;
} 