package com.coocaa.meht.module.web.dto;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/1/8
 * @description 商机
 */
@Data
public class BusinessOpportunityDto {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 商机名称
     */
    @Schema(description = "商机名称")
    private String name;
    /**
     * 商机编号
     */
    @Schema(description = "商机编号")
    private String code;
    /**
     * 商机状态
     */
    @Schema(description = "商机状态")
    private String status;
    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String customerId;

    /**
     * building_rating表的building_no
     */
    @Schema(description = "building_rating表的building_no")
    private String buildingNo;
    /**
     * 提交人
     */
    @Schema(description = "提交人")
    private String submitUser;
    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    private String owner;
}
