package com.coocaa.meht.module.web.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 商机状态枚举,desc的值主要用于crm不要修改
 */
@Getter
@AllArgsConstructor
public enum BusinessChangeStatusEnum {
    TO_BE_DISCUSSED("0043-1", "待洽谈"),
    PRELIMINARY_NEGOTIATIONS("0043-2", "初步洽谈"),
    REACHING_INTENTION("0043-3", "达成意向"),
    PROPOSAL_QUOTATION("0043-4", "方案报价"),
    CONTRACT_PHASE("0043-5", "合同流程"),
    DEAL("0043-6", "成交"),
    CLOSE("0043-7", "关闭");

    /**
     * 字典编码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;


    /**
     * 获取的描述
     */
    public static String getByCode(String code) {
        for (BusinessChangeStatusEnum value : BusinessChangeStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
    /**
     * 获取的编码
     */
    public static String getByDesc(String desc) {
        for (BusinessChangeStatusEnum value : BusinessChangeStatusEnum.values()) {
            if (value.desc.equals(desc)) {
                return value.getCode();
            }
        }
        return null;
    }
}
