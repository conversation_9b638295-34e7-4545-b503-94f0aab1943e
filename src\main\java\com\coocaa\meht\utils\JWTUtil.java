package com.coocaa.meht.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;

/**
 * <AUTHOR>
 * @since 2024/10/22
 */

@Component
public class JWTUtil implements InitializingBean {

    @Value("${jwt.secret}")
    private String secretKey;

    @Value("${jwt.exp}")
    private  int exp;

    private static int expireTime ;

    private static String key;

    private static Algorithm algorithm;
    @PostConstruct
    public void init() {
        expireTime = exp;
    }

    public static String getToken(String name, Integer id) {
        return getExpireCommon(name, id, expireTime);
    }

    public static String getExpireCommon(String name, Integer id, int exp) {
        // 生成Token
        return JWT.create()
                .withIssuer("auth0")
                .withClaim("id", id)
                .withClaim("name", name)
                .withClaim("timestamp",System.currentTimeMillis())
                .withExpiresAt(Instant.now().plusSeconds(exp))
                .sign(algorithm);
    }

    public static DecodedJWT  parseToken(String token) {
        Algorithm algorithm = Algorithm.HMAC256(key);
        JWTVerifier verifier = JWT.require(algorithm)
                .withIssuer("auth0")
                .build();

        return verifier.verify(token);

    }

    public static Integer getUserIdByToken(String token){
        return parseToken(token).getClaim("id").asInt();
    }

    /**
     * 判断是否过期
     * */
    public static boolean expire(long exp){
        return System.currentTimeMillis()>exp;
    }
    @Override
    public void afterPropertiesSet() throws Exception {
       // key = Keys.hmacShaKeyFor(secretKey.getBytes(StandardCharsets.UTF_8));
        this.key = secretKey;
        this.algorithm = Algorithm.HMAC256(key);
    }


}
