@startuml
title 获取价格申请详情 GET /price/apply/detail/{id}

start

:调用 getById(id) 获取 PriceApplyEntity;
if (是否是草稿状态?) then (是)
    :从 draft 字段解析为 PriceApplyDetailDto;
    :设置项目名称 projectName;
    :设置项目类型 projectType;
    :设置评分 score;
    :返回 dto;
else (否)
    :检查 priceApply 是否存在;
    :检查楼宇信息 buildingRatingEntity 是否存在;
    :检查商机信息 businessOpportunity 是否存在;

    :转换为 PriceApplyDetailDto;
    :设置项目信息（名称、类型、评分）;
    :拼接地址信息 mapAddress;
    :解密经纬度和详细地址;
    :设置创建人姓名 createName;
    :设置商机名称 businessName;

    if (兼容 H5?) then (是)
        :判断是否本人申请 isSelfApply;
    endif

    if (审批人不为空?) then (是)
        :获取审批人姓名 approveName;
    endif

    if (创建人不为空?) then (是)
        :再次确认创建人姓名 createName;
    endif

    :查询设备列表 applyDeviceList;
    if (设备列表非空?) then (是)
        :获取设备点位信息 priceApplyDevicePointEntities;
        :构建 devicePointMap 映射表;

        :初始化 deviceDetailDtoList;
        while (循环设备数据applyDeviceList)
            :转换为 DeviceDetailDto;
            if (存在点位信息?)
                :转换为 DevicePointDto 列表并设置;
                :计算总单价 totalUnitPrice;
            endif
            :添加进 deviceDetailDtoList;
        endwhile

        :设置设备列表 devices;
        :统计点位数量 pointCount;
    endif

    :处理附件信息 setFiles();
    note right: 查询附件信息等
    :查询审批记录 approveRecords;
    :判断是否当前用户为下一个审批人 setIsSelfApprove;
endif

:返回 PriceApplyDetailDto;

stop

@enduml