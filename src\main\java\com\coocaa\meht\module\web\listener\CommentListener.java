package com.coocaa.meht.module.web.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.module.web.dto.CommentCreateDTO;
import com.coocaa.meht.module.web.enums.BusinessTypeEnum;
import com.coocaa.meht.module.web.vo.common.UserVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.dto.UserFeiShuMessageParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 评论事件监听器
 * <p>
 * 监听评论创建事件，并发送飞书通知
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-29
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CommentListener {

    private final FeignAuthorityRpc authorityRpc;

    /**
     * 应用域名
     */
    @Value("${domainNameApp}")
    private String domain;

    /**
     * 飞书应用编码（字典0124）
     */
    private static final String APP_CODE = "0124-1";

    /**
     * 楼宇评级详情页面URL模板
     */
    private static final String BUILDING_RATING_URL = "/buildApprovalDetail?buildingNo=%s&type=%s&ratingVersion=%s";

    /**
     * 价格申请详情页面URL模板
     */
    private static final String PRICE_APPLY_URL = "/price-detail?applyId=%s&version=%s";

    /**
     * 飞书消息查看详情按钮文本
     */
    private static final String URL_TEXT = "查看详情";

    /**
     * 发送通知最高长度
     */
    private static final int MAX_CONTENT_LENGTH = 100;


    /**
     * 监听评论创建事件，并发送飞书通知
     *
     * @param comment 评论创建信息
     */
    @Async
    @EventListener
    public void onComment(CommentCreateDTO comment) {
        if (comment == null) {
            log.error("评论事件处理失败: 评论对象为空");
            return;
        }

        log.info("收到评论事件: 业务类型={}, 业务ID={}, 内容={}",
                comment.getBusinessType(), comment.getBusinessId(), comment.getContent());

        try {
            // 1. 校验参数
            BusinessTypeEnum businessType = BusinessTypeEnum.getByValue(comment.getBusinessType());
            if (businessType == null) {
                log.error("评论事件处理失败: 无效的业务类型, businessType={}", comment.getBusinessType());
                return;
            }

            if (StringUtils.isBlank(comment.getBusinessId())) {
                log.error("评论事件处理失败: 业务ID为空");
                return;
            }

            if (StringUtils.isBlank(comment.getContent())) {
                log.error("评论事件处理失败: 评论内容为空");
                return;
            }

            if (StringUtils.isBlank(comment.getSender())) {
                log.error("评论事件处理失败: 发送者为空");
                return;
            }

            if (CollUtil.isEmpty(comment.getNotifiedUsers())) {
                log.error("评论事件处理失败: 通知用户列表为空");
                return;
            }

            // 2. 构建飞书消息参数
            UserFeiShuMessageParam param = buildMessageParam(comment, businessType);

            // 3. 获取用户信息
            List<String> wnoList = new ArrayList<>(comment.getNotifiedUsers());
            wnoList.add(comment.getSender());

            // 去除空值和重复值
            wnoList = wnoList.stream()
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(wnoList)) {
                log.error("评论事件处理失败: 有效用户列表为空");
                return;
            }

            List<UserVO> userList;
            try {
                userList = RpcUtils.unBox(authorityRpc.listUserByWnos(wnoList));
            } catch (Exception e) {
                log.error("获取用户信息失败: {}", e.getMessage(), e);
                return;
            }

            if (CollUtil.isEmpty(userList)) {
                log.error("评论通知处理失败: 未找到相关用户信息, wnoList={}", wnoList);
                return;
            }

            // 4. 构建工号与ID的映射关系
            Map<String, Integer> userMap = userList.stream()
                    .filter(user -> StringUtils.isNotBlank(user.getWno()) && user.getId() != null)
                    .collect(Collectors.toMap(
                            UserVO::getWno,
                            UserVO::getId,
                            (key1, key2) -> key1
                    ));

            // 5. 设置发送者ID
            Integer senderId = userMap.get(comment.getSender());
            if (senderId == null) {
                log.error("评论通知处理失败: 未找到发送者ID, sender={}", comment.getSender());
                return;
            }
            param.setSendUserId(senderId);

            // 6. 设置接收者ID列表
            Set<Integer> receiveUserIds = getNotifiedUserIds(comment, userMap);
            if (CollUtil.isEmpty(receiveUserIds)) {
                log.error("评论通知处理失败: 接收者ID列表为空, notifiedUsers={}", comment.getNotifiedUsers());
                return;
            }
            param.setReceiveUserIds(receiveUserIds);

            // 7. 发送飞书消息
            log.info("发送飞书通知: 发送者={}, 接收者数量={}, 标题={},发送参数={}",
                    comment.getSender(), receiveUserIds.size(), param.getTitle(), JSON.toJSONString(param));

            try {
                ResultTemplate<?> result = authorityRpc.sendFeishuMessage(param);
                log.info("飞书通知发送结果: {}", JSON.toJSONString(result));
                if (result != null && "1".equals(result.getCode()) && Boolean.TRUE.equals(result.getSuccess())) {
                    log.info("飞书通知发送成功: 业务类型={}, 业务ID={}",
                            comment.getBusinessType(), comment.getBusinessId());
                } else {
                    log.error("飞书通知发送失败: 响应结果={}", JSON.toJSONString(result));
                }
            } catch (Exception e) {
                log.error("飞书通知发送异常: {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("评论事件处理异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 构建飞书消息参数
     *
     * @param comment      评论信息
     * @param businessType 业务类型枚举
     * @return 飞书消息参数
     */
    private UserFeiShuMessageParam buildMessageParam(CommentCreateDTO comment, BusinessTypeEnum businessType) {
        UserFeiShuMessageParam param = new UserFeiShuMessageParam();

        // 设置应用编码
        param.setAppCode(APP_CODE);

        // 设置消息标题
        param.setTitle(businessType.getDesc() + "评论通知");

        // 设置消息内容
        param.setContent(comment.getContent());

        if (comment.getContent().length() > MAX_CONTENT_LENGTH) {
            param.setContent(comment.getContent().substring(0, MAX_CONTENT_LENGTH) + "...");
        }


        // 设置跳转链接
        String url = "";
        if (Objects.equals(comment.getBusinessType(), BusinessTypeEnum.BUILDING_RATING.getValue())) {
            // 楼宇评级详情页
            String type = comment.getBusinessId().contains("WS") ? "2" : "1";
            url = domain + String.format(BUILDING_RATING_URL, comment.getBusinessId(), type, comment.getVersion());
        } else if (Objects.equals(comment.getBusinessType(), BusinessTypeEnum.PRICE_APPLY.getValue())) {
            // 价格申请详情页
            url = domain + String.format(PRICE_APPLY_URL, comment.getBusinessId(), comment.getVersion());
        }
        param.setUrl(url);

        // 设置链接文本
        param.setUrlText(URL_TEXT);

        return param;
    }

    /**
     * 获取通知用户ID列表
     *
     * @param comment 评论信息
     * @param userMap 用户工号与ID映射
     * @return 通知用户ID集合
     */
    private Set<Integer> getNotifiedUserIds(CommentCreateDTO comment, Map<String, Integer> userMap) {
        if (CollUtil.isEmpty(comment.getNotifiedUsers()) || CollUtil.isEmpty(userMap)) {
            return Collections.emptySet();
        }

        return comment.getNotifiedUsers().stream()
                .filter(StringUtils::isNotBlank)
                .map(userMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

}
