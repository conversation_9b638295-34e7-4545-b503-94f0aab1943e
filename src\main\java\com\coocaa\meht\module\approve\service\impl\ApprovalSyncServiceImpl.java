package com.coocaa.meht.module.approve.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.meht.module.approve.adapter.ApprovalAdapter;
import com.coocaa.meht.module.approve.dao.ScreenApprovalInstanceMapper;
import com.coocaa.meht.module.approve.dto.ApprovalDetailVO;
import com.coocaa.meht.module.approve.entity.ScreenApprovalInstanceEntity;
import com.coocaa.meht.module.approve.enums.ApprovalResultEnum;
import com.coocaa.meht.module.approve.event.ApprovalEventPublisher;
import com.coocaa.meht.module.approve.event.ApprovalStatusChangeEvent;
import com.coocaa.meht.module.approve.exception.ApprovalSyncException;
import com.coocaa.meht.module.approve.service.ApprovalSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审批状态同步服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApprovalSyncServiceImpl implements ApprovalSyncService {

    private final ApprovalAdapter approvalAdapter;
    private final ScreenApprovalInstanceMapper instanceMapper;
    private final ApprovalEventPublisher eventPublisher;

    /**
     * 定时同步所有处理中的审批状态
     */
    @Override
    public void syncAllProcessingApprovals() {
        log.info("开始同步所有处理中的审批状态");

        try {
            // 查询所有处理中的审批实例
            LambdaQueryWrapper<ScreenApprovalInstanceEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ScreenApprovalInstanceEntity::getApproveStatus, ApprovalResultEnum.PROCESSING.getCode());
            List<ScreenApprovalInstanceEntity> instances = instanceMapper.selectList(queryWrapper);

            log.info("查询到{}个处理中的审批实例", instances.size());

            for (ScreenApprovalInstanceEntity instance : instances) {
                try {
                    if (instance != null && StringUtils.isNotBlank(instance.getInstanceCode())) {
                        syncApprovalStatus(instance.getInstanceCode());
                    }
                } catch (Exception e) {
                    log.error("同步审批状态异常, instanceCode: {}", instance.getInstanceCode(), e);
                    markSyncFailed(instance.getInstanceCode(), e.getMessage());
                }
            }

            log.info("所有处理中的审批状态同步完成");
        } catch (Exception e) {
            log.error("同步所有处理中的审批状态异常", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncApprovalStatus(String instanceCode) {
        if (StringUtils.isEmpty(instanceCode)) {
            log.warn("审批实例编码为空，无法同步");
            return false;
        }

        log.info("同步审批状态, instanceCode: {}", instanceCode);

        try {
            // 查询本地审批实例
            ScreenApprovalInstanceEntity instance = instanceMapper.selectByInstanceCode(instanceCode);
            if (instance == null) {
                log.warn("未找到审批实例, instanceCode: {}", instanceCode);
                return false;
            }

            // 查询审批中心详情
            ApprovalDetailVO detailVO = approvalAdapter.getDetail(instanceCode);
            if (detailVO == null) {
                log.warn("获取审批详情失败, instanceCode: {}", instanceCode);
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("同步审批状态异常, instanceCode: {}", instanceCode, e);
            throw new ApprovalSyncException(instanceCode, "同步审批状态异常: " + e.getMessage(), e);
        }
    }

    @Override
    public void markSyncFailed(String instanceCode, String errorMessage) {
        log.info("标记同步失败, instanceCode: {}, errorMessage: {}", instanceCode, errorMessage);

        // TODO: 实现标记同步失败的逻辑，可以记录到同步失败表中
    }

    @Override
    public void retryFailedSync() {
        log.info("开始重试同步失败的审批");

        // TODO: 实现重试同步失败的逻辑，从同步失败表中读取记录并重试

        log.info("重试同步失败的审批完成");
    }

    /**
     * 发布状态变更事件
     *
     * @param instance  审批实例
     * @param newStatus 新状态
     * @param newResult 新结果
     */
    private void publishStatusChangeEvent(ScreenApprovalInstanceEntity instance, String newStatus, String newResult) {
        ApprovalStatusChangeEvent event = ApprovalStatusChangeEvent.builder()
                .instanceCode(instance.getInstanceCode())
                .businessKey(instance.getBizCode())
                .approvalType(instance.getApprovalType())
                .status(newStatus)
                .result(newResult)
                .approvalTime(LocalDateTime.now())
                .version(instance.getVersion())
                .build();

        eventPublisher.publish(event);
    }
} 