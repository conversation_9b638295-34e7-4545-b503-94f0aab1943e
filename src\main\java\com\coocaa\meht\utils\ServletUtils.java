package com.coocaa.meht.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class ServletUtils {

    public static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        }
        return null;
    }

    public static HttpServletResponse getResponse() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            return ((ServletRequestAttributes) requestAttributes).getResponse();
        }
        return null;
    }

    public static String getParamToStr(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        if (parameterMap == null || parameterMap.isEmpty()) {
            return null;
        }
        List<String> paramList = new ArrayList<>();
        parameterMap.forEach((k, v) -> {
            try {
                boolean isPassword = "password".equals(k);
                for (String val : v) {
                    paramList.add(k + "=" + (isPassword ? "" : val));
                }
            } catch (Exception ignored) {
            }
        });
        return String.join("&", paramList);
    }

    public static String getBody(HttpServletRequest request) {
        if (request instanceof ContentCachingRequestWrapper) {
            ContentCachingRequestWrapper ccrw = (ContentCachingRequestWrapper) request;
            byte[] content = ccrw.getContentAsByteArray();
            if (content.length > 0) {
                try {
                    return new String(content, ccrw.getCharacterEncoding());
                } catch (UnsupportedEncodingException e) {
                    log.error("Http CharacterEncoding 错误", e);
                }
            }
        }
        return null;
    }

    public static void setAttr(String name, Object o) {
        HttpServletRequest request = getRequest();
        if (request != null) {
            request.setAttribute(name, o);
        }
    }

    public static Object getAttr(String name) {
        HttpServletRequest request = getRequest();
        if (request != null) {
            return request.getAttribute(name);
        }
        return null;
    }

    public static <T> T getAttr(String name, Class<T> claz) {
        HttpServletRequest request = getRequest();
        if (request != null) {
            Object attribute = request.getAttribute(name);
            if (attribute != null) {
                return claz.cast(attribute);
            }
        }
        return null;
    }

    public static String getOrigin() {
        HttpServletRequest request = getRequest();
        if (request != null) {
            return request.getHeader(HttpHeaders.ORIGIN);
        }
        return null;
    }

    public static void respResult(HttpServletResponse response, Object result) {
        try {
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/json");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Allow-Origin", ServletUtils.getOrigin());
            PrintWriter writer = response.getWriter();
            writer.println(JsonUtils.toJson(result));
            writer.flush();
        } catch (IOException e) {
            log.error("输出response异常", e);
        }
    }

}