package com.coocaa.meht.rpc.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BuildingTopEntity implements Serializable {


    private Long id;

    /**
     * 楼宇名称
     */
    private String buildingName;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * TOP等级
     */
    private String topLevel;

    /**
     * 楼宇类型
     */
    private String buildingType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


}
