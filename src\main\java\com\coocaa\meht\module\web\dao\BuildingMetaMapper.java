package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaImgRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Mapper
public interface BuildingMetaMapper extends BaseMapper<BuildingMetaEntity> {

    List<BuildingMetaImgRelationEntity> metaPic(@Param("mapNo") String mapNo, @Param("imgType") List<Integer> imgType);
}
