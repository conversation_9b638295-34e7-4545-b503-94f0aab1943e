package com.coocaa.meht.module.web.service;

import com.coocaa.meht.module.web.entity.PriceApplyDeviceEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 价格申请设备明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
public interface PriceApplyDeviceService extends IService<PriceApplyDeviceEntity> {


    /**
     * 通过申请申请记录id 获取设备信息
     * @param priceApplyId
     * @return
     */
    List<PriceApplyDeviceEntity> findByPriceApplyId(Integer priceApplyId);


    /**
     * 通过申请申请记录id 获取设备信息
     * @param priceApplyIdList
     * @return
     */
    Map<Integer,List<PriceApplyDeviceEntity>> findByPriceApplyIdMap(List<Integer> priceApplyIdList);

    /**
     *通过申请编码查点位
     */
    List<String> getPointByApplyNumber(String applyNumber);
}
