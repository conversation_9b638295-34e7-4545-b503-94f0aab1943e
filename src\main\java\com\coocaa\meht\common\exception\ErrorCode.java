package com.coocaa.meht.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ErrorCode {

    SUCCESS(200, "success"),
    ERROR(999, "服务器异常，请稍后再试"),
    UNAUTHORIZED(401, "未登录"),
    NO_PERMISSION(403, "没有权限，禁止访问"),
    METHOD_ERROR(405, "请求方式错误"),
    INTERNAL_SERVER_ERROR(500, "服务器内部异常"),
    LOGIN_TIME_OUT(10001, "登录过期");

    private final int code;
    private final String msg;

}